package com.yhl.scp.mps.coating.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.coating.dto.MpsCoatingChangeTimeDTO;
import com.yhl.scp.mps.coating.vo.MpsCoatingChangeTimeVO;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <code>MpsCoatingChangeTimeService</code>
 * <p>
 * 镀膜切换时间应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-28 20:37:23
 */
public interface MpsCoatingChangeTimeService extends BaseService<MpsCoatingChangeTimeDTO, MpsCoatingChangeTimeVO> {

    /**
     * 查询所有
     *
     * @return list {@link MpsCoatingChangeTimeVO}
     */
    List<MpsCoatingChangeTimeVO> selectAll();

    void loseEffectivenessBatch(List<String> ids);
    void doDeleteByVersion(List<Map<String, Object>> versionDTOList);
    void export(HttpServletResponse response);

    /**
     * 删除
     *
     * @param ids 主键ID
     * @return int
     */
    @Override
    int doDelete(List<String> ids);
    @Override
    BaseResponse<Void> doCreate(@RequestBody MpsCoatingChangeTimeDTO mpsCoatingChangeTimeDTO);
    @Override
    BaseResponse<Void> doUpdate(@RequestBody MpsCoatingChangeTimeDTO mpsCoatingChangeTimeDTO);

}
