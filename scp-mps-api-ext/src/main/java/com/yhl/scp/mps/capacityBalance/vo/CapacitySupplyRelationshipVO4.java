package com.yhl.scp.mps.capacityBalance.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>CapacitySupplyRelationshipVO4</code>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-24 14:41:26
 */
@Data
public class CapacitySupplyRelationshipVO4 implements Serializable {


    private static final long serialVersionUID = -2399817019246794991L;


    @ApiModelProperty(value = "版本号")
    @FieldInterpretation(value = "版本号")
    private String versionCode;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @FieldInterpretation(value = "需求数量")
    private BigDecimal demandQuantity;

    /**
     * 供应数量
     */
    @ApiModelProperty(value = "供应数量")
    @FieldInterpretation(value = "供应数量")
    private String supplyQuantity;

    /**
     * 供应方式（本厂/委外）
     */
    @ApiModelProperty(value = "供应方式（本厂/委外）")
    @FieldInterpretation(value = "供应方式（本厂/委外）")
    private String supplyModel;



}
