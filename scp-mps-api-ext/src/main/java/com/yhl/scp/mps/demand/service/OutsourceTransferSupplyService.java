package com.yhl.scp.mps.demand.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.demand.dto.OutsourceTransferSupplyDTO;
import com.yhl.scp.mps.demand.vo.OutsourceTransferSupplyVO;

import java.util.List;

/**
 * <code>OutsourceTransferSupplyService</code>
 * <p>
 * 委外转产汇总供料时间应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-23 17:43:15
 */
public interface OutsourceTransferSupplyService extends BaseService<OutsourceTransferSupplyDTO, OutsourceTransferSupplyVO> {

    /**
     * 查询所有
     *
     * @return list {@link OutsourceTransferSupplyVO}
     */
    List<OutsourceTransferSupplyVO> selectAll();

    /**
     * 材料需求计算
     * @param outsourceTransferSupplyDTO
     * @return
     */
	BaseResponse<Void> doMaterialSupplyCalculate(OutsourceTransferSupplyDTO outsourceTransferSupplyDTO);

	void deleteBySummaryIds(List<String> summaryIds);

}
