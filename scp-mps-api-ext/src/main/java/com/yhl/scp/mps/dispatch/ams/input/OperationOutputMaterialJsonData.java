package com.yhl.scp.mps.dispatch.ams.input;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OperationOutputMaterialJsonData {

    /**
     * 输入物品id
     */
    private String productId;

    private String minTimeConstraint;

    private Boolean mainProduct;

    private BigDecimal quantity;

}
