package com.yhl.scp.mps.productDemandMonitor.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mps.productDemandMonitor.dto.MpsProProductDemandMonitorDTO;
import com.yhl.scp.mps.productDemandMonitor.vo.MpsProProductDemandMonitorVO;

import java.util.List;

/**
 * <code>MpsProProductDemandMonitorService</code>
 * <p>
 * 生产齐套生产需求应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-14 11:04:54
 */
public interface MpsProProductDemandMonitorService extends BaseService<MpsProProductDemandMonitorDTO, MpsProProductDemandMonitorVO> {

    /**
     * 查询所有
     *
     * @return list {@link MpsProProductDemandMonitorVO}
     */
    List<MpsProProductDemandMonitorVO> selectAll();

    void doInsertBatch();

}
