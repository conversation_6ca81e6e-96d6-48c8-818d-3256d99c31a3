package com.yhl.scp.mps.manualAdjust.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 手动调整参数(source)
 *
 * <AUTHOR>
 */
@Data
@ApiModel("手动编辑源信息")
public class ManualAdjustSource {

    @ApiModelProperty("资源ID")
    private String resourceId;

    @ApiModelProperty("工序ID")
    private String operationId;

    @ApiModelProperty(value = "位置", allowableValues = "BEFORE,AFTER")
    private String position;
}
