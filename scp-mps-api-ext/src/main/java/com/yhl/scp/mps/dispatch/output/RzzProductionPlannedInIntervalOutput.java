package com.yhl.scp.mps.dispatch.output;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName RzzProductionPlannedInIntervalOutput
 * @Description TODO
 * @Date 2024-12-07 14:26:40
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Data
@ApiModel("生产批量与生产计划量对应关系输出结果")
public class RzzProductionPlannedInIntervalOutput {

    @ApiModelProperty("生产批量id")
    private String productionIntervalId;

    @ApiModelProperty("算法自建生产计划量id")
    private String productionPlannedId;

    @ApiModelProperty("算法自建计划量输出物品id")
    private String productionPlannedOutputId;
}
