package com.yhl.scp.mps.plan.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MasterPlanPublishedLogVO</code>
 * <p>
 * 主生产计划发布日志表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-03 15:37:52
 */
@ApiModel(value = "主生产计划发布日志表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MasterPlanPublishedLogVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -89370665984861477L;

    /**
     * 发布人ID
     */
    @ApiModelProperty(value = "发布人ID")
    @FieldInterpretation(value = "发布人ID")
    private String publishedOperatorId;
    /**
     * 发布人
     */
    @ApiModelProperty(value = "发布人")
    @FieldInterpretation(value = "发布人")
    private String publishedOperator;
    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @FieldInterpretation(value = "发布时间")
    private Date publishedTime;
    /**
     * 发布状态
     */
    @ApiModelProperty(value = "发布状态")
    @FieldInterpretation(value = "发布状态")
    private String publishedStatus;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;

    @Override
    public void clean() {

    }

}
