package com.yhl.scp.mps.plan.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>OperationDeleteLogDTO</code>
 * <p>
 * OperationDeleteLogDTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-10 20:19:26
 */
@ApiModel(value = "工序DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OperationDeleteLogDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -29750266299354893L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 工序代码
     */
    @ApiModelProperty(value = "工序代码")
    private String operationCode;
    /**
     * 制造订单ID
     */
    @ApiModelProperty(value = "制造订单ID")
    private String orderId;
    /**
     * 计划批次ID
     */
    @ApiModelProperty(value = "计划批次ID")
    private String planUnitId;
    /**
     * 路径步骤ID
     */
    @ApiModelProperty(value = "路径步骤ID")
    private String routingStepId;
    /**
     * 路径步骤顺序号
     */
    @ApiModelProperty(value = "路径步骤顺序号")
    private Integer routingStepSequenceNo;
    /**
     * 前工序顺序号
     */
    @ApiModelProperty(value = "前工序顺序号")
    private String preRoutingStepSequenceNo;
    /**
     * 后工序顺序号
     */
    @ApiModelProperty(value = "后工序顺序号")
    private String nextRoutingStepSequenceNo;
    /**
     * 库存点物品ID
     */
    @ApiModelProperty(value = "库存点物品ID")
    private String productStockPointId;
    /**
     * 物品ID
     */
    @ApiModelProperty(value = "物品ID")
    private String productId;
    /**
     * 库存点ID
     */
    @ApiModelProperty(value = "库存点ID")
    private String stockPointId;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;
    /**
     * 计划资源ID
     */
    @ApiModelProperty(value = "计划资源ID")
    private String plannedResourceId;
    /**
     * 是否冻结
     */
    @ApiModelProperty(value = "是否冻结")
    private String frozen;
    /**
     * 计划状态
     */
    @ApiModelProperty(value = "计划状态")
    private String planStatus;
    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    private String orderType;
    /**
     * 齐套状态
     */
    @ApiModelProperty(value = "齐套状态")
    private String kitStatus;
    /**
     * 处理时间
     */
    @ApiModelProperty(value = "处理时间")
    private Integer processingTime;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    /**
     * 最早开始时间
     */
    @ApiModelProperty(value = "最早开始时间")
    private Date earliestStartTime;
    /**
     * 最晚结束时间
     */
    @ApiModelProperty(value = "最晚结束时间")
    private Date latestEndTime;
    /**
     * 计算的最早开始时间
     */
    @ApiModelProperty(value = "计算的最早开始时间")
    private Date calcEarliestStartTime;
    /**
     * 计算的最晚结束时间
     */
    @ApiModelProperty(value = "计算的最晚结束时间")
    private Date calcLatestEndTime;
    /**
     * 接续任务
     */
    @ApiModelProperty(value = "接续任务")
    private String connectionTask;
    /**
     * 接续方式
     */
    @ApiModelProperty(value = "接续方式")
    private String connectionType;
    /**
     * 最大接续时间
     */
    @ApiModelProperty(value = "最大接续时间")
    private Integer maxConnectionDuration;
    /**
     * 最小接续时间
     */
    @ApiModelProperty(value = "最小接续时间")
    private Integer minConnectionDuration;
    /**
     * 分割类别
     */
    @ApiModelProperty(value = "分割类别")
    private String partitionType;
    /**
     * 资源固定标志
     */
    @ApiModelProperty(value = "资源固定标志")
    private String resourceFixed;
    /**
     * 时间固定标志
     */
    @ApiModelProperty(value = "时间固定标志")
    private String timeFixed;
    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    private String lockStatus;
    /**
     * 延期原因
     */
    @ApiModelProperty(value = "延期原因")
    private String delayReason;
    /**
     * 父工序ID
     */
    @ApiModelProperty(value = "父工序ID")
    private String parentId;
    /**
     * 工序顺序
     */
    @ApiModelProperty(value = "工序顺序")
    private Integer operationIndex;
    /**
     * 未排程原因
     */
    @ApiModelProperty(value = "未排程原因")
    private String unscheduledReason;
    /**
     * 是否上次插单
     */
    @ApiModelProperty(value = "是否上次插单")
    private String lastInsertion;
    /**
     * 标准工艺ID
     */
    @ApiModelProperty(value = "标准工艺ID")
    private String standardStepId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
    /**
     * 删除原因
     */
    @ApiModelProperty(value = "删除原因")
    private String deleteReason;

}
