package com.yhl.scp.mps.plan.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MasterPlanWorkOrderVO {

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 动态表头
     */
    private List<String> dynamicHeader;

    /**
     * 动态内容
     */
    private List<MasterPlanWorkOrderBodyVO> bodyList;

}
