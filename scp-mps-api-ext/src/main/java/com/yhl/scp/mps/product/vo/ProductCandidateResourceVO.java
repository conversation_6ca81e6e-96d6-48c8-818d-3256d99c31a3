package com.yhl.scp.mps.product.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>ProductCandidateResourceVO</code>
 * <p>
 * 产品资源生产关系表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 10:16:47
 */
@ApiModel(value = "产品资源生产关系表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProductCandidateResourceVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 745542280392827909L;

    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    @FieldInterpretation(value = "库存点代码")
    private String stockPointCode;
    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    @FieldInterpretation(value = "库存点名称")
    private String stockPointName;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productCode;
    /**
     * 零件名称
     */
    @ApiModelProperty(value = "零件名称")
    @FieldInterpretation(value = "零件名称")
    private String partName;
    /**
     * 工序代码
     */
    @ApiModelProperty(value = "工序代码")
    @FieldInterpretation(value = "工序代码")
    private String operationCode;
    /**
     * 工序名称
     */
    @ApiModelProperty(value = "工序名称")
    @FieldInterpretation(value = "工序名称")
    private String operationName;
    /**
     * 资源代码
     */
    @ApiModelProperty(value = "资源代码")
    @FieldInterpretation(value = "资源代码")
    private String resourceCode;
    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    @FieldInterpretation(value = "资源名称")
    private String resourceName;
    /**
     * 资源类型
     */
    @ApiModelProperty(value = "资源类型")
    @FieldInterpretation(value = "资源类型")
    private String resourceType;
    /**
     * 生产线组
     */
    @ApiModelProperty(value = "生产线组")
    @FieldInterpretation(value = "生产线组")
    private String lineGroup;
    /**
     * 节拍（秒/片）
     */
    @ApiModelProperty(value = "节拍（秒/片）")
    @FieldInterpretation(value = "节拍（秒/片）")
    private Double beat;

    @Override
    public void clean() {

    }

}
