package com.yhl.scp.mps.util;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * <code>LabelValueThree</code>
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-07 14:47:34
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LabelValueThree<T> implements Serializable {

    private static final long serialVersionUID = -218648835993328151L;

    private String value1;

    private T value2;

    private String value3 ;

    public LabelValueThree(String value1) {
        super();
        this.value1 = value1;
    }

    public static List<LabelValueThree> initWithoutSelect() {
        return new LinkedList<>();
    }

}
