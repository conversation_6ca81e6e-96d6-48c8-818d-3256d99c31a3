package com.yhl.scp.mps.operationPublished.service;

import java.util.List;
import java.util.Map;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mps.operationPublished.dto.FulfillmentPublishedDTO;
import com.yhl.scp.mps.operationPublished.vo.CopyPublishedStatusVO;
import com.yhl.scp.mps.operationPublished.vo.FulfillmentPublishedVO;

/**
 * <code>FulfillmentPublishedService</code>
 * <p>
 * 分配关系发布信息表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 10:05:14
 */
public interface FulfillmentPublishedService extends BaseService<FulfillmentPublishedDTO, FulfillmentPublishedVO> {

    /**
     * 查询所有
     *
     * @return list {@link FulfillmentPublishedVO}
     */
    List<FulfillmentPublishedVO> selectAll();

    Boolean disposeSnapshotData(String oldPublishedLogId, String bindPublishedLogId, String publishedTime, Boolean copyFlag);

	CopyPublishedStatusVO doSnapshotDataForLineGroup(List<String> workOrderIds, List<String> publishedLogIds,
			Map<String, String> productLineGroupMap);

}
