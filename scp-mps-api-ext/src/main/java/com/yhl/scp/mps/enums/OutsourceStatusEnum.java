package com.yhl.scp.mps.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <code>OutsourceStatusEnum</code>
 * <p>
 * 委外状态枚举
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 10:39:05
 */
public enum OutsourceStatusEnum implements CommonEnum {

    ISSUED("ISSUED","已下发"),
    UNISSUED( "UNISSUED","未下发");


    private String code;
    private String desc;

    OutsourceStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}

