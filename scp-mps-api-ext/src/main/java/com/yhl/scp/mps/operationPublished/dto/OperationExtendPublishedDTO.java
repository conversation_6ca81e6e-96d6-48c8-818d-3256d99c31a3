package com.yhl.scp.mps.operationPublished.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import com.yhl.scp.mps.plan.dto.DeliveryChangeRecordDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>OperationExtendPublishedDTO</code>
 * <p>
 * 工序扩展发布信息表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 09:37:31
 */
@ApiModel(value = "工序扩展发布信息表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OperationExtendPublishedDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 484366602478945576L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String newId;
    
    /**
     * 源数据id
     */
    @ApiModelProperty(value = "源数据id")
    private String id;
    
    /**
     * 工序ID
     */
    @ApiModelProperty(value = "工序ID")
    private String operationId;
    /**
     * 前工序IDS
     */
    @ApiModelProperty(value = "前工序IDS")
    private String preOperationIds;
    /**
     * 资源IDS
     */
    @ApiModelProperty(value = "资源IDS")
    private String resourceIds;
    /**
     * 资源固定标志
     */
    @ApiModelProperty(value = "资源固定标志")
    private String resourceFixed;
    /**
     * 时间固定标志
     */
    @ApiModelProperty(value = "时间固定标志")
    private String timeFixed;
    /**
     * 后工序IDS
     */
    @ApiModelProperty(value = "后工序IDS")
    private String nextOperationIds;
    /**
     * 用户指定制造时间
     */
    @ApiModelProperty(value = "用户指定制造时间")
    private Date appointProductionTime;
    /**
     * 用户指定数量
     */
    @ApiModelProperty(value = "用户指定数量")
    private Integer appointQuantity;
    /**
     * 用户指定主资源
     */
    @ApiModelProperty(value = "用户指定主资源")
    private String appointMainResourceId;
    /**
     * 指定开始时刻
     */
    @ApiModelProperty(value = "指定开始时刻")
    private Date appointStartTime;
    /**
     * 指定结束时刻
     */
    @ApiModelProperty(value = "指定结束时刻")
    private Date appointEndTime;
    /**
     * 分割比率
     */
    @ApiModelProperty(value = "分割比率")
    private BigDecimal partitionRate;
    /**
     * 切割数
     */
    @ApiModelProperty(value = "切割数")
    private Integer partitionNum;
    /**
     * 分割批量
     */
    @ApiModelProperty(value = "分割批量")
    private BigDecimal partitionBatch;
    /**
     * 分割类别
     */
    @ApiModelProperty(value = "分割类别")
    private String partitionType;
    /**
     * 最大分割批量
     */
	@ApiModelProperty(value = "最大分割批量")
    private BigDecimal maxPartitionBatch;
    /**
     * 最小分割批量
     */
    @ApiModelProperty(value = "最小分割批量")
    private BigDecimal minPartitionBatch;
    /**
     * 分割批量单位
     */
    @ApiModelProperty(value = "分割批量单位")
    private String partitionBatchUnit;
    /**
     * 分割尾数处理
     */
    @ApiModelProperty(value = "分割尾数处理")
    private String partitionMantissaDeal;
	/**
     * 计划主资源ID
     */
    @ApiModelProperty(value = "计划主资源ID")
    private String plannedMainResourceId;
    /**
     * 上次计划主资源ID
     */
    @ApiModelProperty(value = "上次计划主资源ID")
    private String lastMainResourceId;
    /**
     * 计划工具资源ID
     */
    @ApiModelProperty(value = "计划工具资源ID")
    private String plannedToolResourceId;
    /**
     * 上次计划工具资源ID
     */
    @ApiModelProperty(value = "上次计划工具资源ID")
    private String lastToolResourceId;
    /**
     * 计划技能ID
     */
    @ApiModelProperty(value = "计划技能ID")
    private String plannedSkillId;
    /**
     * 上次计划技能ID
     */
    @ApiModelProperty(value = "上次计划技能ID")
    private String lastSkillId;
    /**
     * 设置开始时刻
     */
    @ApiModelProperty(value = "设置开始时刻")
    private Date setupStartTime;
    /**
     * 上次设置开始时刻
     */
    @ApiModelProperty(value = "上次设置开始时刻")
    private Date lastSetupStartTime;
    /**
     * 设置结束时刻
     */
    @ApiModelProperty(value = "设置结束时刻")
    private Date setupEndTime;
    /**
     * 设置结束时刻
     */
    @ApiModelProperty(value = "设置结束时刻")
    private Date lastSetupEndTime;
    /**
     * 设置时间
     */
    @ApiModelProperty(value = "设置时间")
    private Integer setupDuration;
    /**
     * 制造开始时刻
     */
    @ApiModelProperty(value = "制造开始时刻")
    private Date productionStartTime;
    /**
     * 上次制造开始时刻
     */
    @ApiModelProperty(value = "上次制造开始时刻")
    private Date lastProductionStartTime;
	/**
     * 制造结束时刻
     */
    @ApiModelProperty(value = "制造结束时刻")
    private Date productionEndTime;
    /**
     * 上次制造结束时刻
     */
    @ApiModelProperty(value = "上次制造结束时刻")
    private Date lastProductionEndTime;
    /**
     * 锁定开始时刻
     */
    @ApiModelProperty(value = "锁定开始时刻")
    private Date lockStartTime;
    /**
     * 上次锁定开始时刻
     */
    @ApiModelProperty(value = "上次锁定开始时刻")
    private Date lastLockStartTime;
    /**
     * 锁定结束时刻
     */
    @ApiModelProperty(value = "锁定结束时刻")
    private Date lockEndTime;
    /**
     * 上次锁定结束时刻
     */
    @ApiModelProperty(value = "上次锁定结束时刻")
    private Date lastLockEndTime;
    /**
     * 锁定时间
     */
    @ApiModelProperty(value = "锁定时间")
    private Integer lockDuration;
    /**
     * 清洗开始时刻
     */
    @ApiModelProperty(value = "清洗开始时刻")
    private Date cleanupStartTime;
    /**
     * 上次清洗开始时刻
     */
    @ApiModelProperty(value = "上次清洗开始时刻")
    private Date lastCleanupStartTime;
    /**
     * 清洗结束时刻
     */
    @ApiModelProperty(value = "清洗结束时刻")
    private Date cleanupEndTime;
    /**
     * 上次清洗结束时刻
     */
    @ApiModelProperty(value = "上次清洗结束时刻")
    private Date lastCleanupEndTime;
    /**
     * 清洗时间
     */
    @ApiModelProperty(value = "清洗时间")
    private Integer cleanupDuration;
    /**
     * 投料结束时间
     */
    @ApiModelProperty(value = "投料结束时间")
    private Date feedFinishTime;
    /**
     * 工序类型
     */
    @ApiModelProperty(value = "工序类型")
    private String operationType;
    /**
     * 延期原因
     */
    @ApiModelProperty(value = "延期原因")
    private String delayReason;
    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    private String lockStatus;
    /**
     * 未排原因
     */
    @ApiModelProperty(value = "未排原因")
    private String unscheduledReason;
    /**
     * 是否上次插单
     */
    @ApiModelProperty(value = "是否上次插单")
    private String lastInsertion;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    
    /**
     * 主生产计划发布日志id
     */
    @ApiModelProperty(value = "主生产计划发布日志id")
    private String publishedLogId;

}
