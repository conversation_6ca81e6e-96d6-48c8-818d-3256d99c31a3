package com.yhl.scp.mps.productionLot.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mps.productionLot.dto.ProductionLotDTO;
import com.yhl.scp.mps.productionLot.vo.ProductionLotVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>ProductionLotService</code>
 * <p>
 * 生产经济批量应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-14 15:23:20
 */
public interface ProductionLotService extends BaseService<ProductionLotDTO, ProductionLotVO> {

    /**
     * 查询所有
     *
     * @return list {@link ProductionLotVO}
     */
    List<ProductionLotVO> selectAll();


    /**
     * 导出
     * @param response
     */
    void export(HttpServletResponse response);

}
