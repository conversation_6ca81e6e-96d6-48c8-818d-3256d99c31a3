package com.yhl.scp.mps.demand.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mps.demand.dto.DemandPersonalDTO;
import com.yhl.scp.mps.demand.vo.DemandPersonalVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>DemandPersonalService</code>
 * <p>
 * 需求应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20 13:56:57
 */
public interface DemandPersonalService extends BaseService<DemandPersonalDTO, DemandPersonalVO> {

    /**
     * 查询所有
     *
     * @return list {@link DemandPersonalVO}
     */
    List<DemandPersonalVO> selectAll();

    void export(String ids, HttpServletResponse response);

}
