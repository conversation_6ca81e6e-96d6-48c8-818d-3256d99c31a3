<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yhl.scp</groupId>
        <artifactId>bpim</artifactId>
        <version>1.0.0</version>
    </parent>
    <artifactId>scp-mps-api-ext</artifactId>
    <name>scp-mps-api-ext</name>
    <url>https://rzz.com</url>
    <description>MPS API模块</description>

    <dependencies>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-biz-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-mps-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-mds-api-ext</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-mps-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-sds-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-dcp-api-ext</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-dfp-api-ext</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-ams-infra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-ams-infra-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-ams-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.algorithm</groupId>
            <artifactId>rzz-java-service</artifactId>
            <exclusions>
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.boot</groupId>-->
<!--                    <artifactId>spring-boot-starter-aop</artifactId>-->
<!--                </exclusion>-->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <build>
        <finalName>scp-mps-api-ext</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>${maven.assembly.plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok.mapstruct.binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>