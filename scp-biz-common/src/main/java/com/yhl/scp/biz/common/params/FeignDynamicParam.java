package com.yhl.scp.biz.common.params;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <code>FeignDynamicParam</code>
 * <p>
 * FeignDynamicParam
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/11/20 12:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FeignDynamicParam {

    /**
     * 动态字段
     */
    private List<String> dynamicColumnParam;

    /**
     * 查询参数
     */
    private Map<String, Object> queryParam;

}
