package com.yhl.scp.biz.common.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <code>RangeMergeStrategy</code>
 * <p>
 * RangeMergeStrategy
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19 15:31:02
 */
@Getter
@Setter
@Slf4j
public class RangeMergeStrategy extends AbstractMergeStrategy {

    private List<CellRangeAddress> mergeRegions;

    private Set<String> uniqueKeys = new HashSet<>();

    public RangeMergeStrategy(List<CellRangeAddress> mergeRegions) {
        this.mergeRegions = mergeRegions;
    }

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
        for (CellRangeAddress mergeRegion : this.mergeRegions) {
            int firstRow = mergeRegion.getFirstRow();
            int lastRow = mergeRegion.getLastRow();
            int firstCol = mergeRegion.getFirstColumn();
            int lastCol = mergeRegion.getLastColumn();
            String uniqueKey = firstRow + "#" + lastRow + "#" + firstCol + "#" + lastCol;
            if (!this.uniqueKeys.contains(uniqueKey)) {
                uniqueKeys.add(uniqueKey);
                // 若不存在重叠，则进行合并
                CellRangeAddress cellRangeAddress = new CellRangeAddress(firstRow, lastRow, firstCol, lastCol);
                sheet.addMergedRegion(cellRangeAddress);
            }
        }
    }

}