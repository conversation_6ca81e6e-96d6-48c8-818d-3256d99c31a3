package com.yhl.scp.biz.common.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <code>ModuleCodeEnum</code>
 * <p>
 * ModuleCodeEnum
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/5 11:49
 */
public enum ModuleCodeEnum implements CommonEnum {
    UNION("UNION", "UNION"),
    MPS("MPS", "MPS"),
    AMS("AMS", "AMS"),
    DFP("DFP", "DFP"),
    CAPACITY("CAPACITY", "产能平衡"),
    FEEDBACK("FEEDBACK", "生产反馈"),
    DELAY_ADJUST("DELAY_ADJUST", "延期调整建议"),
    ;
    private String code;
    private String desc;

    ModuleCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

}
