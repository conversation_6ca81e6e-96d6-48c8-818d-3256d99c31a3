package com.yhl.scp.dfp.oem.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.oem.dto.OemStockPointMapDTO;
import com.yhl.scp.dfp.oem.service.OemStockPointMapService;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>OemStockPointMapController</code>
 * <p>
 * 主机厂库存点关联关系控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:48:08
 */
@Slf4j
@Api(tags = "主机厂库存点关联关系控制器")
@RestController
@RequestMapping("oemStockPointMap")
public class OemStockPointMapController extends BaseController {

    @Resource
    private OemStockPointMapService oemStockPointMapService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<OemStockPointMapVO>> page() {
        List<OemStockPointMapVO> oemStockPointMapList = oemStockPointMapService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<OemStockPointMapVO> pageInfo = new PageInfo<>(oemStockPointMapList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody OemStockPointMapDTO oemStockPointMapDTO) {
        return oemStockPointMapService.doCreate(oemStockPointMapDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody OemStockPointMapDTO oemStockPointMapDTO) {
        return oemStockPointMapService.doUpdate(oemStockPointMapDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<RemoveVersionDTO> removeVersionDTOS) {
        oemStockPointMapService.doDeleteByVersion(removeVersionDTOS);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<OemStockPointMapVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, oemStockPointMapService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "根据库存点编码查询主机厂编码映射")
    @GetMapping(value = "selectOemCodetByStockPoinCode")
    public BaseResponse<List<LabelValue<String>>> selectOemCodetByStockPoinCode(@RequestParam(name = "stockPointCode") String stockPointCode) {
        return BaseResponse.success(oemStockPointMapService.selectOemCodetByStockPoinCode(stockPointCode));
    }
}
