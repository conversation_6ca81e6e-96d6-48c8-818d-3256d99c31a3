package com.yhl.scp.dfp.delivery.domain.factory;

import com.yhl.scp.dfp.delivery.domain.entity.DeliveryPlanDO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanDTO;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>DeliveryPlanFactory</code>
 * <p>
 * 发货计划表领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 13:51:24
 */
@Component
public class DeliveryPlanFactory {

    @Resource
    private DeliveryPlanDao deliveryPlanDao;

    DeliveryPlanDO create(DeliveryPlanDTO dto) {
        // TODO
        return null;
    }

}
