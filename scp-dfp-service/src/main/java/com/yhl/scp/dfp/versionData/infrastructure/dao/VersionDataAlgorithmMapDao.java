package com.yhl.scp.dfp.versionData.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.versionData.infrastructure.po.VersionDataAlgorithmMapPO;
import com.yhl.scp.dfp.versionData.vo.VersionDataAlgorithmMapVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <code>VersionDataAlgorithmMapDao</code>
 * <p>
 * 预测算法版本数据映射关系DAO
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 17:24:23
 */
public interface VersionDataAlgorithmMapDao extends BaseDao<VersionDataAlgorithmMapPO, VersionDataAlgorithmMapVO> {

    /**
     * 根据版本批量删除
     * @param versionDTOList
     * @return
     */
    int deleteBatchVersion(@Param("list") List<RemoveVersionDTO> versionDTOList);

}
