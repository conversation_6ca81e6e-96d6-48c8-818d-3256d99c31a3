package com.yhl.scp.dfp.calendar.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>CalendarRulePO</code>
 * <p>
 * 日历规则PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-07 18:13:42
 */
public class CalendarRulePO extends BasePO implements Serializable {

    private static final long serialVersionUID = -91586906008722072L;

    /**
     * 生产组织ID
     */
    private String organizationId;
    /**
     * 标准资源ids
     */
    private String standardResourceIds;
    /**
     * 物理资源ids
     */
    private String physicalResourceIds;
    /**
     * 班次ID
     */
    private String shiftIds;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 重复期间
     */
    private String repeatFrequency;
    /**
     * 周期模板
     */
    private String frequencyPattern;
    /**
     * 正班工时
     */
    private BigDecimal workHours;
    /**
     * 加班工时
     */
    private BigDecimal overtimeHours;
    /**
     * 异常日历类型
     */
    private String calendarType;
    /**
     * 日历规则类型
     */
    private String calendarRuleType;
    /**
     * 效率
     */
    private BigDecimal efficiency;
    /**
     * 资源量
     */
    private Integer resourceQuantity;
    /**
     * 开始日
     */
    private Date startDate;
    /**
     * 结束日
     */
    private Date endDate;
    /**
     * 主机厂编码
     */
    private String oemCode;

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public String getShiftIds() {
        return shiftIds;
    }

    public void setShiftIds(String shiftIds) {
        this.shiftIds = shiftIds;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getRepeatFrequency() {
        return repeatFrequency;
    }

    public void setRepeatFrequency(String repeatFrequency) {
        this.repeatFrequency = repeatFrequency;
    }

    public String getFrequencyPattern() {
        return frequencyPattern;
    }

    public void setFrequencyPattern(String frequencyPattern) {
        this.frequencyPattern = frequencyPattern;
    }

    public BigDecimal getWorkHours() {
        return workHours;
    }

    public void setWorkHours(BigDecimal workHours) {
        this.workHours = workHours;
    }

    public BigDecimal getOvertimeHours() {
        return overtimeHours;
    }

    public void setOvertimeHours(BigDecimal overtimeHours) {
        this.overtimeHours = overtimeHours;
    }

    public String getCalendarType() {
        return calendarType;
    }

    public void setCalendarType(String calendarType) {
        this.calendarType = calendarType;
    }

    public String getCalendarRuleType() {
        return calendarRuleType;
    }

    public void setCalendarRuleType(String calendarRuleType) {
        this.calendarRuleType = calendarRuleType;
    }

    public BigDecimal getEfficiency() {
        return efficiency;
    }

    public void setEfficiency(BigDecimal efficiency) {
        this.efficiency = efficiency;
    }

    public Integer getResourceQuantity() {
        return resourceQuantity;
    }

    public void setResourceQuantity(Integer resourceQuantity) {
        this.resourceQuantity = resourceQuantity;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }


    public String getStandardResourceIds() {
        return standardResourceIds;
    }

    public void setStandardResourceIds(String standardResourceIds) {
        this.standardResourceIds = standardResourceIds;
    }

    public String getPhysicalResourceIds() {
        return physicalResourceIds;
    }

    public void setPhysicalResourceIds(String physicalResourceIds) {
        this.physicalResourceIds = physicalResourceIds;
    }
}
