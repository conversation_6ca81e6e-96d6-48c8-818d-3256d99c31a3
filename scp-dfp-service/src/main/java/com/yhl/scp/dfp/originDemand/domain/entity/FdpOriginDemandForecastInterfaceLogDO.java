package com.yhl.scp.dfp.originDemand.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>FdpOriginDemandForecastInterfaceLogDO</code>
 * <p>
 * Edi装车需求GRP接口同步记录表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-04 18:26:21
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class FdpOriginDemandForecastInterfaceLogDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -81043638416788584L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 预测集
     */
    private String forecastSet;
    private String ediLocation;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 收货地址
     */
    private String siteAddress1;
    /**
     * 本厂编号
     */
    private String itemNum;
    /**
     * 库存组织ID
     */
    private String orgId;
    /**
     * 数量
     */
    private BigDecimal qty;
    /**
     * 计划类型
     */
    private String scheduleType;
    /**
     * 原始需求日期从
     */
    private Date originalSchStDate;
    /**
     * 原始需求日期至
     */
    private Date originalSchEndDate;
    /**
     * 报文原始发货时间
     */
    private Date originalShipTime;
    /**
     * 报文原始交货时间
     */
    private Date originalDeliveryTime;
    /**
     * 预测开始日期(计算后的日期)
     */
    private Date forecastDateStart;
    /**
     * 预测结束日期(计算后的日期)
     */
    private Date forecastDateEnd;
    /**
     * ERP客户ID
     */
    private String ebsCustomerId;
    /**
     * ERP地址ID
     */
    private String ebsSiteId;
    /**
     * 客户零件号
     */
    private String customerItemNum;
    /**
     * 提前备料
     */
    private String materialsAdv;
    /**
     * 预测创建日期
     */
    private Date forecastCreationDate;
    /**
     * 预测版本号
     */
    private String releaseNum;
    /**
     * 最后更新时间
     */
    private Date lastUpdateDate;
    /**
     * 日需求/月需求 DAY/MONTH
     */
    private String submissionType;
    /**
     * GRP/MES
     */
    private String importType;
    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 版本
     */
    private Integer versionValue;
    /**
     * 接口流水号
     */
    private String serialNum;
    /**
     * 业务实体ID(组织ID)
     */
    private String buId;
    /**
     * 组织编码
     */
    private String organizationCode;
    /**
     * 组织名称
     */
    private String organizationName;
    /**
     * 客户名称
     */
    private String partyName;
    /**
     * 客户地址
     */
    private String siteName;
    /**
     * 已制单数
     */
    private BigDecimal orderedQty;
    /**
     * SO地址ID
     */
    private String erpPartySiteId;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * kid
     */
    private String kid;
    /**
     * 客户工厂
     */
    private String plantCode;
    /**
     * 客户编码
     */
    private String customerCode;

}
