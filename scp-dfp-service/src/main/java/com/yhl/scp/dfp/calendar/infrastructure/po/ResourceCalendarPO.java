package com.yhl.scp.dfp.calendar.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>ResourceCalendarPO</code>
 * <p>
 * 资源日历PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-07 16:06:22
 */
public class ResourceCalendarPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -10718125125023204L;

    /**
     * 生产组织ID
     */
    private String organizationId;
    /**
     * 标准资源id
     */
    private String standardResourceId;
    /**
     * 物理资源ID
     */
    private String physicalResourceId;
    /**
     * 日历规则ID
     */
    private String ruleId;
    /**
     * 班次ID
     */
    private String shiftId;
    /**
     * 出勤时段
     */
    private String shiftPattern;
    /**
     * 正班工时
     */
    private BigDecimal workHours;
    /**
     * 加班工时
     */
    private BigDecimal overtimeHours;
    /**
     * 工作日期
     */
    private Date workDay;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 异常日历类型
     */
    private String calendarType;
    /**
     * 效率
     */
    private BigDecimal efficiency;
    /**
     * 资源量
     */
    private Integer resourceQuantity;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getStandardResourceId() {
        return standardResourceId;
    }

    public void setStandardResourceId(String standardResourceId) {
        this.standardResourceId = standardResourceId;
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public String getPhysicalResourceId() {
        return physicalResourceId;
    }

    public void setPhysicalResourceId(String physicalResourceId) {
        this.physicalResourceId = physicalResourceId;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public String getShiftId() {
        return shiftId;
    }

    public void setShiftId(String shiftId) {
        this.shiftId = shiftId;
    }

    public String getShiftPattern() {
        return shiftPattern;
    }

    public void setShiftPattern(String shiftPattern) {
        this.shiftPattern = shiftPattern;
    }

    public BigDecimal getWorkHours() {
        return workHours;
    }

    public void setWorkHours(BigDecimal workHours) {
        this.workHours = workHours;
    }

    public BigDecimal getOvertimeHours() {
        return overtimeHours;
    }

    public void setOvertimeHours(BigDecimal overtimeHours) {
        this.overtimeHours = overtimeHours;
    }

    public Date getWorkDay() {
        return workDay;
    }

    public void setWorkDay(Date workDay) {
        this.workDay = workDay;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getCalendarType() {
        return calendarType;
    }

    public void setCalendarType(String calendarType) {
        this.calendarType = calendarType;
    }

    public BigDecimal getEfficiency() {
        return efficiency;
    }

    public void setEfficiency(BigDecimal efficiency) {
        this.efficiency = efficiency;
    }

    public Integer getResourceQuantity() {
        return resourceQuantity;
    }

    public void setResourceQuantity(Integer resourceQuantity) {
        this.resourceQuantity = resourceQuantity;
    }


}
