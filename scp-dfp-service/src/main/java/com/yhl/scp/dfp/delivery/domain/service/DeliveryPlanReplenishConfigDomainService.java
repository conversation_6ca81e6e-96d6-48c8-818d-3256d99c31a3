package com.yhl.scp.dfp.delivery.domain.service;

import com.yhl.scp.dfp.delivery.domain.entity.DeliveryPlanReplenishConfigDO;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanReplenishConfigDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>DeliveryPlanReplenishConfigDomainService</code>
 * <p>
 * 发货计划补货策略领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-01 10:44:35
 */
@Service
public class DeliveryPlanReplenishConfigDomainService {

    @Resource
    private DeliveryPlanReplenishConfigDao deliveryPlanReplenishConfigDao;

    /**
     * 数据校验
     *
     * @param deliveryPlanReplenishConfigDO 领域对象
     */
    public void validation(DeliveryPlanReplenishConfigDO deliveryPlanReplenishConfigDO) {
        checkNotNull(deliveryPlanReplenishConfigDO);
        checkUniqueCode(deliveryPlanReplenishConfigDO);
        // Do nothing because of X and Y.
    }

    /**
     * 非空检验
     *
     * @param deliveryPlanReplenishConfigDO 领域对象
     */
    private void checkNotNull(DeliveryPlanReplenishConfigDO deliveryPlanReplenishConfigDO) {
        // Do nothing because of X and Y.
    }

    /**
     * 唯一性校验
     *
     * @param deliveryPlanReplenishConfigDO 领域对象
     */
    private void checkUniqueCode(DeliveryPlanReplenishConfigDO deliveryPlanReplenishConfigDO) {
        // Do nothing because of X and Y.
    }

}
