package com.yhl.scp.dfp.policy.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.policy.convertor.PolicyInformationConvertor;
import com.yhl.scp.dfp.policy.domain.entity.PolicyInformationDO;
import com.yhl.scp.dfp.policy.domain.service.PolicyInformationDomainService;
import com.yhl.scp.dfp.policy.dto.PolicyInformationDTO;
import com.yhl.scp.dfp.policy.infrastructure.dao.PolicyInformationDao;
import com.yhl.scp.dfp.policy.infrastructure.po.PolicyInformationPO;
import com.yhl.scp.dfp.policy.service.PolicyInformationService;
import com.yhl.scp.dfp.policy.vo.PolicyInformationVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>PolicyInformationServiceImpl</code>
 * <p>
 * 政策信息应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-26 10:47:02
 */
@Slf4j
@Service
public class PolicyInformationServiceImpl extends AbstractService implements PolicyInformationService {

    @Resource
    private PolicyInformationDao policyInformationDao;

    @Resource
    private PolicyInformationDomainService policyInformationDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(PolicyInformationDTO policyInformationDTO) {
        // 0.数据转换
        PolicyInformationDO policyInformationDO = PolicyInformationConvertor.INSTANCE.dto2Do(policyInformationDTO);
        PolicyInformationPO policyInformationPO = PolicyInformationConvertor.INSTANCE.dto2Po(policyInformationDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        policyInformationDomainService.validation(policyInformationDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(policyInformationPO);
        policyInformationPO.setPolicyCode(getPolicyCode());
        policyInformationDao.insert(policyInformationPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(PolicyInformationDTO policyInformationDTO) {
        // 0.数据转换
        PolicyInformationDO policyInformationDO = PolicyInformationConvertor.INSTANCE.dto2Do(policyInformationDTO);
        PolicyInformationPO policyInformationPO = PolicyInformationConvertor.INSTANCE.dto2Po(policyInformationDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        policyInformationDomainService.validation(policyInformationDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(policyInformationPO);
        policyInformationDao.update(policyInformationPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<PolicyInformationDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<PolicyInformationPO> newList = PolicyInformationConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        for (PolicyInformationPO policyInformationPO : newList) {
            policyInformationPO.setPolicyCode(getPolicyCode());
        }
        policyInformationDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<PolicyInformationDTO> list) {
        List<PolicyInformationPO> newList = PolicyInformationConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        policyInformationDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return policyInformationDao.deleteBatch(idList);
        }
        return policyInformationDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public PolicyInformationVO selectByPrimaryKey(String id) {
        PolicyInformationPO po = policyInformationDao.selectByPrimaryKey(id);
        return PolicyInformationConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "POLICY_INFORMATION")
    public List<PolicyInformationVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "POLICY_INFORMATION")
    public List<PolicyInformationVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<PolicyInformationVO> dataList = policyInformationDao.selectByCondition(sortParam, queryCriteriaParam);
        PolicyInformationServiceImpl target = SpringBeanUtils.getBean(PolicyInformationServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<PolicyInformationVO> selectByParams(Map<String, Object> params) {
        List<PolicyInformationPO> list = policyInformationDao.selectByParams(params);
        return PolicyInformationConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<PolicyInformationVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.POLICY_INFORMATION.getCode();
    }

    @Override
    public List<PolicyInformationVO> invocation(List<PolicyInformationVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return policyInformationDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    private String getPolicyCode() {
        Date currentDate = new Date();
        String currentDateStr = "ZC" + DateUtils.dateToString(currentDate, "yyyyMMdd");
        String oldPolicyCode = policyInformationDao.selectMaxPolicyCode(currentDateStr);
        int num = 1;
        if (StringUtils.isNotBlank(oldPolicyCode)) {
            num = Integer.parseInt(oldPolicyCode.substring(oldPolicyCode.length() - 3)) + 1;
        }
        return currentDateStr + String.format("%03d", num);
    }
}
