<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.oem.infrastructure.dao.OemStockPointMapDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.oem.infrastructure.po.OemStockPointMapPO">
        <!--@Table mds_oem_stock_point_map-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.oem.vo.OemStockPointMapVO">
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        oem_code,
        stock_point_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,oem_name,stock_point_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.oemCodeList != null and params.oemCodeList.size() > 0">
                and oem_code in
                <foreach collection="params.oemCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.stockPointCodesNotInList != null and params.stockPointCodesNotInList.size() > 0">
                and stock_point_code not in
                <foreach collection="params.stockPointCodesNotInList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_oem_stock_point_map
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_oem_stock_point_map
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mds_oem_stock_point_map
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_oem_stock_point_map
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mds_oem_stock_point_map
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectByOemCodes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_oem_stock_point_map
        where oem_code = #{oemCode,jdbcType=VARCHAR}
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.oem.infrastructure.po.OemStockPointMapPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid())
            from dual
        </selectKey>
        insert into mds_oem_stock_point_map(id,
                                            oem_code,
                                            stock_point_code,
                                            remark,
                                            enabled,
                                            creator,
                                            create_time,
                                            modifier,
                                            modify_time,
                                            version_value)
        values (#{id,jdbcType=VARCHAR},
                #{oemCode,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.oem.infrastructure.po.OemStockPointMapPO">
        insert into mds_oem_stock_point_map(id,
                                            oem_code,
                                            stock_point_code,
                                            remark,
                                            enabled,
                                            creator,
                                            create_time,
                                            modifier,
                                            modify_time, version_value)
        values (#{id,jdbcType=VARCHAR},
                #{oemCode,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_oem_stock_point_map(id,
                                            oem_code,
                                            stock_point_code,
                                            remark,
                                            enabled,
                                            creator,
                                            create_time,
                                            modifier,
                                            modify_time,
                                            version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
             #{entity.oemCode,jdbcType=VARCHAR},
             #{entity.stockPointCode,jdbcType=VARCHAR},
             #{entity.remark,jdbcType=VARCHAR},
             #{entity.enabled,jdbcType=VARCHAR},
             #{entity.creator,jdbcType=VARCHAR},
             #{entity.createTime,jdbcType=TIMESTAMP},
             #{entity.modifier,jdbcType=VARCHAR},
             #{entity.modifyTime,jdbcType=TIMESTAMP},
             #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_oem_stock_point_map(id,
                                            oem_code,
                                            stock_point_code,
                                            remark,
                                            enabled,
                                            creator,
                                            create_time,
                                            modifier,
                                            modify_time,
                                            version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id,jdbcType=VARCHAR},
             #{entity.oemCode,jdbcType=VARCHAR},
             #{entity.stockPointCode,jdbcType=VARCHAR},
             #{entity.remark,jdbcType=VARCHAR},
             #{entity.enabled,jdbcType=VARCHAR},
             #{entity.creator,jdbcType=VARCHAR},
             #{entity.createTime,jdbcType=TIMESTAMP},
             #{entity.modifier,jdbcType=VARCHAR},
             #{entity.modifyTime,jdbcType=TIMESTAMP},
             #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.oem.infrastructure.po.OemStockPointMapPO">
        update mds_oem_stock_point_map
        set oem_code         = #{oemCode,jdbcType=VARCHAR},
            stock_point_code = #{stockPointCode,jdbcType=VARCHAR},
            remark           = #{remark,jdbcType=VARCHAR},
            enabled          = #{enabled,jdbcType=VARCHAR},
            modifier         = #{modifier,jdbcType=VARCHAR},
            modify_time      = #{modifyTime,jdbcType=TIMESTAMP},
            version_value    = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.oem.infrastructure.po.OemStockPointMapPO">
        update mds_oem_stock_point_map
        <set>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            version_value = version_value + 1
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_oem_stock_point_map
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            version_value = version_value + 1,
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mds_oem_stock_point_map
            <set>
                <if test="item.oemCode != null and item.oemCode != ''">
                    oem_code = #{item.oemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                version_value = version_value + 1
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
              and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mds_oem_stock_point_map
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete
        from mds_oem_stock_point_map where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <!-- 批量id+版本删除 -->
    <delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete
            from mds_oem_stock_point_map
            where id = #{item.id,jdbcType=VARCHAR}
              and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>

    <select id="selectByOemCodeList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_oem_stock_point_map
        where oem_code in
        <foreach collection="oemCodes" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectOemStockPointByStockPointCode" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mds_oem_stock_point_map
        where stock_point_code in
         <if test="stockPointCodeList != null and stockPointCodeList.size() > 0">
            <foreach collection="stockPointCodeList" item="item" open="(" separator="," close=")">
               #{item,jdbcType=VARCHAR}
            </foreach>
         </if>
    </select>
    
    <select id="selectExcludeStockPointCode" resultMap="VOResultMap">
       	SELECT 
			<include refid="Base_Column_List"/>
		FROM 
			mds_oem_stock_point_map 
		WHERE stock_point_code <![CDATA[ != ]]>  #{stockPointCode,jdbcType=VARCHAR}
    </select>

</mapper>
