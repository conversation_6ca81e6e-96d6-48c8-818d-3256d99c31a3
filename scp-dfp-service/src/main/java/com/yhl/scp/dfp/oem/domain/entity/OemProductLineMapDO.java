package com.yhl.scp.dfp.oem.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>OemProductLineMapDO</code>
 * <p>
 * 产线映射关系DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-20 14:50:24
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OemProductLineMapDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 467552679302740992L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 产线编码
     */
    private String lineCode;
    /**
     * 车型编码
     */
    private String vehicleModelCode;
    /**
     * 版本
     */
    private Integer versionValue;
    /**
     * 是否由福耀提供
     */
    private String providedByFuyao;

}
