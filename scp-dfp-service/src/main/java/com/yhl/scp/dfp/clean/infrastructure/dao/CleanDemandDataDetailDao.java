package com.yhl.scp.dfp.clean.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.clean.infrastructure.po.CleanDemandDataDetailPO;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataDetailVO;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>CleanDemandDataDetailDao</code>
 * <p>
 * 日需求数据明细DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-01 16:39:56
 */
public interface CleanDemandDataDetailDao extends BaseDao<CleanDemandDataDetailPO, CleanDemandDataDetailVO> {

    /**
     * 根据需求ID列表查询明细数据
     *
     * @param demandIds 需求ID列表
     * @return java.util.List<com.yhl.scp.dfp.clean.infrastructure.po.CleanDemandDataDetailPO>
     */
    List<CleanDemandDataDetailPO> selectByCleanDemandDataIds(@Param(value = "demandIds") List<String> demandIds);

    /**
     * 根据版本批量删除
     *
     * @param versionDTOList 删除对象列表
     * @return int
     */
    int deleteBatchVersion(@Param(value = "list") List<RemoveVersionDTO> versionDTOList);

    void deleteByDataIds(@Param(value = "list") List<String> list);

    List<CleanDemandDataDetailVO> selectCleanDemandDetail(@Param("cleanDemandIdList") List<String> cleanDemandIdList, @Param("scopeStart") String scopeStart, @Param("scopeEnd") String scopeEnd);

    List<CleanDemandDataDetailVO> selectDetailByParams(@Param("params") Map<String, Object> params);
}
