package com.yhl.scp.dfp.projectForecast.domain.factory;

import com.yhl.scp.dfp.projectForecast.domain.entity.NewProjectSubmissionDO;
import com.yhl.scp.dfp.projectForecast.dto.NewProjectSubmissionDTO;
import com.yhl.scp.dfp.projectForecast.infrastructure.dao.NewProjectSubmissionDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>NewProjectSubmissionFactory</code>
 * <p>
 * 新项目提报领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:25
 */
@Component
public class NewProjectSubmissionFactory {

    @Resource
    private NewProjectSubmissionDao newProjectSubmissionDao;

    NewProjectSubmissionDO create(NewProjectSubmissionDTO dto) {
        // TODO
        return null;
    }

}
