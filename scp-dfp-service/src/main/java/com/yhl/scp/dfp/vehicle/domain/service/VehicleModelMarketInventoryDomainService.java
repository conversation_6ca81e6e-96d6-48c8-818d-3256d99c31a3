package com.yhl.scp.dfp.vehicle.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.dfp.common.constants.AbnormalConstants;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.vehicle.domain.entity.VehicleModelMarketInventoryDO;
import com.yhl.scp.dfp.vehicle.infrastructure.dao.VehicleModelMarketInventoryDao;
import com.yhl.scp.dfp.vehicle.infrastructure.po.VehicleModelMarketInventoryPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class VehicleModelMarketInventoryDomainService {

    @Resource
    private VehicleModelMarketInventoryDao vehicleModelMarketInventoryDao;

    /**
     * 数据校验
     *
     * @param vehicleModelMarketInventoryDO 领域对象
     */
    public void validation(VehicleModelMarketInventoryDO vehicleModelMarketInventoryDO) {
        checkNotNull(vehicleModelMarketInventoryDO);
        checkUniqueCode(vehicleModelMarketInventoryDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param vehicleModelMarketInventoryDO 领域对象
     */
    private void checkNotNull(VehicleModelMarketInventoryDO vehicleModelMarketInventoryDO) {
        if (StringUtils.isBlank(vehicleModelMarketInventoryDO.getVehicleModelName())) {
            throw new BusinessException("车型市场保有量全球销量车型名称，不能为空");
        }
    }

    /**
     * 唯一性校验
     *
     * @param vehicleModelMarketInventoryDO 领域对象
     */
    private void checkUniqueCode(VehicleModelMarketInventoryDO vehicleModelMarketInventoryDO) {
        Map<String, Object> params = new HashMap<>(4);
        params.put("vehicleModelName", vehicleModelMarketInventoryDO.getVehicleModelName());
        if (StringUtils.isBlank(vehicleModelMarketInventoryDO.getId())) {
            List<VehicleModelMarketInventoryPO> list = vehicleModelMarketInventoryDao.selectByParams(params);
            if (CollectionUtils.isNotEmpty(list)) {
                throw new BusinessException("新增失败，车型市场保有量信息已存在");
            }
        } else {
            VehicleModelMarketInventoryPO old = vehicleModelMarketInventoryDao.selectByPrimaryKey(vehicleModelMarketInventoryDO.getId());
            if (!vehicleModelMarketInventoryDO.getVehicleModelName().equals(old.getVehicleModelName())) {
                List<VehicleModelMarketInventoryPO> list = vehicleModelMarketInventoryDao.selectByParams(params);
                if (CollectionUtils.isNotEmpty(list)) {
                    throw new BusinessException("修改失败，车型市场保有量信息已存在");
                }
            }
            if (!old.getVersionValue().equals(vehicleModelMarketInventoryDO.getVersionValue())) {
                throw new BusinessException(AbnormalConstants.UPDATE_VERSION_EXCEPTION_MSG);
            }
        }
    }

    public void checkDelete(List<RemoveVersionDTO> versionDTOList) {
        for (RemoveVersionDTO removeVersionDTO : versionDTOList) {
            VehicleModelMarketInventoryPO old = vehicleModelMarketInventoryDao.selectByPrimaryKey(removeVersionDTO.getId());
            if (Objects.isNull(old)) {
                throw new BusinessException(AbnormalConstants.DELETE_EXCEPTION_MSG + removeVersionDTO.getId());
            }
            if (!removeVersionDTO.getVersionValue().equals(old.getVersionValue())) {
                throw new BusinessException(AbnormalConstants.DELETE_VERSION_EXCEPTION_MSG);
            }
        }
    }
}
