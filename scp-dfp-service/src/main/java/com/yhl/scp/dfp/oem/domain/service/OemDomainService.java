package com.yhl.scp.dfp.oem.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.oem.domain.entity.OemDO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Service
public class OemDomainService {

    @Resource
    private OemDao oemDao;

    /**
     * 数据校验
     *
     * @param oemDO 领域对象
     */
    public void validation(OemDO oemDO) {
        checkNotNull(oemDO);
        checkUniqueCode(oemDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param oemDO 领域对象
     */
    private void checkNotNull(OemDO oemDO) {
        if (StringUtils.isBlank(oemDO.getOemCode())) {
            throw new BusinessException("主机厂档案代码，不能为空");
        }
    }

    /**
     * 唯一性校验
     *
     * @param oemDO 领域对象
     */
    private void checkUniqueCode(OemDO oemDO) {
        Map<String, Object> params = new HashMap<>(4);
        params.put("oemCode", oemDO.getOemCode());
        if (StringUtils.isBlank(oemDO.getId())) {
            List<OemPO> list = oemDao.selectByParams(params);
            if (CollectionUtils.isNotEmpty(list)) {
                throw new BusinessException("新增失败，主机厂档案代码已存在：" + oemDO.getOemCode());
            }
        } else {
            OemPO old = oemDao.selectByPrimaryKey(oemDO.getId());
            if (!oemDO.getOemCode().equals(old.getOemCode())) {
                List<OemPO> list = oemDao.selectByParams(params);
                if (CollectionUtils.isNotEmpty(list)) {
                    throw new BusinessException("修改失败，主机厂档案代码已存在：" + oemDO.getOemCode());
                }
            }
            if (!old.getVersionValue().equals(oemDO.getVersionValue())) {
                throw new BusinessException("修改失败，数据已被修改，请刷新后重试");
            }
        }
    }

    /**
     * 删除数据校验
     *
     * @param removeVersionDTOS
     */
    public void checkDelete(List<RemoveVersionDTO> removeVersionDTOS) {
        if (CollectionUtils.isEmpty(removeVersionDTOS)) {
            return;
        }
        for (RemoveVersionDTO removeVersionDTO : removeVersionDTOS) {
            OemPO old = oemDao.selectByPrimaryKey(removeVersionDTO.getId());
            if (Objects.isNull(old)) {
                throw new BusinessException("删除失败，数据不存在：" + removeVersionDTO.getId());
            }
            if (!removeVersionDTO.getVersionValue().equals(old.getVersionValue())) {
                throw new BusinessException("删除失败，数据已被修改，请刷新后重试");
            }
        }
    }
}
