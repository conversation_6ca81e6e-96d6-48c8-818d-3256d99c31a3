package com.yhl.scp.dfp.delivery.domain.factory;

import com.yhl.scp.dfp.delivery.domain.entity.DeliveryPlanPublishedCompareDO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedCompareDTO;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanPublishedCompareDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>DeliveryPlanPublishedCompareFactory</code>
 * <p>
 * 发货计划发布数量变化对比表领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-25 10:53:27
 */
@Component
public class DeliveryPlanPublishedCompareFactory {

    @Resource
    private DeliveryPlanPublishedCompareDao deliveryPlanPublishedCompareDao;

    DeliveryPlanPublishedCompareDO create(DeliveryPlanPublishedCompareDTO dto) {
        // TODO
        return null;
    }

}
