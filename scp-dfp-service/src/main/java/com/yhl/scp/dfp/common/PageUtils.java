package com.yhl.scp.dfp.common;


import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageInfo;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class PageUtils {

    /**
     * 根据传入的List和页码返回分页后的List
     *
     * @param original 全量的List数据
     * @param pageNum  页码
     * @param pageSize 每页数据条数
     * @param <T>
     * @return 返回分页后的对应页码页面的List
     */
    public static <T> List<T> page(List<T> original, int pageNum, int pageSize) {
        List list = new ArrayList<T>();
        Collections.addAll(list, page(original.toArray(), pageNum, pageSize));
        return list;
    }

    /**
     * 组装分页对象PageInfo
     *
     * @param dataList 数据集
     * @param pageNum 起始页
     * @param pageSize 页大小
     * @param <T> 类型
     * @return PageInfo {@link PageInfo}
     */
    public static <T> PageInfo getPageInfo(List<T> dataList, int pageNum, int pageSize) {
        if(ObjectUtil.isEmpty(pageNum)){
            pageNum = 1;
        }
        if(ObjectUtil.isEmpty(pageSize)){
            pageSize = 50;
        }
        PageInfo<org.apache.poi.ss.formula.functions.T> objectPageInfo = new PageInfo<>();
        List list = new ArrayList<org.apache.poi.ss.formula.functions.T>();
        Collections.addAll(list, page(dataList.toArray(), pageNum, pageSize));
        //结果集
        objectPageInfo.setList(list);
        //起始页
        objectPageInfo.setPageNum(pageNum);
        //截至页
        objectPageInfo.setPageSize(pageSize);
        //总记录数字
        objectPageInfo.setTotal(dataList.size());
        //当前页的数量
        objectPageInfo.setSize(list.size());
        //总页数
        int pages = 0;
        if (dataList.size() % pageSize == 0) {
            pages = dataList.size() / pageSize;
        }
        if (dataList.size() % pageSize > 0) {
            pages = dataList.size() / pageSize + 1;
        }
        int[] arr = new int[pages];
        for (int i = 0; i < pages; i++) {
            arr[i] = i + 1;
        }
        //所有导航页号
        objectPageInfo.setNavigatepageNums(arr);
        //总页数
        objectPageInfo.setPages(pages);
        return objectPageInfo;
    }


    /**
     * 根据传入的数组和页码返回分页后的数组
     *
     * @param original 全量数据的数组
     * @param pageNum  页码
     * @param pageSize 每页数据条数
     * @param <T>      泛型
     * @return 返回分页后的对应页码页面的数据
     */
    public static <T> T[] page(T[] original, int pageNum, int pageSize) {
    	if (null == original) {
            return null;
        }
        //当前集合是否为空
        if (null != original && original.length == 0) {
            return (T[]) Array.newInstance(original.getClass().getComponentType(), 0);
        }
        //如果起始页数等0，默认从第一页开始
        if (pageNum <= 0) {
            pageNum = 1;
        }
        //计算截取数组的起始位置和截至位置
        int from = (pageNum - 1) * pageSize;
        int to = pageNum * pageSize;
        if (to > original.length) {
            to = original.length;
        }
        if (from >= original.length || to <= from) {
            return (T[]) Array.newInstance(original.getClass().getComponentType(), 0);
        }
        //当前方法返回一个指定范围的新数组
        return Arrays.copyOfRange(original, from, to);
    }

}
