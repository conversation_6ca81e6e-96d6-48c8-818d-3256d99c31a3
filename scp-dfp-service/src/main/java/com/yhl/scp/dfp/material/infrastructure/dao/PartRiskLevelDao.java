package com.yhl.scp.dfp.material.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.material.dto.PartRiskLevelDTO;
import com.yhl.scp.dfp.material.infrastructure.po.PartRiskLevelPO;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>PartRiskLevelDao</code>
 * <p>
 * 零件风险等级DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:11
 */
public interface PartRiskLevelDao extends BaseDao<PartRiskLevelPO, PartRiskLevelVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link PartRiskLevelVO}
     */
    List<PartRiskLevelVO> selectVOByParams(@Param("params") Map<String, Object> params);

    /**
     * 根据版本批量删除
     * @param versionDTOList
     * @return
     */
    int deleteBatchVersion(@Param("list") List<RemoveVersionDTO> versionDTOList);

    List<PartRiskLevelVO> selectByProductCodeList(@Param("productCodeList") List<String> productCodeList,
                                                  @Param("oemCode") String oemCode);

    void deleteAll();

	List<PartRiskLevelVO> selectLevelByOmeCodes(@Param("list") List<String> oemCodeList);

	void batchUpdateLevel(PartRiskLevelDTO dto);

}
