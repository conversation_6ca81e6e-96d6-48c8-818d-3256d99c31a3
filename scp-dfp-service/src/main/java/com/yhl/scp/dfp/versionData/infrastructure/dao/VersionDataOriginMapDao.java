package com.yhl.scp.dfp.versionData.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.versionData.infrastructure.po.VersionDataOriginMapPO;
import com.yhl.scp.dfp.versionData.vo.VersionDataOriginMapVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <code>VersionDataOriginMapDao</code>
 * <p>
 * 原始版本数据映射关系DAO
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 17:29:03
 */
public interface VersionDataOriginMapDao extends BaseDao<VersionDataOriginMapPO, VersionDataOriginMapVO> {

    /**
     * 根据版本批量删除
     * @param versionDTOList
     * @return
     */
    int deleteBatchVersion(@Param("list") List<RemoveVersionDTO> versionDTOList);

}
