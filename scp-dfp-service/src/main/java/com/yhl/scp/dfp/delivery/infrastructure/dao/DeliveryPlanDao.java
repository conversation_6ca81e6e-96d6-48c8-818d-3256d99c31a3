package com.yhl.scp.dfp.delivery.infrastructure.dao;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanDTO;
import com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanExportVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>DeliveryPlanDao</code>
 * <p>
 * 发货计划表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 13:51:23
 */
public interface DeliveryPlanDao extends BaseDao<DeliveryPlanPO, DeliveryPlanVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link DeliveryPlanVO}
     */
    List<DeliveryPlanVO> selectVOByParams(@Param("params") Map<String, Object> params);

    /**
     * 根据版本批量删除
     *
     * @param versionDTOList
     * @return
     */
    int deleteBatchVersion(@Param("list") List<RemoveVersionDTO> versionDTOList);

    /**
     * 按计划周期获取最新发货计划
     *
     * @param planPeriod
     * @return
     */
    List<DeliveryPlanVO2> selectVO2ByPlanPeriod(@Param("planPeriod") String planPeriod, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 按版本获取最新发货计划,不传版本获取创建日期最新的版本
     *
     * @param deliveryVersionId
     * @return
     */
    List<DeliveryPlanVO2> selectVO2ByDeliveryVersionId(@Param("deliveryVersionId") String deliveryVersionId);

    /**
     * 根据DTO查询
     *
     * @param deliveryPlanDTO
     * @return
     */
    DeliveryPlanPO selectByDeliveryPlanDTO(DeliveryPlanDTO deliveryPlanDTO);

    String selectLatestPublishedVersionCode();

    List<DeliveryPlanPO> selectCopy();

    List<DeliveryPlanExportVO> selectExportData(@Param("versionCode") String versionCode,
                                                @Param("beginDate") String beginDate, @Param("endDate") String endDate);

    List<LabelValue<String>> selectOemLabelValue(@Param("versionCode") String versionCode);

	List<DeliveryPlanVO> selectDeliveryReport(@Param("sortParam") String sortParam, @Param("queryCriteriaParam") String queryCriteriaParam);

}
