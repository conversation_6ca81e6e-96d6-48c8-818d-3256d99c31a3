package com.yhl.scp.dfp.global.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yhl.platform.common.CustomThreadPoolFactory;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.global.convertor.GlobalCarSaleConvertor;
import com.yhl.scp.dfp.global.domain.entity.GlobalCarSaleDO;
import com.yhl.scp.dfp.global.domain.service.GlobalCarSaleDomainService;
import com.yhl.scp.dfp.global.dto.GlobalCarSaleDTO;
import com.yhl.scp.dfp.global.infrastructure.dao.GlobalCarSaleDao;
import com.yhl.scp.dfp.global.infrastructure.dao.GlobalCarSaleDetailDao;
import com.yhl.scp.dfp.global.infrastructure.po.GlobalCarSaleDetailPO;
import com.yhl.scp.dfp.global.infrastructure.po.GlobalCarSalePO;
import com.yhl.scp.dfp.global.service.GlobalCarSaleDetailService;
import com.yhl.scp.dfp.global.service.GlobalCarSaleService;
import com.yhl.scp.dfp.global.vo.GlobalCarSaleVO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemVehicleModelMapDao;
import com.yhl.scp.ips.utils.BasePOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.yhl.scp.dfp.utils.BpimDateUtil.getDateByYearAndMonth;

/**
 * <code>GlobalCarSaleServiceImpl</code>
 * <p>
 * 全球汽车销量应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:11
 */
@Slf4j
@Service
public class GlobalCarSaleServiceImpl extends AbstractService implements GlobalCarSaleService {

    @Resource
    private GlobalCarSaleDao globalCarSaleDao;

    @Resource
    private GlobalCarSaleDetailDao globalCarSaleDetailDao;

    @Resource
    private GlobalCarSaleDomainService globalCarSaleDomainService;

    @Resource
    private GlobalCarSaleDetailService globalCarSaleDetailService;

    @Resource
    private OemVehicleModelMapDao oemVehicleModelMapDao;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(GlobalCarSaleDTO globalCarSaleDTO) {
        // 0.数据转换
        GlobalCarSaleDO globalCarSaleDO = GlobalCarSaleConvertor.INSTANCE.dto2Do(globalCarSaleDTO);
        GlobalCarSalePO globalCarSalePO = GlobalCarSaleConvertor.INSTANCE.dto2Po(globalCarSaleDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        globalCarSaleDomainService.validation(globalCarSaleDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(globalCarSalePO);
        globalCarSaleDao.insertWithPrimaryKey(globalCarSalePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(GlobalCarSaleDTO globalCarSaleDTO) {
        // 0.数据转换
        GlobalCarSaleDO globalCarSaleDO = GlobalCarSaleConvertor.INSTANCE.dto2Do(globalCarSaleDTO);
        GlobalCarSalePO globalCarSalePO = GlobalCarSaleConvertor.INSTANCE.dto2Po(globalCarSaleDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        globalCarSaleDomainService.validation(globalCarSaleDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(globalCarSalePO);
        globalCarSaleDao.update(globalCarSalePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<GlobalCarSaleDTO> list) {
        List<GlobalCarSalePO> newList = GlobalCarSaleConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        globalCarSaleDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<GlobalCarSaleDTO> list) {
        List<GlobalCarSalePO> newList = GlobalCarSaleConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        globalCarSaleDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return globalCarSaleDao.deleteBatch(idList);
        }
        // 删除详情信息
        globalCarSaleDetailDao.deleteByGlobalCarSaleId(idList);
        return globalCarSaleDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public GlobalCarSaleVO selectByPrimaryKey(String id) {
        GlobalCarSalePO po = globalCarSaleDao.selectByPrimaryKey(id);
        return GlobalCarSaleConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_global_car_sale")
    public List<GlobalCarSaleVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_global_car_sale")
    public List<GlobalCarSaleVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<GlobalCarSaleVO> dataList = globalCarSaleDao.selectByCondition(sortParam, queryCriteriaParam);
        GlobalCarSaleServiceImpl target = springBeanUtils.getBean(GlobalCarSaleServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<GlobalCarSaleVO> selectByParams(Map<String, Object> params) {
        List<GlobalCarSalePO> list = globalCarSaleDao.selectByParams(params);
        return GlobalCarSaleConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<GlobalCarSaleVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.GLOBAL_CAR_SALE.getCode();
    }

    @Override
    public List<GlobalCarSaleVO> invocation(List<GlobalCarSaleVO> dataList, Map<String, Object> params, String invocation) {
        if (CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }

        String dataSource = DynamicDataSourceContextHolder.getDataSource();
        int degree = invocation.split("#").length;
        ThreadPoolExecutor threadPoolExecutor = CustomThreadPoolFactory.getDegreeThreadPool(degree);
        // 异步任务集合
        List<CompletableFuture<Void>> completableFutures = new ArrayList<>();
        List<GlobalCarSaleVO> list0 = dataList.stream().filter(t -> !"*".equals(t.getId()) && StringUtils.isNotEmpty(t.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list0)) {
            completableFutures.add(CompletableFuture.runAsync(() -> {
                DynamicDataSourceContextHolder.setDataSource(dataSource);
                // 冗余生产组织字段
                globalCarSaleDetailService.addGlobalCarSaleDetailsColumn(list0, invocation, "setGlobalCarSaleDetails", "globalCarSaleIds", "id", "globalCarSaleId");
            }, threadPoolExecutor));
        }
        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();
        return dataList;
    }

    @Override
    public void doImport(Map<Integer, String> headers, List<Map<Integer, String>> data, int fixedColumns, Map<String, String> extMap) {
        // 通过主机厂车型映射表关联
        // Map<String, String> oemVehicleModelMap = oemVehicleModelMapDao.selectByParams(new HashMap<>(2)).stream().collect(Collectors.toMap(OemVehicleModelMapPO::getGlobalVehicleModelCode, OemVehicleModelMapPO::getVehicleModelCode));
        List<GlobalCarSalePO> globalCarSalePOs = new ArrayList<>();
        List<GlobalCarSaleDetailPO> globalCarSaleDetailPOs = new ArrayList<>();
        for (Map<Integer, String> rowData : data) {
            // 构建全局汽车销量PO
            GlobalCarSalePO globalCarSalePO = new GlobalCarSalePO();
            globalCarSalePO.setId(UUIDUtil.getUUID());
            globalCarSalePO.setCountry(rowData.get(0));
            globalCarSalePO.setCorporate(rowData.get(1));
            globalCarSalePO.setBrand(rowData.get(2));
            globalCarSalePO.setVehicleType(rowData.get(3));
            globalCarSalePO.setVehicleLevel(rowData.get(4));
            globalCarSalePO.setGlobalVehicleModelCode(rowData.get(5));
            globalCarSalePO.setVehicleModel(rowData.get(5));
            globalCarSalePO.setPowerTrain(rowData.get(6));
            // todo 待设置FY内部客户代码
            globalCarSalePO.setOemCode("xxx");
            // globalCarSalePO.setVehicleModelCode(oemVehicleModelMap.getOrDefault(globalCarSalePO.getGlobalVehicleModelCode(), null));
            globalCarSalePOs.add(globalCarSalePO);
            // 拆分明细信息
            for (int i = fixedColumns; i < headers.size() - 2; i++) {
                GlobalCarSaleDetailPO globalCarSaleDetailPO = new GlobalCarSaleDetailPO();
                globalCarSaleDetailPO.setId(UUIDUtil.getUUID());
                globalCarSaleDetailPO.setGlobalCarSaleId(globalCarSalePO.getId());
                globalCarSaleDetailPO.setSaleTime(getDateByYearAndMonth(headers.get(i)));
                globalCarSaleDetailPO.setSaleQuantity(rowData.get(i));
                globalCarSaleDetailPOs.add(globalCarSaleDetailPO);
            }
        }
        saveDbData(globalCarSalePOs, globalCarSaleDetailPOs);
    }

    private void saveDbData(List<GlobalCarSalePO> globalCarSalePOs, List<GlobalCarSaleDetailPO> globalCarSaleDetailPOs) {
        if (CollectionUtils.isNotEmpty(globalCarSalePOs)) {
            // 全量导入，先清空表
            globalCarSaleDao.deleteAll();
            BasePOUtils.insertBatchFiller(globalCarSalePOs);
            Lists.partition(globalCarSalePOs, 5000).forEach(temp ->
                    // 冗余生产组织字段
                    globalCarSaleDao.insertBatchWithPrimaryKey(temp)
            );
        }
        if (CollectionUtils.isNotEmpty(globalCarSaleDetailPOs)) {
            // 插入明细表数据
            globalCarSaleDetailDao.deleteThreeYearAgo();
            BasePOUtils.insertBatchFiller(globalCarSaleDetailPOs);
            Lists.partition(globalCarSaleDetailPOs, 5000).forEach(temp -> {
                // 冗余生产组织字段
                globalCarSaleDetailDao.insertBatchWithPrimaryKey(temp);
            });
        }
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isEmpty(versionDTOList)) {
            return 0;
        }
        for (RemoveVersionDTO removeVersionDTO : versionDTOList) {
            int deleted = globalCarSaleDao.deleteByPrimaryKey(removeVersionDTO.getId());
            if (deleted < 1) {
                continue;
            }
            globalCarSaleDetailService.deleteByGlobalCarSaleId(removeVersionDTO.getId());
        }
        return 0;
    }
}
