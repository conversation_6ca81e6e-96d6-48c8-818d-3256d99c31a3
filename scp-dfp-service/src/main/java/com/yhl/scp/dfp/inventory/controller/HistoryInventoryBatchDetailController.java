package com.yhl.scp.dfp.inventory.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.inventory.dto.HistoryInventoryBatchDetailDTO;
import com.yhl.scp.dfp.inventory.service.HistoryInventoryBatchDetailService;
import com.yhl.scp.dfp.inventory.vo.HistoryInventoryBatchDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>HistoryInventoryBatchDetailController</code>
 * <p>
 * 历史库存批次明细控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-08 16:05:24
 */
@Slf4j
@Api(tags = "历史库存批次明细控制器")
@RestController
@RequestMapping("historyInventoryBatchDetail")
public class HistoryInventoryBatchDetailController extends BaseController {

    @Resource
    private HistoryInventoryBatchDetailService historyInventoryBatchDetailService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<HistoryInventoryBatchDetailVO>> page() {
        List<HistoryInventoryBatchDetailVO> historyInventoryBatchDetailList = historyInventoryBatchDetailService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<HistoryInventoryBatchDetailVO> pageInfo = new PageInfo<>(historyInventoryBatchDetailList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody HistoryInventoryBatchDetailDTO historyInventoryBatchDetailDTO) {
        return historyInventoryBatchDetailService.doCreate(historyInventoryBatchDetailDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody HistoryInventoryBatchDetailDTO historyInventoryBatchDetailDTO) {
        return historyInventoryBatchDetailService.doUpdate(historyInventoryBatchDetailDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        historyInventoryBatchDetailService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<HistoryInventoryBatchDetailVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, historyInventoryBatchDetailService.selectByPrimaryKey(id));
    }

}
