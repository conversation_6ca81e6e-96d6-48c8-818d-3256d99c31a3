package com.yhl.scp.dfp.transport.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.transport.infrastructure.po.TransportRoutingDetailPO;
import com.yhl.scp.dfp.transport.vo.TransportRoutingDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <code>TransportRoutingDetailDao</code>
 * <p>
 * 运输线路DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-20 22:20:32
 */
public interface TransportRoutingDetailDao extends BaseDao<TransportRoutingDetailPO, TransportRoutingDetailVO> {

    int deleteBatchVersion(@Param("list") List<RemoveVersionDTO> removeVersionDTOS);

    TransportRoutingDetailPO selectTransportModelByLineCode(@Param("routingDetailCode") String routingDetailCode);
}
