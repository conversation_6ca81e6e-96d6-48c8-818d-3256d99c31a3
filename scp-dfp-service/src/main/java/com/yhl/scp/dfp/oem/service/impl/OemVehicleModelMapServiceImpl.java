package com.yhl.scp.dfp.oem.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.oem.convertor.OemVehicleModelMapConvertor;
import com.yhl.scp.dfp.oem.domain.entity.OemVehicleModelMapDO;
import com.yhl.scp.dfp.oem.domain.service.OemVehicleModelMapDomainService;
import com.yhl.scp.dfp.oem.dto.OemVehicleModelMapDTO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemVehicleModelMapDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemVehicleModelMapPO;
import com.yhl.scp.dfp.oem.service.OemVehicleModelMapService;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelMapVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>OemVehicleModelMapServiceImpl</code>
 * <p>
 * 主机厂车型映射关系应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:48:09
 */
@Slf4j
@Service
public class OemVehicleModelMapServiceImpl extends AbstractService implements OemVehicleModelMapService {

    @Resource
    private OemVehicleModelMapDao oemVehicleModelMapDao;

    @Resource
    private OemVehicleModelMapDomainService oemVehicleModelMapDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(OemVehicleModelMapDTO oemVehicleModelMapDTO) {
        // 0.数据转换
        OemVehicleModelMapDO oemVehicleModelMapDO = OemVehicleModelMapConvertor.INSTANCE.dto2Do(oemVehicleModelMapDTO);
        OemVehicleModelMapPO oemVehicleModelMapPO = OemVehicleModelMapConvertor.INSTANCE.dto2Po(oemVehicleModelMapDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        oemVehicleModelMapDomainService.validation(oemVehicleModelMapDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(oemVehicleModelMapPO);
        oemVehicleModelMapDao.insertWithPrimaryKey(oemVehicleModelMapPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(OemVehicleModelMapDTO oemVehicleModelMapDTO) {
        // 0.数据转换
        OemVehicleModelMapDO oemVehicleModelMapDO = OemVehicleModelMapConvertor.INSTANCE.dto2Do(oemVehicleModelMapDTO);
        OemVehicleModelMapPO oemVehicleModelMapPO = OemVehicleModelMapConvertor.INSTANCE.dto2Po(oemVehicleModelMapDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        oemVehicleModelMapDomainService.validation(oemVehicleModelMapDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(oemVehicleModelMapPO);
        oemVehicleModelMapDao.update(oemVehicleModelMapPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<OemVehicleModelMapDTO> list) {
        List<OemVehicleModelMapPO> newList = OemVehicleModelMapConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        oemVehicleModelMapDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<OemVehicleModelMapDTO> list) {
        List<OemVehicleModelMapPO> newList = OemVehicleModelMapConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        oemVehicleModelMapDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return oemVehicleModelMapDao.deleteBatch(idList);
        }
        return oemVehicleModelMapDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public OemVehicleModelMapVO selectByPrimaryKey(String id) {
        OemVehicleModelMapPO po = oemVehicleModelMapDao.selectByPrimaryKey(id);
        return OemVehicleModelMapConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mds_oem_vehicle_model_map")
    public List<OemVehicleModelMapVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mds_oem_vehicle_model_map")
    public List<OemVehicleModelMapVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<OemVehicleModelMapVO> dataList = oemVehicleModelMapDao.selectByCondition(sortParam, queryCriteriaParam);
        OemVehicleModelMapServiceImpl target = springBeanUtils.getBean(OemVehicleModelMapServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<OemVehicleModelMapVO> selectByParams(Map<String, Object> params) {
        List<OemVehicleModelMapPO> list = oemVehicleModelMapDao.selectByParams(params);
        return OemVehicleModelMapConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<OemVehicleModelMapVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.OEM_VEHICLE_MODEL_MAP.getCode();
    }

    @Override
    public List<OemVehicleModelMapVO> invocation(List<OemVehicleModelMapVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isEmpty(versionDTOList)) {
            return 0;
        }
        oemVehicleModelMapDomainService.checkDelete(versionDTOList);
        return oemVehicleModelMapDao.deleteBatchVersion(versionDTOList);
    }
}
