package com.yhl.scp.dfp.warehouse.domain.factory;

import com.yhl.scp.dfp.warehouse.domain.entity.WarehouseReleaseToWarehouseDO;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReleaseToWarehouseDTO;
import com.yhl.scp.dfp.warehouse.infrastructure.dao.WarehouseReleaseToWarehouseDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class WarehouseReleaseToWarehouseFactory {

    @Resource
    private WarehouseReleaseToWarehouseDao warehouseReleaseToWarehouseDao;

    WarehouseReleaseToWarehouseDO create(WarehouseReleaseToWarehouseDTO dto) {
        // TODO
        return null;
    }

}
