package com.yhl.scp.dfp.global.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.global.infrastructure.po.GlobalCarSalePO;
import com.yhl.scp.dfp.global.vo.GlobalCarSaleVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>GlobalCarSaleDao</code>
 * <p>
 * 全球汽车销量DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:11
 */
public interface GlobalCarSaleDao extends BaseDao<GlobalCarSalePO, GlobalCarSaleVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link GlobalCarSaleVO}
     */
    List<GlobalCarSaleVO> selectVOByParams(@Param("params") Map<String, Object> params);

    int deleteAll();

    /**
     * 根据版本批量删除
     * @param versionDTOList
     * @return
     */
    int deleteBatchVersion(@Param("list") List<RemoveVersionDTO> versionDTOList);

    List<GlobalCarSalePO> selectByVehicleModelCodes(@Param("vehicleModelCodeList") List<String> vehicleModelCodeList);
}
