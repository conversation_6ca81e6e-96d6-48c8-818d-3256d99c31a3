<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.part.infrastructure.dao.PartRelationMapDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.part.infrastructure.po.PartRelationMapPO">
        <!--@Table fdp_part_relation_map-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="mapping_id" jdbcType="VARCHAR" property="mappingId"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="part_number" jdbcType="VARCHAR" property="partNumber"/>
        <result column="part_name" jdbcType="VARCHAR" property="partName"/>
        <result column="effective_start_time" jdbcType="TIMESTAMP" property="effectiveStartTime"/>
        <result column="effective_end_time" jdbcType="TIMESTAMP" property="effectiveEndTime"/>
        <result column="stock_point_id" jdbcType="VARCHAR" property="stockPointId"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="site_id" jdbcType="VARCHAR" property="siteId"/>
        <result column="customer_item_num_alias" jdbcType="VARCHAR" property="customerItemNumAlias"/>
        <result column="po_num" jdbcType="VARCHAR" property="poNum"/>
        <result column="old_product_code" jdbcType="VARCHAR" property="oldProductCode"/>
        <result column="relation_version" jdbcType="VARCHAR" property="relationVersion"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.part.vo.PartRelationMapVO">
        <!-- TODO -->
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="customer_code" jdbcType="VARCHAR" property="customerCode"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="location_area2" jdbcType="VARCHAR" property="locationArea2"/>
    </resultMap>
    <sql id="Base_Column_List">
id,mapping_id,product_code,part_number,part_name,effective_start_time,effective_end_time,stock_point_id,customer_id,source_type,remark,enabled,creator,create_time,modifier,modify_time,version_value,site_id,customer_item_num_alias,po_num,old_product_code,relation_version
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />, product_name,stock_point_code,customer_code,customer_name,location_area2
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.mappingId != null and params.mappingId != ''">
                and mapping_id = #{params.mappingId,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodeList != null and params.productCodeList.size() > 0">
                and product_code in
                <foreach collection="params.productCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.partNumber != null and params.partNumber != ''">
                and part_number = #{params.partNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.partName != null and params.partName != ''">
                and part_name = #{params.partName,jdbcType=VARCHAR}
            </if>
            <if test="params.effectiveStartTime != null">
                and effective_start_time = #{params.effectiveStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.effectiveEndTime != null">
                and effective_end_time = #{params.effectiveEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.stockPointId != null and params.stockPointId != ''">
                and stock_point_id = #{params.stockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.customerId != null and params.customerId != ''">
                and customer_id = #{params.customerId,jdbcType=VARCHAR}
            </if>
            <if test="params.sourceType != null and params.sourceType != ''">
                and source_type = #{params.sourceType,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.siteId != null and params.siteId != ''">
                and site_id = #{params.siteId,jdbcType=VARCHAR}
            </if>
            <if test="params.customerItemNumAlias != null and params.customerItemNumAlias != ''">
                and customer_item_num_alias = #{params.customerItemNumAlias,jdbcType=VARCHAR}
            </if>
            <if test="params.poNum != null and params.poNum != ''">
                and po_num = #{params.poNum,jdbcType=VARCHAR}
            </if>
            <if test="params.oldProductCode != null and params.oldProductCode != ''">
                and old_product_code = #{params.oldProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.relationVersion != null and params.relationVersion != ''">
                and relation_version = #{params.relationVersion,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="VO_Column_List" />
        from v_fdp_part_relation_map
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_part_relation_map
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_fdp_part_relation_map
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_part_relation_map
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="BaseResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_fdp_part_relation_map
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectVOByPrimaryKey" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_fdp_part_relation_map
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectByThreeParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_part_relation_map
        where (product_code,part_number,relation_version,source_type) in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            (#{item.productCode},#{item.partNumber},#{item.relationVersion},#{item.sourceType})
        </foreach>
    </select>
    <select id="selectTwoParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_part_relation_map
        where (mapping_id,customer_id) in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            (#{item.mappingId},#{item.customerId})
        </foreach>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.part.infrastructure.po.PartRelationMapPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_part_relation_map(
        id,
        mapping_id,
        product_code,
        part_number,
        part_name,
        effective_start_time,
        effective_end_time,
        stock_point_id,
        customer_id,
        source_type,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        site_id,
        customer_item_num_alias,
        po_num,
        old_product_code,
        relation_version)
        values (
        #{id,jdbcType=VARCHAR},
        #{mappingId,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{partNumber,jdbcType=VARCHAR},
        #{partName,jdbcType=VARCHAR},
        #{effectiveStartTime,jdbcType=TIMESTAMP},
        #{effectiveEndTime,jdbcType=TIMESTAMP},
        #{stockPointId,jdbcType=VARCHAR},
        #{customerId,jdbcType=VARCHAR},
        #{sourceType,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{siteId,jdbcType=VARCHAR},
        #{customerItemNumAlias,jdbcType=VARCHAR},
        #{poNum,jdbcType=VARCHAR},
        #{oldProductCode,jdbcType=VARCHAR},
        #{relationVersion,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.part.infrastructure.po.PartRelationMapPO">
        insert into fdp_part_relation_map(
        id,
        mapping_id,
        product_code,
        part_number,
        part_name,
        effective_start_time,
        effective_end_time,
        stock_point_id,
        customer_id,
        source_type,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        site_id,
        customer_item_num_alias,
        po_num,
        old_product_code,
        relation_version)
        values (
        #{id,jdbcType=VARCHAR},
        #{mappingId,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{partNumber,jdbcType=VARCHAR},
        #{partName,jdbcType=VARCHAR},
        #{effectiveStartTime,jdbcType=TIMESTAMP},
        #{effectiveEndTime,jdbcType=TIMESTAMP},
        #{stockPointId,jdbcType=VARCHAR},
        #{customerId,jdbcType=VARCHAR},
        #{sourceType,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{siteId,jdbcType=VARCHAR},
        #{customerItemNumAlias,jdbcType=VARCHAR},
        #{poNum,jdbcType=VARCHAR},
        #{oldProductCode,jdbcType=VARCHAR},
        #{relationVersion,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into `scp_fysh`.`fdp_part_relation_map`(
        id,
        mapping_id,
        product_code,
        part_number,
        part_name,
        effective_start_time,
        effective_end_time,
        stock_point_id,
        customer_id,
        source_type,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        site_id,
        customer_item_num_alias,
        po_num,
        old_product_code,
        relation_version)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.mappingId,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.partNumber,jdbcType=VARCHAR},
        #{entity.partName,jdbcType=VARCHAR},
        #{entity.effectiveStartTime,jdbcType=TIMESTAMP},
        #{entity.effectiveEndTime,jdbcType=TIMESTAMP},
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.customerId,jdbcType=VARCHAR},
        #{entity.sourceType,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.siteId,jdbcType=VARCHAR},
        #{entity.customerItemNumAlias,jdbcType=VARCHAR},
        #{entity.poNum,jdbcType=VARCHAR},
        #{entity.oldProductCode,jdbcType=VARCHAR},
        #{entity.relationVersion,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_part_relation_map(
        id,
        mapping_id,
        product_code,
        part_number,
        part_name,
        effective_start_time,
        effective_end_time,
        stock_point_id,
        customer_id,
        source_type,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        site_id,
        customer_item_num_alias,
        po_num,
        old_product_code,
        relation_version)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.mappingId,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.partNumber,jdbcType=VARCHAR},
        #{entity.partName,jdbcType=VARCHAR},
        #{entity.effectiveStartTime,jdbcType=TIMESTAMP},
        #{entity.effectiveEndTime,jdbcType=TIMESTAMP},
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.customerId,jdbcType=VARCHAR},
        #{entity.sourceType,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.siteId,jdbcType=VARCHAR},
        #{entity.customerItemNumAlias,jdbcType=VARCHAR},
        #{entity.poNum,jdbcType=VARCHAR},
        #{entity.oldProductCode,jdbcType=VARCHAR},
        #{entity.relationVersion,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.part.infrastructure.po.PartRelationMapPO">
        update fdp_part_relation_map set
        mapping_id = #{mappingId,jdbcType=VARCHAR},
        product_code = #{productCode,jdbcType=VARCHAR},
        part_number = #{partNumber,jdbcType=VARCHAR},
        part_name = #{partName,jdbcType=VARCHAR},
        effective_start_time = #{effectiveStartTime,jdbcType=TIMESTAMP},
        effective_end_time = #{effectiveEndTime,jdbcType=TIMESTAMP},
        stock_point_id = #{stockPointId,jdbcType=VARCHAR},
        customer_id = #{customerId,jdbcType=VARCHAR},
        source_type = #{sourceType,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER},
        site_id = #{siteId,jdbcType=VARCHAR},
        customer_item_num_alias = #{customerItemNumAlias,jdbcType=VARCHAR},
        po_num = #{poNum,jdbcType=VARCHAR},
        old_product_code = #{oldProductCode,jdbcType=VARCHAR},
        relation_version = #{relationVersion,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.part.infrastructure.po.PartRelationMapPO">
        update fdp_part_relation_map
        <set>
            <if test="item.mappingId != null and item.mappingId != ''">
                mapping_id = #{item.mappingId,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.partNumber != null and item.partNumber != ''">
                part_number = #{item.partNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.partName != null and item.partName != ''">
                part_name = #{item.partName,jdbcType=VARCHAR},
            </if>
            <if test="item.effectiveStartTime != null">
                effective_start_time = #{item.effectiveStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.effectiveEndTime != null">
                effective_end_time = #{item.effectiveEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.customerId != null and item.customerId != ''">
                customer_id = #{item.customerId,jdbcType=VARCHAR},
            </if>
            <if test="item.sourceType != null and item.sourceType != ''">
                source_type = #{item.sourceType,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.siteId != null and item.siteId != ''">
                site_id = #{item.siteId,jdbcType=VARCHAR},
            </if>
            <if test="item.customerItemNumAlias != null and item.customerItemNumAlias != ''">
                customer_item_num_alias = #{item.customerItemNumAlias,jdbcType=VARCHAR},
            </if>
            <if test="item.poNum != null and item.poNum != ''">
                po_num = #{item.poNum,jdbcType=VARCHAR},
            </if>
            <if test="item.oldProductCode != null and item.oldProductCode != ''">
                old_product_code = #{item.oldProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.relationVersion != null and item.relationVersion != ''">
                relation_version = #{item.relationVersion,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_part_relation_map
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="mapping_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.mappingId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="part_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.partNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="part_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.partName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="effective_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effectiveStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="effective_end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effectiveEndTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="customer_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.customerId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="source_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sourceType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="site_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.siteId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="customer_item_num_alias = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.customerItemNumAlias,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="po_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.poNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="old_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oldProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="relation_version = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.relationVersion,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update fdp_part_relation_map 
        <set>
            <if test="item.mappingId != null and item.mappingId != ''">
                mapping_id = #{item.mappingId,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.partNumber != null and item.partNumber != ''">
                part_number = #{item.partNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.partName != null and item.partName != ''">
                part_name = #{item.partName,jdbcType=VARCHAR},
            </if>
            <if test="item.effectiveStartTime != null">
                effective_start_time = #{item.effectiveStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.effectiveEndTime != null">
                effective_end_time = #{item.effectiveEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.customerId != null and item.customerId != ''">
                customer_id = #{item.customerId,jdbcType=VARCHAR},
            </if>
            <if test="item.sourceType != null and item.sourceType != ''">
                source_type = #{item.sourceType,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.siteId != null and item.siteId != ''">
                site_id = #{item.siteId,jdbcType=VARCHAR},
            </if>
            <if test="item.customerItemNumAlias != null and item.customerItemNumAlias != ''">
                customer_item_num_alias = #{item.customerItemNumAlias,jdbcType=VARCHAR},
            </if>
            <if test="item.poNum != null and item.poNum != ''">
                po_num = #{item.poNum,jdbcType=VARCHAR},
            </if>
            <if test="item.oldProductCode != null and item.oldProductCode != ''">
                old_product_code = #{item.oldProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.relationVersion != null and item.relationVersion != ''">
                relation_version = #{item.relationVersion,jdbcType=VARCHAR},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from fdp_part_relation_map where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_part_relation_map where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <!-- 批量id+版本删除 -->
    <delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from fdp_part_relation_map where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>
    <select id="selectByProductCodeAndSourceType"
            resultType="com.yhl.scp.dfp.part.infrastructure.po.PartRelationMapPO">
        SELECT *
        FROM `scp_fysh`.`fdp_part_relation_map`
        WHERE product_code = #{productCode} AND source_type = #{sourceType}
    </select>
    <update id="updateSelectiveById" parameterType="com.yhl.scp.dfp.part.infrastructure.po.PartRelationMapPO">
        UPDATE `scp_fysh`.`fdp_part_relation_map`
        <set>
            <if test="partNumber != null">
                part_number = #{partNumber,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>
