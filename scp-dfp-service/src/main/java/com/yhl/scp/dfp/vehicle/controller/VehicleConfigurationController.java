package com.yhl.scp.dfp.vehicle.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.vehicle.dto.VehicleConfigurationDTO;
import com.yhl.scp.dfp.vehicle.service.VehicleConfigurationService;
import com.yhl.scp.dfp.vehicle.vo.VehicleConfigurationVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>VehicleConfigurationController</code>
 * <p>
 * 车型配置信息控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:49
 */
@Slf4j
@Api(tags = "车型配置信息控制器")
@RestController
@RequestMapping("vehicleConfiguration")
public class VehicleConfigurationController extends BaseController {

    @Resource
    private VehicleConfigurationService vehicleConfigurationService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<VehicleConfigurationVO>> page() {
        List<VehicleConfigurationVO> vehicleConfigurationList = vehicleConfigurationService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<VehicleConfigurationVO> pageInfo = new PageInfo<>(vehicleConfigurationList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody VehicleConfigurationDTO vehicleConfigurationDTO) {
        return vehicleConfigurationService.doCreate(vehicleConfigurationDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody VehicleConfigurationDTO vehicleConfigurationDTO) {
        return vehicleConfigurationService.doUpdate(vehicleConfigurationDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        vehicleConfigurationService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<VehicleConfigurationVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, vehicleConfigurationService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "根据版本删除")
    @PostMapping(value = "deleteByVersion")
    public BaseResponse<Void> deleteByVersion(@RequestBody List<RemoveVersionDTO> versionDTOList) {
        vehicleConfigurationService.doDeleteByVersion(versionDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

}
