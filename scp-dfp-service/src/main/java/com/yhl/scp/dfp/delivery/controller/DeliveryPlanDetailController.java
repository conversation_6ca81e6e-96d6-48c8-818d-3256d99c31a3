package com.yhl.scp.dfp.delivery.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanDetailDTO;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanDetailService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>DeliveryPlanDetailController</code>
 * <p>
 * 发货计划明细控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 13:52:06
 */
@Slf4j
@Api(tags = "发货计划明细控制器")
@RestController
@RequestMapping("deliveryPlanDetail")
public class DeliveryPlanDetailController extends BaseController {

    @Resource
    private DeliveryPlanDetailService deliveryPlanDetailService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<DeliveryPlanDetailVO>> page() {
        List<DeliveryPlanDetailVO> deliveryPlanDetailList = deliveryPlanDetailService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<DeliveryPlanDetailVO> pageInfo = new PageInfo<>(deliveryPlanDetailList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody DeliveryPlanDetailDTO deliveryPlanDetailDTO) {
        return deliveryPlanDetailService.doCreate(deliveryPlanDetailDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody DeliveryPlanDetailDTO deliveryPlanDetailDTO) {
        return deliveryPlanDetailService.doUpdate(deliveryPlanDetailDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        deliveryPlanDetailService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<DeliveryPlanDetailVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, deliveryPlanDetailService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "根据版本删除")
    @PostMapping(value = "deleteByVersion")
    public BaseResponse<Void> deleteByVersion(@RequestBody List<RemoveVersionDTO> versionDTOList) {
        deliveryPlanDetailService.doDeleteByVersion(versionDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }
    
    @ApiOperation(value = "移除修改标识")
    @PostMapping(value = "removeEditSign")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> removeEditSign(@RequestBody List<String> deliveryPlanDataIds) {
        deliveryPlanDetailService.doRemoveEditSign(deliveryPlanDataIds);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }
}
