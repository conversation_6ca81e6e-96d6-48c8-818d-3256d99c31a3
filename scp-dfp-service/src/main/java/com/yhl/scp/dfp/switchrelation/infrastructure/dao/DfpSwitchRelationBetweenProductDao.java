package com.yhl.scp.dfp.switchrelation.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.switchrelation.infrastructure.po.DfpSwitchRelationBetweenProductPO;
import com.yhl.scp.dfp.switchrelation.vo.DfpSwitchRelationBetweenProductVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>DfpSwitchRelationBetweenProductDao</code>
 * <p>
 * 新旧产品工程变更关系DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-19 16:35:26
 */
public interface DfpSwitchRelationBetweenProductDao extends BaseDao<DfpSwitchRelationBetweenProductPO, DfpSwitchRelationBetweenProductVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link DfpSwitchRelationBetweenProductVO}
     */
    List<DfpSwitchRelationBetweenProductVO> selectVOByParams(@Param("params") Map<String, Object> params);

    DfpSwitchRelationBetweenProductVO selectVOByProductCode(@Param("productCode") String productCode);

    List<DfpSwitchRelationBetweenProductVO> selectVOByCodeGroup(@Param("switchCodeGroup") String switchCodeGroup);

    List<DfpSwitchRelationBetweenProductPO> selectByOemAndProduct(@Param("oemCodes") List<String> oemCodes,
                                                                  @Param("productCodes") List<String> productCodes);

}