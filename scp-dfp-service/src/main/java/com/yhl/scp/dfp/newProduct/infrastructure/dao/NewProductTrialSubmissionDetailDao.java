package com.yhl.scp.dfp.newProduct.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.newProduct.infrastructure.po.NewProductTrialSubmissionDetailPO;
import com.yhl.scp.dfp.newProduct.vo.NewProductTrialSubmissionDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>NewProductTrialSubmissionDetailDao</code>
 * <p>
 * 新品试制提报详情DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:25
 */
public interface NewProductTrialSubmissionDetailDao extends BaseDao<NewProductTrialSubmissionDetailPO, NewProductTrialSubmissionDetailVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link NewProductTrialSubmissionDetailVO}
     */
    List<NewProductTrialSubmissionDetailVO> selectVOByParams(@Param("params") Map<String, Object> params);

    /**
     * 根据主表id查询对应的子表详情数据
     * @param submissionIdList
     * @return
     */
    List<NewProductTrialSubmissionDetailVO> selectBySubmissionIdList(@Param("submissionIdList") List<String> submissionIdList);

    /**
     * 根据父表id集合删除子表详情数据
     * @param idList 父表id集合
     * @return
     */
    int deleteBatchBySubmissionIds(@Param("idList")List<String> idList);

    /**
     * 根据父表id删除子表详情数据
     * @param submissionId 父表id
     * @return
     */
    int deleteBySubmissionId(String submissionId);

    /**
     * 根据版本批量删除
     * @param versionDTOList
     * @return
     */
    int deleteBatchVersion(@Param("list") List<RemoveVersionDTO> versionDTOList);

}
