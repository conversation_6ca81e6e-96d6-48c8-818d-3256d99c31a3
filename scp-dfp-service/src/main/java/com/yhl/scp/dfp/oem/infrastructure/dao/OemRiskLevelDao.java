package com.yhl.scp.dfp.oem.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.oem.infrastructure.po.OemRiskLevelPO;
import com.yhl.scp.dfp.oem.vo.OemRiskLevelVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <code>OemRiskLevelDao</code>
 * <p>
 * 主机厂风险等级DAO
 * </p>
 *
 * @version 1.0
 * @since 2024-07-28 21:45:10
 */
public interface OemRiskLevelDao extends BaseDao<OemRiskLevelPO, OemRiskLevelVO> {

    int updateRiskLevelByYearMonth(@Param("oemCode") String oemCode,
                                   @Param("yearMonth") String yearMonth,
                                   @Param("riskLevel") String riskLevel);

    /**
     * 根据版本批量删除
     * @param versionDTOList
     * @return
     */
    int deleteBatchVersion(@Param("list") List<RemoveVersionDTO> versionDTOList);

    /**
     * 根据主机厂查询最新版本
     * @param oemCode
     * @return
     */
    OemRiskLevelPO selectLatestOemRiskLevel(String oemCode);

    void updateByOemCodeAndTime(OemRiskLevelPO oemRiskLevelPO);

	List<OemRiskLevelVO> selectByOemCodes(@Param("oemCodes") List<String> oemCodes);
}
