package com.yhl.scp.dfp.projectForecast.domain.factory;

import com.yhl.scp.dfp.projectForecast.domain.entity.ProjectForecastPresentationDetailDO;
import com.yhl.scp.dfp.projectForecast.dto.ProjectForecastPresentationDetailDTO;
import com.yhl.scp.dfp.projectForecast.infrastructure.dao.ProjectForecastPresentationDetailDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>ProjectForecastPresentationDetailFactory</code>
 * <p>
 * 项目预测提报详情领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-03 16:11:53
 */
@Component
public class ProjectForecastPresentationDetailFactory {

    @Resource
    private ProjectForecastPresentationDetailDao projectForecastPresentationDetailDao;

    ProjectForecastPresentationDetailDO create(ProjectForecastPresentationDetailDTO dto) {
        // TODO
        return null;
    }

}
