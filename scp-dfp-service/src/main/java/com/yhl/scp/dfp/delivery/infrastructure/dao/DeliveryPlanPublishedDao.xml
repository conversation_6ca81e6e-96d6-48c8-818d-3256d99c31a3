<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanPublishedDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedPO">
        <!--@Table fdp_delivery_plan_published-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="KID" jdbcType="INTEGER" property="KID"/>
        <result column="delivery_version_id" jdbcType="VARCHAR" property="deliveryVersionId"/>
        <result column="delivery_plan_data_id" jdbcType="VARCHAR" property="deliveryPlanDataId"/>
        <result column="demand_category" jdbcType="VARCHAR" property="demandCategory"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="demand_time" jdbcType="TIMESTAMP" property="demandTime"/>
        <result column="demand_quantity" jdbcType="INTEGER" property="demandQuantity"/>
        <result column="box_quantity" jdbcType="INTEGER" property="boxQuantity"/>
        <result column="publisher" jdbcType="VARCHAR" property="publisher"/>
        <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO">
        <result column="demand_version_id" jdbcType="VARCHAR" property="demandVersionId"/>
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,KID
        ,delivery_version_id,delivery_plan_data_id,demand_category,oem_code,product_code,demand_time,demand_quantity,box_quantity,publisher,publish_time,remark,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,demand_version_id,oem_name,vehicle_model_code
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.KID != null and params.KID != ''">
                and KID = #{params.KID,jdbcType=INTEGER}
            </if>
            <if test="params.deliveryVersionId != null and params.deliveryVersionId != ''">
                and delivery_version_id = #{params.deliveryVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.deliveryPlanDataId != null and params.deliveryPlanDataId != ''">
                and delivery_plan_data_id = #{params.deliveryPlanDataId,jdbcType=VARCHAR}
            </if>
            <if test="params.demandCategory != null and params.demandCategory != ''">
                and demand_category = #{params.demandCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCodes != null and params.oemCodes.size() > 0">
                and oem_code in
                <foreach collection="params.oemCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and product_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.productCodeLike != null and params.productCodeLike != ''">
                and product_code like #{params.productCodeLike,jdbcType=VARCHAR}
            </if>
            <if test="params.demandTime != null">
                and demand_time = #{params.demandTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.demandTimeStart != null">
                and demand_time >= #{params.demandTimeStart,jdbcType=VARCHAR}
            </if>
            <if test="params.demandTimeList != null and params.demandTimeList.size() > 0">
                and demand_time in
                <foreach collection="params.demandTimeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=TIMESTAMP}
                </foreach>
            </if>
            <if test="params.demandTimes != null and params.demandTimes.size() > 0">
                and demand_time in
                <foreach collection="params.demandTimes" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.demandTimeStrs != null and params.demandTimeStrs.size() > 0">
                and date_format(demand_time,'%Y-%m-%d') in
                <foreach collection="params.demandTimeStrs" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.startTime != null and params.endTime != null">
                and demand_time BETWEEN #{params.startTime,jdbcType=TIMESTAMP} AND #{params.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.startTimeStr != null and params.startTimeStr != '' and params.endTimeStr != null and params.endTimeStr != ''">
                and date_format(demand_time,'%Y-%m-%d %H:%i:%s') BETWEEN #{params.startTimeStr,jdbcType=VARCHAR} AND
                #{params.endTimeStr,jdbcType=VARCHAR}
            </if>
            <if test="params.startTimeStrYMD != null and params.startTimeStrYMD != '' and params.endTimeStrYMD != null and params.endTimeStrYMD != ''">
                and date_format(demand_time,'%Y-%m-%d') <![CDATA[ >= ]]>  #{params.startTimeStrYMD,jdbcType=VARCHAR}
            </if>
            <if test="params.endTimeStrYMD != null and params.endTimeStrYMD != ''">
                AND date_format(demand_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{params.endTimeStrYMD,jdbcType=VARCHAR}
            </if>
            <if test="params.demandQuantity != null">
                and demand_quantity = #{params.demandQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.demandQuantityGTZero != null and params.demandQuantityGTZero=='YES'">
                and demand_quantity > 0
            </if>
            <if test="params.boxQuantity != null">
                and box_quantity = #{params.boxQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.publisher != null and params.publisher != ''">
                and publisher = #{params.publisher,jdbcType=VARCHAR}
            </if>
            <if test="params.publishTime != null">
                and publish_time = #{params.publishTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.vehicleModelCodeLike != null and params.vehicleModelCodeLike != ''">
                and vehicle_model_code like #{params.vehicleModelCodeLike,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_delivery_plan_published
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_delivery_plan_published
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_fdp_delivery_plan_published
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_delivery_plan_published
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_fdp_delivery_plan_published
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 查询KID最大值 -->
    <select id="selectMaxKID" resultType="java.lang.Integer">
        SELECT MAX(KID)
        FROM fdp_delivery_plan_published;
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_delivery_plan_published(
        id,
        KID,
        delivery_version_id,
        delivery_plan_data_id,
        demand_category,
        oem_code,
        product_code,
        demand_time,
        demand_quantity,
        box_quantity,
        publisher,
        publish_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
            #{KID,jdbcType=INTEGER},
        #{deliveryVersionId,jdbcType=VARCHAR},
        #{deliveryPlanDataId,jdbcType=VARCHAR},
        #{demandCategory,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{demandTime,jdbcType=TIMESTAMP},
        #{demandQuantity,jdbcType=INTEGER},
        #{boxQuantity,jdbcType=INTEGER},
        #{publisher,jdbcType=VARCHAR},
        #{publishTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedPO">
        insert into fdp_delivery_plan_published(id,
                                                KID,
                                                delivery_version_id,
                                                delivery_plan_data_id,
                                                demand_category,
                                                oem_code,
                                                product_code,
                                                demand_time,
                                                demand_quantity,
                                                box_quantity,
                                                publisher,
                                                publish_time,
                                                remark,
                                                enabled,
                                                creator,
                                                create_time,
                                                modifier,
                                                modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{KID,jdbcType=INTEGER},
                #{deliveryVersionId,jdbcType=VARCHAR},
                #{deliveryPlanDataId,jdbcType=VARCHAR},
                #{demandCategory,jdbcType=VARCHAR},
                #{oemCode,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{demandTime,jdbcType=TIMESTAMP},
                #{demandQuantity,jdbcType=INTEGER},
                #{boxQuantity,jdbcType=INTEGER},
                #{publisher,jdbcType=VARCHAR},
                #{publishTime,jdbcType=TIMESTAMP},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_delivery_plan_published(
        id,
        KID,
        delivery_version_id,
        delivery_plan_data_id,
        demand_category,
        oem_code,
        product_code,
        demand_time,
        demand_quantity,
        box_quantity,
        publisher,
        publish_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
             #{entity.KID,jdbcType=INTEGER},
            #{entity.deliveryVersionId,jdbcType=VARCHAR},
            #{entity.deliveryPlanDataId,jdbcType=VARCHAR},
            #{entity.demandCategory,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.demandTime,jdbcType=TIMESTAMP},
            #{entity.demandQuantity,jdbcType=INTEGER},
            #{entity.boxQuantity,jdbcType=INTEGER},
            #{entity.publisher,jdbcType=VARCHAR},
            #{entity.publishTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="KID">
        insert into fdp_delivery_plan_published(
        id,
        KID,
        delivery_version_id,
        delivery_plan_data_id,
        demand_category,
        oem_code,
        product_code,
        demand_time,
        demand_quantity,
        box_quantity,
        publisher,
        publish_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
             #{entity.KID,jdbcType=INTEGER},
            #{entity.deliveryVersionId,jdbcType=VARCHAR},
            #{entity.deliveryPlanDataId,jdbcType=VARCHAR},
            #{entity.demandCategory,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.demandTime,jdbcType=TIMESTAMP},
            #{entity.demandQuantity,jdbcType=INTEGER},
            #{entity.boxQuantity,jdbcType=INTEGER},
            #{entity.publisher,jdbcType=VARCHAR},
            #{entity.publishTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedPO">
        update fdp_delivery_plan_published
        set delivery_version_id   = #{deliveryVersionId,jdbcType=VARCHAR},
            KID                   = #{KID,jdbcType=INTEGER},
            delivery_plan_data_id = #{deliveryPlanDataId,jdbcType=VARCHAR},
            demand_category       = #{demandCategory,jdbcType=VARCHAR},
            oem_code              = #{oemCode,jdbcType=VARCHAR},
            product_code          = #{productCode,jdbcType=VARCHAR},
            demand_time           = #{demandTime,jdbcType=TIMESTAMP},
            demand_quantity       = #{demandQuantity,jdbcType=INTEGER},
            box_quantity          = #{boxQuantity,jdbcType=INTEGER},
            publisher             = #{publisher,jdbcType=VARCHAR},
            publish_time          = #{publishTime,jdbcType=TIMESTAMP},
            remark                = #{remark,jdbcType=VARCHAR},
            enabled               = #{enabled,jdbcType=VARCHAR},
            modifier              = #{modifier,jdbcType=VARCHAR},
            modify_time           = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedPO">
        update fdp_delivery_plan_published
        <set>
            <if test="item.KID != null and item.KID != ''">
                KID = #{item.KID,jdbcType=INTEGER},
            </if>
            <if test="item.deliveryVersionId != null and item.deliveryVersionId != ''">
                delivery_version_id = #{item.deliveryVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryPlanDataId != null and item.deliveryPlanDataId != ''">
                delivery_plan_data_id = #{item.deliveryPlanDataId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandCategory != null and item.demandCategory != ''">
                demand_category = #{item.demandCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.demandTime != null">
                demand_time = #{item.demandTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.demandQuantity != null">
                demand_quantity = #{item.demandQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.boxQuantity != null">
                box_quantity = #{item.boxQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.publisher != null and item.publisher != ''">
                publisher = #{item.publisher,jdbcType=VARCHAR},
            </if>
            <if test="item.publishTime != null">
                publish_time = #{item.publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_delivery_plan_published
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="KID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.KID,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="delivery_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="delivery_plan_data_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryPlanDataId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="demand_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="box_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.boxQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="publisher = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.publisher,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="publish_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.publishTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update fdp_delivery_plan_published
            <set>
                <if test="item.KID != null and item.KID != ''">
                    KID = #{item.KID,jdbcType=INTEGER},
                </if>
                <if test="item.deliveryVersionId != null and item.deliveryVersionId != ''">
                    delivery_version_id = #{item.deliveryVersionId,jdbcType=VARCHAR},
                </if>
                <if test="item.deliveryPlanDataId != null and item.deliveryPlanDataId != ''">
                    delivery_plan_data_id = #{item.deliveryPlanDataId,jdbcType=VARCHAR},
                </if>
                <if test="item.demandCategory != null and item.demandCategory != ''">
                    demand_category = #{item.demandCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.oemCode != null and item.oemCode != ''">
                    oem_code = #{item.oemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.demandTime != null">
                    demand_time = #{item.demandTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.demandQuantity != null">
                    demand_quantity = #{item.demandQuantity,jdbcType=INTEGER},
                </if>
                <if test="item.boxQuantity != null">
                    box_quantity = #{item.boxQuantity,jdbcType=INTEGER},
                </if>
                <if test="item.publisher != null and item.publisher != ''">
                    publisher = #{item.publisher,jdbcType=VARCHAR},
                </if>
                <if test="item.publishTime != null">
                    publish_time = #{item.publishTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdp_delivery_plan_published
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_delivery_plan_published where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="selectCurrentMonthVOByItemCodes"
            resultType="com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedMonthVO">
        SELECT
			oem_code AS oemCode,
			product_code AS productCode,
			DATE_FORMAT( demand_time, '%Y%m' ) AS yearMonth,
			SUM( demand_quantity ) AS sumDemandQuantity
		FROM
			fdp_delivery_plan_published
		WHERE
			enabled = 'YES'
			AND DATE_FORMAT( demand_time, '%Y%m' ) = DATE_FORMAT( NOW(), '%Y%m' )
			<if test="oemCodeList != null and oemCodeList.size() > 0">
                and oem_code in
                <foreach collection="oemCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="productCodeList != null and productCodeList.size() > 0">
                and product_code in
                <foreach collection="productCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
             <if test="startDemandTime != null">
                and demand_time <![CDATA[ >= ]]> #{startDemandTime}
            </if>
             <if test="endDemandTime != null">
                and demand_time <![CDATA[ <= ]]> #{endDemandTime}
            </if>
		GROUP BY
			oem_code,
			product_code,
			DATE_FORMAT(demand_time, '%Y%m')
    </select>

    <select id="selectSumDeliveryPlanPublished" resultMap="BaseResultMap">
        SELECT
			product_code,
			SUM( demand_quantity ) demand_quantity
		FROM
			fdp_delivery_plan_published
		WHERE
			enabled = 'YES'
			<if test="params.productCodeList != null and params.productCodeList.size() > 0">
                and product_code in
                <foreach collection="params.productCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.startDemandTime != null">
                and demand_time <![CDATA[ >= ]]> #{params.startDemandTime}
            </if>
             <if test="params.endDemandTime != null">
                and demand_time <![CDATA[ <= ]]> #{params.endDemandTime}
            </if>
		GROUP BY
			product_code
    </select>

    <select id="selectProductListByParamOnDynamicColumns" resultMap="VOResultMap">
        select ${dynamicColumn}
        from v_fdp_delivery_plan_published
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectDeliveryPlan14days" resultMap="VOResultMap">
--         WITH
--         latest_version AS (
--         SELECT id
--         FROM fdp_delivery_plan_version
--         where version_code is not null
--         ORDER BY create_time DESC
--         LIMIT 1
--         ),
--         min_time AS (
--         SELECT MIN(demand_time) AS start_time
--         FROM fdp_delivery_plan_published
--         WHERE delivery_version_id = (SELECT id FROM latest_version)
--         )
        SELECT f.*
        FROM fdp_delivery_plan_published f
        WHERE
        and f.demand_time <![CDATA[ >= ]]> m.start_time
        AND f.demand_time <![CDATA[ <= ]]> m.start_time + INTERVAL 14 DAY;

    </select>

    <select id="selectDayVOByItemCodes"
            resultType="com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedDayVO">
        SELECT
			oem_code AS oemCode,
			product_code AS productCode,
			DATE_FORMAT( demand_time, '%Y-%m-%d' ) AS monthDay,
			SUM( demand_quantity ) AS sumDemandQuantity
		FROM
			fdp_delivery_plan_published
		WHERE
			enabled = 'YES'
			AND DATE_FORMAT( demand_time, '%Y%m' ) = DATE_FORMAT( NOW(), '%Y%m' )
			<if test="oemCodeList != null and oemCodeList.size() > 0">
                and oem_code in
                <foreach collection="oemCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="productCodeList != null and productCodeList.size() > 0">
                and product_code in
                <foreach collection="productCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
             <if test="startDemandTime != null">
                and demand_time <![CDATA[ >= ]]> #{startDemandTime}
            </if>
             <if test="endDemandTime != null">
                and demand_time <![CDATA[ <= ]]> #{endDemandTime}
            </if>
		GROUP BY
			oem_code,
			product_code,
			DATE_FORMAT(demand_time, '%Y-%m-%d')
    </select>

    <select id="selectCountVOByParams" resultType="java.lang.Long">
        select count(distinct product_code)
        from v_fdp_delivery_plan_published
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectDistinctProductCodeByParams"  resultType="java.lang.String">
        select distinct product_code
        <include refid="VO_Column_List"/>
        from v_fdp_delivery_plan_published
        <include refid="Base_Where_Condition"/>
    </select>
</mapper>
