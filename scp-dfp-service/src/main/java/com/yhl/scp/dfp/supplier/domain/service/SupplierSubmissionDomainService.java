package com.yhl.scp.dfp.supplier.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.dfp.common.constants.AbnormalConstants;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.supplier.domain.entity.SupplierSubmissionDO;
import com.yhl.scp.dfp.supplier.infrastructure.dao.SupplierSubmissionDao;
import com.yhl.scp.dfp.supplier.infrastructure.po.SupplierSubmissionPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <code>SupplierSubmissionDomainService</code>
 * <p>
 * 车型全供应商提报领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-10 09:20:50
 */
@Service
public class SupplierSubmissionDomainService {

    @Resource
    private SupplierSubmissionDao supplierSubmissionDao;

    /**
     * 数据校验
     *
     * @param supplierSubmissionDO 领域对象
     */
    public void validation(SupplierSubmissionDO supplierSubmissionDO) {
        checkNotNull(supplierSubmissionDO);
        checkUniqueCode(supplierSubmissionDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param supplierSubmissionDO 领域对象
     */
    private void checkNotNull(SupplierSubmissionDO supplierSubmissionDO) {
        if (StringUtils.isBlank(supplierSubmissionDO.getOemCode())) {
            throw new BusinessException("车型全供应商提报主机厂编码不能为空");
        }
        if (StringUtils.isBlank(supplierSubmissionDO.getProductLineCode())) {
            throw new BusinessException("车型全供应商提报产线编码不能为空");
        }
        if (StringUtils.isBlank(supplierSubmissionDO.getVehicleModelCode())) {
            throw new BusinessException("车型全供应商提报车型编码不能为空");
        }
    }

    /**
     * 唯一性校验
     *
     * @param supplierSubmissionDO 领域对象
     */
    private void checkUniqueCode(SupplierSubmissionDO supplierSubmissionDO) {
        Map<String, Object> params = new HashMap<>(4);
        params.put("oemCode", supplierSubmissionDO.getOemCode());
        params.put("productLineCode", supplierSubmissionDO.getProductLineCode());
        params.put("vehicleModelCode", supplierSubmissionDO.getVehicleModelCode());
        if (StringUtils.isBlank(supplierSubmissionDO.getId())) {
            List<SupplierSubmissionPO> list = supplierSubmissionDao.selectByParams(params);
            if (CollectionUtils.isNotEmpty(list)) {
                throw new BusinessException("新增失败，车型全供应商提报组合已存在：" + supplierSubmissionDO.getOemCode() + Constants.DELIMITER + supplierSubmissionDO.getProductLineCode() + Constants.DELIMITER + supplierSubmissionDO.getVehicleModelCode());
            }
        } else {
            SupplierSubmissionPO old = supplierSubmissionDao.selectByPrimaryKey(supplierSubmissionDO.getId());
            if (!supplierSubmissionDO.getOemCode().equals(old.getOemCode()) && !supplierSubmissionDO.getProductLineCode().equals(old.getProductLineCode()) && !supplierSubmissionDO.getVehicleModelCode().equals(old.getVehicleModelCode())) {
                List<SupplierSubmissionPO> list = supplierSubmissionDao.selectByParams(params);
                if (CollectionUtils.isNotEmpty(list)) {
                    throw new BusinessException("修改失败，车型全供应商提报组合已存在：" + supplierSubmissionDO.getOemCode() + Constants.DELIMITER + supplierSubmissionDO.getProductLineCode() + Constants.DELIMITER + supplierSubmissionDO.getVehicleModelCode());
                }
            }
        }
    }

    /**
     * 根据版本号批量删除
     *
     * @param versionDTOList
     */
    public void checkDelete(List<RemoveVersionDTO> versionDTOList) {

        for (RemoveVersionDTO removeVersionDTO : versionDTOList) {
            SupplierSubmissionPO old = supplierSubmissionDao.selectByPrimaryKey(removeVersionDTO.getId());
            if (Objects.isNull(old)) {
                throw new BusinessException(AbnormalConstants.DELETE_EXCEPTION_MSG + removeVersionDTO.getId());
            }
            if (removeVersionDTO.getVersionValue() == null || !removeVersionDTO.getVersionValue().equals(old.getVersionValue())) {
                throw new BusinessException(AbnormalConstants.DELETE_VERSION_EXCEPTION_MSG);
            }
        }
    }

}
