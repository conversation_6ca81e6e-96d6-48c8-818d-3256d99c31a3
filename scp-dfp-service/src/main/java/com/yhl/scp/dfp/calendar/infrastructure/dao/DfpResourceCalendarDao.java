package com.yhl.scp.dfp.calendar.infrastructure.dao;


import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.calendar.infrastructure.po.ResourceCalendarPO;
import com.yhl.scp.dfp.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.mds.extension.feign.dto.ResourceCalendarParamDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <code>ResourceCalendarDao</code>
 * <p>
 * 资源日历DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-07 16:06:22
 */
    public interface DfpResourceCalendarDao extends BaseDao<ResourceCalendarPO, ResourceCalendarVO> {
    /**
     * 删除通过ResourceIds
     */
    void deleteByResourceIds(@Param("ids") List<String> ids);

    /**
     * 通过资源id和日期搜索查询
     */
    List<ResourceCalendarPO> selectByResourceIdsAndDate(@Param("standardResourceIds") List<String> standardResourceIds,
                                                        @Param("physicalResourceIds") List<String> physicalResourceIds,
                                                        @Param("startDate") Date startDate,
                                                        @Param("endDate") Date endDate
    );

    /**
     * 通过资源id和日期搜索查询VO
     */
    List<ResourceCalendarVO> selectVOByParamDTO(@Param("paramDTO") ResourceCalendarParamDTO paramDTO);

    void deleteAll();

    /**
     * 查询资源重建重叠的日历
     *
     * @param physicalResourceId
     * @param startDate
     * @param endDate
     * @return
     */
    List<ResourceCalendarPO> selectOverlapByResourceIdsAndDate(@Param("physicalResourceId") String physicalResourceId,
                                                               @Param("startDate") Date startDate,
                                                               @Param("endDate") Date endDate
    );

    List<ResourceCalendarVO> selectOemLatestMonths(@Param("oemCodes")List<String> oemCodes, @Param("months")List<String> months);

    List<ResourceCalendarVO> selectLatestMonthsByVehicle(@Param("vehicles")List<String> vehicles,
                                                 @Param("month")String months);
}
