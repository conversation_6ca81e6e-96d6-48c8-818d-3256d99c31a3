package com.yhl.scp.dfp.vehicle.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class VehicleModelMarketInventoryDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 655904151052473260L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 全球车型名称
     */
    private String vehicleModelName;
    /**
     * 车型市场保有量
     */
    private Integer inventoryQuantity;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    private String enabled;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 修改时间
     */
    private Date modifyTime;
    /**
     * 版本
     */
    private Integer versionValue;

}
