package com.yhl.scp.dfp.oem.convertor;

import com.yhl.scp.dfp.oem.domain.entity.OemProductLineDO;
import com.yhl.scp.dfp.oem.dto.OemProductLineDTO;
import com.yhl.scp.dfp.oem.infrastructure.po.OemProductLinePO;
import com.yhl.scp.dfp.oem.vo.OemProductLineVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>OemProductLineConvertor</code>
 * <p>
 * 主机厂产线资源转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:48:08
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, builder = @org.mapstruct.Builder(disableBuilder = true))
public interface OemProductLineConvertor {

    OemProductLineConvertor INSTANCE = Mappers.getMapper(OemProductLineConvertor.class);

    OemProductLineDO dto2Do(OemProductLineDTO obj);

    List<OemProductLineDO> dto2Dos(List<OemProductLineDTO> list);

    OemProductLineDTO do2Dto(OemProductLineDO obj);

    List<OemProductLineDTO> do2Dtos(List<OemProductLineDO> list);

    OemProductLineDTO vo2Dto(OemProductLineVO obj);

    List<OemProductLineDTO> vo2Dtos(List<OemProductLineVO> list);

    OemProductLineVO po2Vo(OemProductLinePO obj);

    List<OemProductLineVO> po2Vos(List<OemProductLinePO> list);

    OemProductLinePO dto2Po(OemProductLineDTO obj);

    List<OemProductLinePO> dto2Pos(List<OemProductLineDTO> obj);

    OemProductLineVO do2Vo(OemProductLineDO obj);

    OemProductLinePO do2Po(OemProductLineDO obj);

    OemProductLineDO po2Do(OemProductLinePO obj);

}
