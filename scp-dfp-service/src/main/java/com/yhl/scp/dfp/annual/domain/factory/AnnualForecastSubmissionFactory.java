package com.yhl.scp.dfp.annual.domain.factory;

import com.yhl.scp.dfp.annual.domain.entity.AnnualForecastSubmissionDO;
import com.yhl.scp.dfp.annual.dto.AnnualForecastSubmissionDTO;
import com.yhl.scp.dfp.annual.infrastructure.dao.AnnualForecastSubmissionDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class AnnualForecastSubmissionFactory {

    @Resource
    private AnnualForecastSubmissionDao annualForecastSubmissionDao;

    AnnualForecastSubmissionDO create(AnnualForecastSubmissionDTO dto) {
        // TODO
        return null;
    }

}
