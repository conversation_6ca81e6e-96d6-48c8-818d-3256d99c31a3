package com.yhl.scp.dfp.delivery.domain.service;

import com.yhl.scp.dfp.delivery.domain.entity.DeliveryPlanDetailDO;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanDetailDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>DeliveryPlanDetailDomainService</code>
 * <p>
 * 发货计划明细领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 13:52:07
 */
@Service
public class DeliveryPlanDetailDomainService {

    @Resource
    private DeliveryPlanDetailDao deliveryPlanDetailDao;

    /**
     * 数据校验
     *
     * @param deliveryPlanDetailDO 领域对象
     */
    public void validation(DeliveryPlanDetailDO deliveryPlanDetailDO) {
        checkNotNull(deliveryPlanDetailDO);
        checkUniqueCode(deliveryPlanDetailDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param deliveryPlanDetailDO 领域对象
     */
    private void checkNotNull(DeliveryPlanDetailDO deliveryPlanDetailDO) {
        // if (StringUtils.isBlank(deliveryPlanDetailDO.getDeliveryPlanDetailCode())) {
        //     throw new BusinessException("发货计划明细代码，不能为空");
        // }
        // if (StringUtils.isBlank(deliveryPlanDetailDO.getDeliveryPlanDetailName())) {
        //     throw new BusinessException("发货计划明细名称，不能为空");
        // }
    }

    /**
     * 唯一性校验
     *
     * @param deliveryPlanDetailDO 领域对象
     */
    private void checkUniqueCode(DeliveryPlanDetailDO deliveryPlanDetailDO) {
        // Map<String, Object> params = new HashMap<>(4);
        // params.put("deliveryPlanDetailCode", deliveryPlanDetailDO.getDeliveryPlanDetailCode());
        // if (StringUtils.isBlank(deliveryPlanDetailDO.getId())) {
        //     List<DeliveryPlanDetailPO> list = deliveryPlanDetailDao.selectByParams(params);
        //     if (CollectionUtils.isNotEmpty(list)) {
        //         throw new BusinessException("新增失败，发货计划明细代码已存在：" + deliveryPlanDetailDO.getDeliveryPlanDetailCode());
        //     }
        // } else {
        //     DeliveryPlanDetailPO old = deliveryPlanDetailDao.selectByPrimaryKey(deliveryPlanDetailDO.getId());
        //     if (!deliveryPlanDetailDO.getDeliveryPlanDetailCode().equals(old.getDeliveryPlanDetailCode())) {
        //         List<DeliveryPlanDetailPO> list = deliveryPlanDetailDao.selectByParams(params);
        //         if (CollectionUtils.isNotEmpty(list)) {
        //             throw new BusinessException("修改失败，发货计划明细代码已存在：" + deliveryPlanDetailDO.getDeliveryPlanDetailCode());
        //         }
        //     }
        // }
    }

}
