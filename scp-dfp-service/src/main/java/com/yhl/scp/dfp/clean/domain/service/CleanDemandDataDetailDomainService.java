package com.yhl.scp.dfp.clean.domain.service;

import com.yhl.scp.dfp.clean.domain.entity.CleanDemandDataDetailDO;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanDemandDataDetailDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>CleanDemandDataDetailDomainService</code>
 * <p>
 * 日需求数据明细领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-01 16:39:56
 */
@Service
public class CleanDemandDataDetailDomainService {

    @Resource
    private CleanDemandDataDetailDao cleanDemandDataDetailDao;

    /**
     * 数据校验
     *
     * @param cleanDemandDataDetailDO 领域对象
     */
    public void validation(CleanDemandDataDetailDO cleanDemandDataDetailDO) {
        checkNotNull(cleanDemandDataDetailDO);
        checkUniqueCode(cleanDemandDataDetailDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param cleanDemandDataDetailDO 领域对象
     */
    private void checkNotNull(CleanDemandDataDetailDO cleanDemandDataDetailDO) {
        // if (StringUtils.isBlank(cleanDemandDataDetailDO.getCleanDemandDataDetailCode())) {
        //     throw new BusinessException("日需求数据明细代码，不能为空");
        // }
        // if (StringUtils.isBlank(cleanDemandDataDetailDO.getCleanDemandDataDetailName())) {
        //     throw new BusinessException("日需求数据明细名称，不能为空");
        // }
    }

    /**
     * 唯一性校验
     *
     * @param cleanDemandDataDetailDO 领域对象
     */
    private void checkUniqueCode(CleanDemandDataDetailDO cleanDemandDataDetailDO) {
        // Map<String, Object> params = new HashMap<>(4);
        // params.put("cleanDemandDataDetailCode", cleanDemandDataDetailDO.getCleanDemandDataDetailCode());
        // if (StringUtils.isBlank(cleanDemandDataDetailDO.getId())) {
        //     List<CleanDemandDataDetailPO> list = cleanDemandDataDetailDao.selectByParams(params);
        //     if (CollectionUtils.isNotEmpty(list)) {
        //         throw new BusinessException("新增失败，日需求数据明细代码已存在：" + cleanDemandDataDetailDO.getCleanDemandDataDetailCode());
        //     }
        // } else {
        //     CleanDemandDataDetailPO old = cleanDemandDataDetailDao.selectByPrimaryKey(cleanDemandDataDetailDO.getId());
        //     if (!cleanDemandDataDetailDO.getCleanDemandDataDetailCode().equals(old.getCleanDemandDataDetailCode())) {
        //         List<CleanDemandDataDetailPO> list = cleanDemandDataDetailDao.selectByParams(params);
        //         if (CollectionUtils.isNotEmpty(list)) {
        //             throw new BusinessException("修改失败，日需求数据明细代码已存在：" + cleanDemandDataDetailDO.getCleanDemandDataDetailCode());
        //         }
        //     }
        // }
    }

}
