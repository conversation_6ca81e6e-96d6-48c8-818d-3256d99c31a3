package com.yhl.scp.dfp.stock.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>OriginalFilmCurrentBatchQuantityPO</code>
 * <p>
 * 原片库存批次明细数据PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-29 11:26:59
 */
public class OriginalFilmCurrentBatchQuantityPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -52552931119586008L;

    /**
     * 组织
     */
    private String organization;
    /**
     * 子库存
     */
    private String subInventory;
    /**
     * 货位
     */
    private String location;
    /**
     * 原片编码
     */
    private String originalFilmCode;
    /**
     * 颜色
     */
    private String color;
    /**
     * 产品规格
     */
    private String productSpec;
    /**
     * 厚度
     */
    private BigDecimal thickness;
    /**
     * 等级
     */
    private String grade;
    /**
     * 批次等级代码
     */
    private String batchGradeCode;
    /**
     * 批次号
     */
    private String batchNumber;
    /**
     * 片/箱
     */
    private BigDecimal piecesPerBox;
    /**
     * 吨/箱
     */
    private BigDecimal tonsPerBox;
    /**
     * 客户
     */
    private String customer;
    /**
     * 现存件数
     */
    private Integer currentPieces;
    /**
     * 吨数
     */
    private BigDecimal currentTons;
    /**
     * 入库时间
     */
    private Date storageTime;
    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public String getSubInventory() {
        return subInventory;
    }

    public void setSubInventory(String subInventory) {
        this.subInventory = subInventory;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getOriginalFilmCode() {
        return originalFilmCode;
    }

    public void setOriginalFilmCode(String originalFilmCode) {
        this.originalFilmCode = originalFilmCode;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getProductSpec() {
        return productSpec;
    }

    public void setProductSpec(String productSpec) {
        this.productSpec = productSpec;
    }

    public BigDecimal getThickness() {
        return thickness;
    }

    public void setThickness(BigDecimal thickness) {
        this.thickness = thickness;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getBatchGradeCode() {
        return batchGradeCode;
    }

    public void setBatchGradeCode(String batchGradeCode) {
        this.batchGradeCode = batchGradeCode;
    }

    public String getBatchNumber() {
        return batchNumber;
    }

    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }

    public BigDecimal getPiecesPerBox() {
        return piecesPerBox;
    }

    public void setPiecesPerBox(BigDecimal piecesPerBox) {
        this.piecesPerBox = piecesPerBox;
    }

    public BigDecimal getTonsPerBox() {
        return tonsPerBox;
    }

    public void setTonsPerBox(BigDecimal tonsPerBox) {
        this.tonsPerBox = tonsPerBox;
    }

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public Integer getCurrentPieces() {
        return currentPieces;
    }

    public void setCurrentPieces(Integer currentPieces) {
        this.currentPieces = currentPieces;
    }

    public BigDecimal getCurrentTons() {
        return currentTons;
    }

    public void setCurrentTons(BigDecimal currentTons) {
        this.currentTons = currentTons;
    }

    public Date getStorageTime() {
        return storageTime;
    }

    public void setStorageTime(Date storageTime) {
        this.storageTime = storageTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

}
