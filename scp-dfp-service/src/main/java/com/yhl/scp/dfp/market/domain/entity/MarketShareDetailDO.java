package com.yhl.scp.dfp.market.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MarketShareDetailDO</code>
 * <p>
 * 市场占有率详情DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-26 17:24:43
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MarketShareDetailDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 353642927810202704L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 主表ID
     */
    private String marketShareId;
    /**
     * 年月
     */
    private Date saleTime;
    /**
     * 市场占有率
     */
    private String shareRate;

}
