package com.yhl.scp.dfp.industry.domain.factory;

import com.yhl.scp.dfp.industry.domain.entity.IndustryInfoDO;
import com.yhl.scp.dfp.industry.dto.IndustryInfoDTO;
import com.yhl.scp.dfp.industry.infrastructure.dao.IndustryInfoDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>IndustryInfoFactory</code>
 * <p>
 * 行业资讯搜索领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-25 15:41:30
 */
@Component
public class IndustryInfoFactory {

    @Resource
    private IndustryInfoDao industryInfoDao;

    IndustryInfoDO create(IndustryInfoDTO dto) {
        // TODO
        return null;
    }

}
