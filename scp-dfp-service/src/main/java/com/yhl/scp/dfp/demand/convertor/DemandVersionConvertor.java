package com.yhl.scp.dfp.demand.convertor;

import com.yhl.scp.dfp.demand.domain.entity.DemandVersionDO;
import com.yhl.scp.dfp.demand.dto.DemandVersionDTO;
import com.yhl.scp.dfp.demand.infrastructure.po.DemandVersionPO;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>DemandVersionConvertor</code>
 * <p>
 * 需求版本转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:11
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, builder = @org.mapstruct.Builder(disableBuilder = true))
public interface DemandVersionConvertor {

    DemandVersionConvertor INSTANCE = Mappers.getMapper(DemandVersionConvertor.class);

    DemandVersionDO dto2Do(DemandVersionDTO obj);

    List<DemandVersionDO> dto2Dos(List<DemandVersionDTO> list);

    DemandVersionDTO do2Dto(DemandVersionDO obj);

    List<DemandVersionDTO> do2Dtos(List<DemandVersionDO> list);

    DemandVersionDTO vo2Dto(DemandVersionVO obj);

    List<DemandVersionDTO> vo2Dtos(List<DemandVersionVO> list);

    DemandVersionVO po2Vo(DemandVersionPO obj);

    List<DemandVersionVO> po2Vos(List<DemandVersionPO> list);

    DemandVersionPO dto2Po(DemandVersionDTO obj);

    List<DemandVersionPO> dto2Pos(List<DemandVersionDTO> obj);

    DemandVersionVO do2Vo(DemandVersionDO obj);

    DemandVersionPO do2Po(DemandVersionDO obj);

    DemandVersionDO po2Do(DemandVersionPO obj);

}
