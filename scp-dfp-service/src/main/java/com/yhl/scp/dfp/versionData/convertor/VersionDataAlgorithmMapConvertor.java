package com.yhl.scp.dfp.versionData.convertor;

import com.yhl.scp.dfp.versionData.domain.entity.VersionDataAlgorithmMapDO;
import com.yhl.scp.dfp.versionData.dto.VersionDataAlgorithmMapDTO;
import com.yhl.scp.dfp.versionData.infrastructure.po.VersionDataAlgorithmMapPO;
import com.yhl.scp.dfp.versionData.vo.VersionDataAlgorithmMapVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>VersionDataAlgorithmMapConvertor</code>
 * <p>
 * 预测算法版本数据映射关系转换器
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 17:24:25
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VersionDataAlgorithmMapConvertor {

    VersionDataAlgorithmMapConvertor INSTANCE = Mappers.getMapper(VersionDataAlgorithmMapConvertor.class);

    VersionDataAlgorithmMapDO dto2Do(VersionDataAlgorithmMapDTO obj);

    VersionDataAlgorithmMapDTO do2Dto(VersionDataAlgorithmMapDO obj);

    List<VersionDataAlgorithmMapDO> dto2Dos(List<VersionDataAlgorithmMapDTO> list);

    List<VersionDataAlgorithmMapDTO> do2Dtos(List<VersionDataAlgorithmMapDO> list);

    VersionDataAlgorithmMapVO do2Vo(VersionDataAlgorithmMapDO obj);

    VersionDataAlgorithmMapVO po2Vo(VersionDataAlgorithmMapPO obj);

    List<VersionDataAlgorithmMapVO> po2Vos(List<VersionDataAlgorithmMapPO> list);

    VersionDataAlgorithmMapPO do2Po(VersionDataAlgorithmMapDO obj);

    VersionDataAlgorithmMapDO po2Do(VersionDataAlgorithmMapPO obj);

    VersionDataAlgorithmMapPO dto2Po(VersionDataAlgorithmMapDTO obj);

    List<VersionDataAlgorithmMapPO> dto2Pos(List<VersionDataAlgorithmMapDTO> obj);

}
