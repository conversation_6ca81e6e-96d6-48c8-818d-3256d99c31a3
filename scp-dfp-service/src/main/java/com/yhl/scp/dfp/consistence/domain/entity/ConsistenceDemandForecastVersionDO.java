package com.yhl.scp.dfp.consistence.domain.entity;

import java.io.Serializable;

import com.yhl.platform.common.ddd.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <code>ConsistenceDemandForecastVersionDO</code>
 * <p>
 * 一致性业务预测版本DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:57
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ConsistenceDemandForecastVersionDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -17142007412579385L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 计划周期
     */
    private String planPeriod;
    /**
     * 计划跨度
     */
    private Integer planHorizon;
    /**
     * 颗粒度
     */
    private String planGranularity;
    /**
     * 版本号
     */
    private String versionCode;
    /**
     * 父版本ID
     */
    private String parentVersionId;
    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 关联业务预测版本
     */
    private String demandForecastVersionId;
    /**
     * 生成方式
     */
    private String generateType;
    /**
     * 状态：未发布/已发布
     */
    private String versionStatus;
    
    /**
     * 是否为评审版本
     */
    private String reviewVersionFlag;

}
