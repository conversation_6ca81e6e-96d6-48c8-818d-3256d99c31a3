package com.yhl.scp.dfp.massProduction.infrastructure.po;

import java.io.Serializable;

import com.yhl.platform.common.ddd.BasePO;

/**
 * <code>MassProductionHandoverPO</code>
 * <p>
 * 量产移交信息主表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 16:22:14
 */
public class MassProductionHandoverPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -86556444054555605L;

        /**
     * 主机厂编码
     */
        private String oemCode;
        /**
     * 主机厂名称
     */
        private String oemName;
        /**
     * 客户编码
     */
        private String customerCode;
        /**
     * 客户名称
     */
        private String customerName;
        /**
     * 获取订单方式
     */
        private String orderAcquisitionMethod;
        /**
     * 订单联系人
     */
        private String orderContactPerson;
        /**
     * 发货地址
     */
        private String shipAddress;
        /**
     * 收货联系人
     */
        private String receivingContactPerson;
        /**
     * 库位代码（开票客户信息）
     */
        private String locationCode;
        /**
     * 内部车型代码
     */
        private String vehicleModelCode;
        /**
     * 车型企划量
     */
        private String vehicleModelVolume;
        /**
     * 特殊工艺说明
     */
        private String specialProcessInstructions;
        /**
     * 审批状态
     */
        private String approvalStatus;
        /**
     * 版本
     */
        private Integer versionValue;
        
        /**
         * 审批流程id
         */
        private String approvalId;
        
        /**
         * 单据编号
         */
        private String documentCode;

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    public String getOemName() {
        return oemName;
    }

    public void setOemName(String oemName) {
        this.oemName = oemName;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getOrderAcquisitionMethod() {
        return orderAcquisitionMethod;
    }

    public void setOrderAcquisitionMethod(String orderAcquisitionMethod) {
        this.orderAcquisitionMethod = orderAcquisitionMethod;
    }

    public String getOrderContactPerson() {
        return orderContactPerson;
    }

    public void setOrderContactPerson(String orderContactPerson) {
        this.orderContactPerson = orderContactPerson;
    }

    public String getShipAddress() {
        return shipAddress;
    }

    public void setShipAddress(String shipAddress) {
        this.shipAddress = shipAddress;
    }

    public String getReceivingContactPerson() {
        return receivingContactPerson;
    }

    public void setReceivingContactPerson(String receivingContactPerson) {
        this.receivingContactPerson = receivingContactPerson;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public String getVehicleModelCode() {
        return vehicleModelCode;
    }

    public void setVehicleModelCode(String vehicleModelCode) {
        this.vehicleModelCode = vehicleModelCode;
    }

    public String getVehicleModelVolume() {
        return vehicleModelVolume;
    }

    public void setVehicleModelVolume(String vehicleModelVolume) {
        this.vehicleModelVolume = vehicleModelVolume;
    }

    public String getSpecialProcessInstructions() {
        return specialProcessInstructions;
    }

    public void setSpecialProcessInstructions(String specialProcessInstructions) {
        this.specialProcessInstructions = specialProcessInstructions;
    }

    public String getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

	public String getApprovalId() {
		return approvalId;
	}

	public void setApprovalId(String approvalId) {
		this.approvalId = approvalId;
	}

	public String getDocumentCode() {
		return documentCode;
	}

	public void setDocumentCode(String documentCode) {
		this.documentCode = documentCode;
	}
    
}
