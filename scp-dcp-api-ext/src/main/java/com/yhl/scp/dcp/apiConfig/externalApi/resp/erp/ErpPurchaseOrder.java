package com.yhl.scp.dcp.apiConfig.externalApi.resp.erp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <code>ErpPurchaseOrder</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26 16:45:50
 */
@Data
public class ErpPurchaseOrder {
    /**
     * 组织
     */
    private String orgCode;

    /**
     * 采购单号
     */
    private String orderHeader;

    /**
     * 采购单行号
     */
    private String orderLine;

    /**
     * 物料编码
     */
    private String item;

    /**
     * 物料说明
     */
    private String itemDesc;

    /**
     * 物料单位
     */
    private String itemUnit;

    /**
     * 需求日期
     */
    @JSONField(format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date needDate;

    /**
     * 承诺日期
     */
    @JSONField(format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date promisedDate;

    /**
     * 订单数量
     */
    private Integer quantity;

    /**
     * 接收数量
     */
    private String quantityReceived;

    /**
     * 审批状态
     */
    private String approvalStatus;

    /**
     * 头状态
     */
    private String headerStatus;

    /**
     * 行状态
     */
    private String lineStatus;

    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date creationDate;

    /**
     * 最后更新时间
     */
    @JSONField(format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date lastUpdateDate;

    /**
     * 备注
     */
    private String comments;

    /**
     * 采购员
     */
    private String agentName;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 供应商地点
     */
    private String vendorSiteCode;

    /**
     * 备注
     */
    private String note;

    /**
     * 开单数量
     */
    private Integer qtyBilled;
}

