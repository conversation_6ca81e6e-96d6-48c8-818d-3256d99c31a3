package com.yhl.scp.dcp.apiConfig.externalApi.resp.mes;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName MesMoldGroupTooling
 * @Description 模具工装族表数据
 * @Date 2024-12-21 18:16:23
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Data
public class MesMoldToolingGroup {

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdateDate;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date creationDate;

    private String toolingGroupCode;

    private String createdBy;

    private String toolingGroupId;

    private String broadType;

    private String scheduleRegionCode;

    private String enableFlag;

    private String cid;

}
