
package com.yhl.scp.dcp.apiConfig.externalApi.resp.mes;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName MesProductBoxRelation
 * @Description TODO
 * @Date 2024-10-25 18:16:23
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Data
public class MesProductBoxRelation {

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdateDate;

    private String itemCode;

    private String remark;

    private String kid;

    private String descriptions;

    private String scheduleRegionCode;
    private String enableFlag;
    private String typeCode;
    private String itemId;
    private String standardLoadQty;
}
