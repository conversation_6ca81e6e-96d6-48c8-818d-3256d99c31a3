package com.yhl.scp.ips.rbac.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>UserMessageVO</code>
 * <p>
 * UserMessageVO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13 09:46:54
 */
@ApiModel(value = "用户信息消息表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserMessageVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -39029441826702865L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @FieldInterpretation(value = "主键id")
    private String id;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @FieldInterpretation(value = "用户id")
    private String userId;
    /**
     * 角色id
     */
    @ApiModelProperty(value = "角色id")
    @FieldInterpretation(value = "角色id")
    private String roleId;
    /**
     * 消息来源
     */
    @ApiModelProperty(value = "消息来源")
    @FieldInterpretation(value = "消息来源")
    private String messageSource;
    /**
     * 消息类型
     */
    @ApiModelProperty(value = "消息类型")
    @FieldInterpretation(value = "消息类型")
    private String messageType;
    /**
     * 消息标题
     */
    @ApiModelProperty(value = "消息标题")
    @FieldInterpretation(value = "消息标题")
    private String messageTitle;
    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    @FieldInterpretation(value = "消息内容")
    private String messageContent;
    /**
     * 消息链接
     */
    @ApiModelProperty(value = "消息链接")
    @FieldInterpretation(value = "消息链接")
    private String messageLink;
    /**
     * 消息紧急程度
     */
    @ApiModelProperty(value = "消息紧急程度")
    @FieldInterpretation(value = "消息紧急程度")
    private String messageEmergency;
    /**
     * 是否已读
     */
    @ApiModelProperty(value = "是否已读")
    @FieldInterpretation(value = "是否已读")
    private String readStatus;
    /**
     * 额外信息
     */
    @ApiModelProperty(value = "额外信息")
    @FieldInterpretation(value = "额外信息")
    private String extraInfo;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @FieldInterpretation(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    @FieldInterpretation(value = "是否启用")
    private String enabled;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @FieldInterpretation(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @FieldInterpretation(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @FieldInterpretation(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @FieldInterpretation(value = "修改时间")
    private Date modifyTime;

    @Override
    public void clean() {

    }

}
