package com.yhl.scp.ips.log.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>UserTaskHistoryVO</code>
 * <p>
 * 用户任务历史表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-18 09:48:08
 */
@ApiModel(value = "用户任务历史表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserTaskHistoryVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -48937328781071742L;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @FieldInterpretation(value = "用户ID")
    private String userId;
    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID")
    @FieldInterpretation(value = "任务ID")
    private String taskId;
    /**
     * 完成状态
     */
    @ApiModelProperty(value = "完成状态")
    @FieldInterpretation(value = "完成状态")
    private String completedStatus;
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @FieldInterpretation(value = "日期")
    private String dateStr;

    @Override
    public void clean() {

    }

}
