package com.yhl.scp.ips.log.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <code>ExcelImportLogVO</code>
 * <p>
 * Excel导入日志VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-17 13:49:56
 */
@ApiModel(value = "Excel导入日志VO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ExcelImportLogVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -95232128441844871L;

    /**
     * 对象类型
     */
    @ApiModelProperty(value = "对象类型")
    @FieldInterpretation(value = "对象类型")
    private String objectType;
    /**
     * 导入方式
     */
    @ApiModelProperty(value = "导入方式")
    @FieldInterpretation(value = "导入方式")
    private String importType;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    @FieldInterpretation(value = "批次号")
    private String batchNo;
    /**
     * 信息类型
     */
    @ApiModelProperty(value = "信息类型")
    @FieldInterpretation(value = "信息类型")
    private String infoType;
    /**
     * 显示顺序
     */
    @ApiModelProperty(value = "显示顺序")
    @FieldInterpretation(value = "显示顺序")
    private Integer displayIndex;
    /**
     * 错误详情
     */
    @ApiModelProperty(value = "错误详情")
    @FieldInterpretation(value = "错误详情")
    private String errorDetail;
    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    @FieldInterpretation(value = "租户ID")
    private String tenantId;
    /**
     * 父ID
     */
    @ApiModelProperty(value = "父ID")
    @FieldInterpretation(value = "父ID")
    private String parentId;
    /**
     * 模块代码
     */
    @ApiModelProperty(value = "模块代码")
    @FieldInterpretation(value = "模块代码")
    private String moduleCode;
    /**
     * 场景ID
     */
    @ApiModelProperty(value = "场景ID")
    @FieldInterpretation(value = "场景ID")
    private String scenarioId;

    @Override
    public void clean() {

    }

}