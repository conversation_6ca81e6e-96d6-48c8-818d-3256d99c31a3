package com.yhl.scp.ips.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <code>MpsScenarioVO</code>
 * <p>
 * AMS场景VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-12-12 18:33:34
 */
@ApiModel("AMS场景VO")
@SuperBuilder
@Data
@EqualsAndHashCode(callSuper = true)
public class AmsScenarioVO extends ScenarioVO implements Serializable {

    private static final long serialVersionUID = -3699207156375216727L;

    /**
     * 目标场景
     */
    @ApiModelProperty(value = "目标场景", required = true)
    private String targetScenarioId;

}