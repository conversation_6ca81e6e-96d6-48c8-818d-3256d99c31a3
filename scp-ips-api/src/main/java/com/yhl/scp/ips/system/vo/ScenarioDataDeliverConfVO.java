package com.yhl.scp.ips.system.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseTreeVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <code>ScenarioDataDeliverConfVO</code>
 * <p>
 * ScenarioDataDeliverConfVO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-03-12 10:28:29
 */
@ApiModel(value = "场景数据传输配置VO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ScenarioDataDeliverConfVO extends BaseTreeVO<ScenarioDataDeliverConfVO> implements Serializable {

    private static final long serialVersionUID = -10726147625321043L;

    /**
     * 源模块代码
     */
    @ApiModelProperty(value = "源模块代码")
    @FieldInterpretation(value = "源模块代码")
    private String sourceModuleCode;
    /**
     * 原场景ID
     */
    @ApiModelProperty(value = "原场景ID")
    @FieldInterpretation(value = "原场景ID")
    private String sourceScenarioId;
    /**
     * 数据表
     */
    @ApiModelProperty(value = "数据表")
    @FieldInterpretation(value = "数据表")
    private String dataTable;
    /**
     * 目标模块代码
     */
    @ApiModelProperty(value = "目标模块代码")
    @FieldInterpretation(value = "目标模块代码")
    private String targetModuleCode;
    /**
     * 目标场景ID
     */
    @ApiModelProperty(value = "目标场景ID")
    @FieldInterpretation(value = "目标场景ID")
    private String targetScenarioId;
    /**
     * 组织
     */
    @ApiModelProperty(value = "组织")
    @FieldInterpretation(value = "组织")
    private String organization;
    /**
     * 传递方式
     */
    @ApiModelProperty(value = "传递方式")
    @FieldInterpretation(value = "传递方式")
    private String deliverMethod;
    /**
     * 触发器
     */
    @ApiModelProperty(value = "触发器")
    @FieldInterpretation(value = "触发器")
    private String deliverTrigger;

    @Override
    public void clean() {

    }

}
