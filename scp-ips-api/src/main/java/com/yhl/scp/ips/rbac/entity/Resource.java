package com.yhl.scp.ips.rbac.entity;

import com.yhl.platform.common.enums.YesOrNoEnum;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <code>Resource</code>
 * <p>
 * Resource
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-20 16:09:16
 */
public class Resource implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private String id;

    private String resourceName;

    private String url;

    private String parentId;

    private Integer sort;

    private String icon;

    /**
     * 资源类型 MENU 菜单  WIDGET 控件
     */
    private String resourceType;

    private String openType;

    private String tenantId;

    private String moduleCode;

    private String enabled;

    private String componentPath;

    private String widgetType;

    private Set<String> grantedModules;

    /**
     * 是否是自定义菜单 YES/NO
     */
    private String custom = YesOrNoEnum.NO.getCode();

    private List<Resource> subResources = new ArrayList<Resource>();


    public String getWidgetType() {
        return widgetType;
    }

    public void setWidgetType(String widgetType) {
        this.widgetType = widgetType;
    }

    /**
     * 控件是否授权 YES/NO
     */
    private String widgetAuthority = YesOrNoEnum.YES.getCode();

    public String getWidgetAuthority() {
        return widgetAuthority;
    }

    public void setWidgetAuthority(String widgetAuthority) {
        this.widgetAuthority = widgetAuthority;
    }

    public String getComponentPath() {
        return componentPath;
    }

    public void setComponentPath(String componentPath) {
        this.componentPath = componentPath;
    }

    public String getCustom() {
        return custom;
    }

    public void setCustom(String custom) {
        this.custom = custom;
    }

    public List<Resource> getSubResources() {
        return subResources;
    }

    public void setSubResources(List<Resource> subResources) {
        this.subResources = subResources;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName == null ? null : resourceName.trim();
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url == null ? null : url.trim();
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId == null ? null : parentId.trim();
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon == null ? null : icon.trim();
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType == null ? null : resourceType.trim();
    }

    public String getOpenType() {
        return openType;
    }

    public void setOpenType(String openType) {
        this.openType = openType == null ? null : openType.trim();
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode == null ? null : moduleCode.trim();
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public Set<String> getGrantedModules() {
        return grantedModules;
    }

    public void setGrantedModules(Set<String> grantedModules) {
        this.grantedModules = grantedModules;
    }
}