package com.yhl.scp.common.excel.service;

import com.yhl.scp.common.excel.ExcelPropertyCheck;
import com.yhl.scp.common.utils.ReflectUtil;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.feign.IpsFeign;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>ProcessEnumsService</code>
 * <p>
 * 值集翻译处理
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-03-05 16:01:43
 */
@Service
public class ProcessEnumsService<DTO> {

    @Resource
    IpsFeign ipsFeign;

    public void processEnums(List<DTO> dtoList) {
        for (DTO dto : dtoList) {
            Class<?> clazz = dto.getClass();
            Field[] fields = clazz.getSuperclass().getDeclaredFields();
            for (Field field : fields) {
                ExcelPropertyCheck excelPropertyCheck = field.getAnnotation(ExcelPropertyCheck.class);
                if (excelPropertyCheck == null || StringUtils.isEmpty(excelPropertyCheck.collectionCode())) {
                    continue;
                }
                String collectionCode = excelPropertyCheck.collectionCode();
                List<CollectionValueVO> collectionValueVOList = ipsFeign.getByCollectionCode(collectionCode);
                Map<String, String> collectionValueMap = collectionValueVOList.stream()
                        .collect(Collectors.toMap(CollectionValueVO::getValueMeaning, CollectionValueVO::getCollectionValue));
                ReflectUtil.writeValueByFieldName(dto, field.getName(), collectionValueMap.get(ReflectUtil.getValueByFiledName(dto, field.getName())));
            }
        }
    }

}