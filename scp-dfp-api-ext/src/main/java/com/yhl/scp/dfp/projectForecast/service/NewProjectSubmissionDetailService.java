package com.yhl.scp.dfp.projectForecast.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.projectForecast.dto.NewProjectSubmissionDetailDTO;
import com.yhl.scp.dfp.projectForecast.vo.NewProjectSubmissionDetailVO;

import java.util.List;

/**
 * <code>NewProjectSubmissionDetailService</code>
 * <p>
 * 新项目提报详情应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 11:05:10
 */
public interface NewProjectSubmissionDetailService extends BaseService<NewProjectSubmissionDetailDTO, NewProjectSubmissionDetailVO> {

    /**
     * 查询所有
     *
     * @return list {@link NewProjectSubmissionDetailVO}
     */
    List<NewProjectSubmissionDetailVO> selectAll();


    /**
     * 批量删除
     * @param versionDTOList
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

}
