package com.yhl.scp.dfp.report.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.scp.dfp.consistence.vo.ForecastSummerReportDataDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <code>StockAlertVO</code>
 * <p>
 * DemandDeliveryProductionDetailVO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-15 10:40:27
 */
@Data
public class DemandDeliveryProductionDetailVO implements Serializable {

    private static final long serialVersionUID = -8519374303480206685L;

    @ApiModelProperty("发货数量")
    private BigDecimal demandQty;

    @ApiModelProperty("需求数量")
    private BigDecimal loadingQty;
    @ApiModelProperty("日期")
    private  String titleDate;

    @ApiModelProperty(value = "需求数量颜色")
    @FieldInterpretation(value = "需求数量颜色")
    private String demandColor;

    @ApiModelProperty(value = "装车数量颜色")
    @FieldInterpretation(value = "装车数量颜色")
    private String loadingColor;

}
