package com.yhl.scp.dfp.loading.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;


import java.io.Serializable;
import java.util.Date;

/**
 * <code>DfpDemandForecastAttachmentsDTO</code>
 * <p>
 * 源文件管理DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-03 14:43:13
 */
@ApiModel(value = "源文件管理DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DfpDemandForecastAttachmentsDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -47764621395620954L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 原始需求版本号
     */
    @ApiModelProperty(value = "原始需求版本号")
    private String versionCode;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    private String oemCode;
    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String fileName;
    /**
     * 文件路径
     */
    @ApiModelProperty(value = "文件路径")
    private String filePath;
    
    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件路径")
    private String fileType;
    
    /**
     * 导入状态
     */
    @ApiModelProperty(value = "文件路径")
    private String uploadStatus;

}
