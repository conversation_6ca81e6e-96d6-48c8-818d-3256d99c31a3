package com.yhl.scp.dfp.newProduct.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * <code>NewProductTrialSubmissionDTO</code>
 * <p>
 * 新品试制提报DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-29 16:51:52
 */
@ApiModel(value = "新品试制提报DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class NewProductTrialSubmissionDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 586769516983032719L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    private String oemCode;
    /**
     * 主机厂名称
     */
    @ApiModelProperty(value = "主机厂名称")
    private String oemName;
    /**
     * 项目号
     */
    @ApiModelProperty(value = "费用挂靠项目号/项目号")
    @JSONField(name = "projectNo")
    private String projectNumber;
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @JSONField(name = "projectName")
    private String projectName;
    /**
     * 试制产品
     */
    @ApiModelProperty(value = "试制产品")
    @JSONField(name = "trialProduction")
    private String trialProduct;
    /**
     * 试制目的
     */
    @ApiModelProperty(value = "试制目的")
    @JSONField(name = "trialPurpose")
    private String trialPurpose;
    /**
     * 试制流程
     */
    @ApiModelProperty(value = "试制流程")
    @JSONField(name = "processFlow")
    private String trialProcess;
    /**
     * 试制卡号
     */
    @ApiModelProperty(value = "试制卡号")
    @JSONField(name = "")
    private String billNo;
    /**
     * 发起公司
     */
    @ApiModelProperty(value = "发起公司")
    @JSONField(name = "companyName")
    private String companyName;
    /**
     * 发起部门
     */
    @ApiModelProperty(value = "发起部门")
    @JSONField(name = "deptName")
    private String deptName;
    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人")
    @JSONField(name = "userName")
    private String userName;
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
//    @JSONField(name = "itemName")
    private String itemName;
    /**
     * 切割图号
     */
    @ApiModelProperty(value = "切割图号")
    private String cuttingDrawingNumber;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer versionValue;
    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    @JSONField(name = "custCode")
    private String customerCode;
    /**
     * 试制类型
     */
    @ApiModelProperty(value = "试制类型")
    @JSONField(name = "productType")
    private String productType;
    /**
     * 计划员
     */
    @ApiModelProperty(value = "计划员")
    @JSONField(name = "planName")
    private String planner;
    /**
     * 表单审批状态(终止，流程结束，流程未结束)
     */
    @ApiModelProperty(value = "表单审批状态(终止，流程结束，流程未结束)")
    private String approveStatus;
    /**
     * 计划审批(审批通过时为Y，否则为N)
     */
    @ApiModelProperty(value = "计划审批(审批通过时为Y，否则为N)")
    private String planStatus;
//    以下是详情单数据
    /**
     * 装车位置
     */
    @ApiModelProperty(value = "装车位置")
    private String loadingPosition;
    /**
     * 零件名称
     */
    @ApiModelProperty(value = "零件名称")
    private String partName;

    @ApiModelProperty(value = "需求日期")
    @FieldInterpretation(value = "需求日期")
    @JSONField(name = "itemDate")
    private Date demandTime;

    @ApiModelProperty(value = "需求数量")
    @FieldInterpretation(value = "需求数量")
    @JSONField(name = "demandQty")
    private BigDecimal demandQuantity;

    @ApiModelProperty(value = "投料数量")
    @FieldInterpretation(value = "投料数量")
    @JSONField(name = "materialQty")
    private BigDecimal feedingQuantity;

    @ApiModelProperty(value = "投料单位")
    @FieldInterpretation(value = "投料单位")
    @JSONField(name = "materialUnit")
    private String feedingUnit;

    @JSONField(name = "finishDate")
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdateDate;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @JSONField(name = "itemNo")
    private String productCode;
    /**
     * 预处理
     */
    @ApiModelProperty(value = "预处理")
    private Integer pretreatment;
    /**
     * 成型
     */
    @ApiModelProperty(value = "成型")
    private Integer shaping;
    /**
     * 合片
     */
    @ApiModelProperty(value = "合片")
    private Integer lamination;
    /**
     * 总成
     */
    @ApiModelProperty(value = "总成")
    private Integer assembly;
    /**
     * 动态字段表头及值
     */
    private Map<String, BigDecimal> dateNumMap;
    /**
     * 是否完成
     */
    @ApiModelProperty(value = "是否完成")
    private String finishedFlag;

}
