package com.yhl.scp.dfp.delivery.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanLockConfigDTO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanLockConfigVO;

import java.util.List;

/**
 * <code>DeliveryPlanLockConfigService</code>
 * <p>
 * 发货计划锁定配置应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-01 13:54:36
 */
public interface DeliveryPlanLockConfigService extends BaseService<DeliveryPlanLockConfigDTO, DeliveryPlanLockConfigVO> {

    /**
     * 查询所有
     *
     * @return list {@link DeliveryPlanLockConfigVO}
     */
    List<DeliveryPlanLockConfigVO> selectAll();

    DeliveryPlanLockConfigVO selectByOemcode(String oemCode);
}
