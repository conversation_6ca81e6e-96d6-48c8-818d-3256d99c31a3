package com.yhl.scp.dfp.calendar.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.scp.common.vo.SimpleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <code>ShiftVO</code>
 * <p>
 * 班次VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-07 16:07:56
 */
@ApiModel(value = "班次VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShiftVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -15210846206411447L;

    /**
     * 班次名称
     */
    @ApiModelProperty(value = "班次名称")
    @FieldInterpretation(value = "班次名称")
    private String shiftName;
    /**
     * 班次类型
     */
    @ApiModelProperty(value = "班次类型")
    @FieldInterpretation(value = "班次类型")
    private String shiftType;
    /**
     * 出勤时段
     */
    @ApiModelProperty(value = "出勤时段")
    @FieldInterpretation(value = "出勤时段")
    private String shiftPattern;

    @Override
    public void clean() {
    }

    public SimpleVO toSimpleVO() {
        return SimpleVO.builder().id(this.getId()).code(this.getShiftName()).name(this.getShiftName()).build();
    }

}
