package com.yhl.scp.dfp.delivery.vo;

import java.io.Serializable;
import java.util.List;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastCheckVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <code>DeliveryPlanPublishCheckVO</code>
 * <p>
 * 发货计划发布数据校验VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 13:51:11
 */
@ApiModel(value = "发货计划发布数据校验VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryPlanPublishCheckVO implements Serializable {

    private static final long serialVersionUID = -13251316386759664L;

    /**
     * 校验偏差率异常提示信息
     */
    @ApiModelProperty(value = "版本id")
    @FieldInterpretation(value = "版本id")
    private String checkMsg;
    
    /**
     * 校验预测数据
     */
    @ApiModelProperty(value = "校验预测数据")
    @FieldInterpretation(value = "校验预测数据")
    private List<ConsistenceDemandForecastCheckVO> forecastCheckList;

    /**
     * 发货计划
     */
    @ApiModelProperty(value = "发货计划")
    @FieldInterpretation(value = "发货计划")
    private List<DeliveryPlanVO> deliveryPlanList;

    

}
