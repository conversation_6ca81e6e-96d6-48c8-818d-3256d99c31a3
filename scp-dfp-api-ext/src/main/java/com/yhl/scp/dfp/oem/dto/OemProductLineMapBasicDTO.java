package com.yhl.scp.dfp.oem.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.ddd.BaseDTO;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.common.excel.ExcelPropertyCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>OemProductLineMapDTO</code>
 * <p>
 * 产线映射关系DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-26 15:20:02
 */
@ApiModel(value = "产线映射关系DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OemProductLineMapBasicDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 214635839177221534L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "主机厂编码")
    @ExcelProperty(value = "*主机厂编码")
    @ExcelPropertyCheck(required = true)
    private String oemCode;
    /**
     * 产线编码
     */
    @ApiModelProperty(value = "产线编码")
    @ExcelProperty(value = "*产线编码")
    @ExcelPropertyCheck(required = true)
    private String lineCode;

    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @ExcelProperty(value = "*车型编码")
    @ExcelPropertyCheck(required = true)
    private String vehicleModelCode;

    /**
     * 版本
     */
    private Integer versionValue;
    /**
     * 是否由福耀提供
     */
    @ApiModelProperty(value = "是否由福耀提供")
    @ExcelProperty(value = "是否由福耀提供")
    private String providedByFuyao;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
}
