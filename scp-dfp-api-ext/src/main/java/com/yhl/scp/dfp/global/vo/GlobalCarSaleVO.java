package com.yhl.scp.dfp.global.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <code>GlobalCarSaleVO</code>
 * <p>
 * 全球汽车销量VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 11:01:24
 */
@ApiModel(value = "全球汽车销量VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GlobalCarSaleVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -11007757863423454L;

    /**
     * 国家/地区
     */
    @ApiModelProperty(value = "国家/地区")
    @FieldInterpretation(value = "国家/地区")
    private String country;
    /**
     * 集团
     */
    @ApiModelProperty(value = "集团")
    @FieldInterpretation(value = "集团")
    private String corporate;
    /**
     * 整车厂/品牌
     */
    @ApiModelProperty(value = "整车厂/品牌")
    @FieldInterpretation(value = "整车厂/品牌")
    private String brand;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 车种
     */
    @ApiModelProperty(value = "车种")
    @FieldInterpretation(value = "车种")
    private String vehicleType;
    /**
     * 级别
     */
    @ApiModelProperty(value = "级别")
    @FieldInterpretation(value = "级别")
    private String vehicleLevel;
    /**
     * 车型
     */
    @ApiModelProperty(value = "车型")
    @FieldInterpretation(value = "车型")
    private String vehicleModel;
    /**
     * 全球车型编码
     */
    @ApiModelProperty(value = "全球车型编码")
    @FieldInterpretation(value = "全球车型编码")
    private String globalVehicleModelCode;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @FieldInterpretation(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 动力总成
     */
    @ApiModelProperty(value = "动力总成")
    @FieldInterpretation(value = "动力总成")
    private String powerTrain;

    // 全球汽车销量详情
    private Map<String, String> globalCarSaleDetails;

    @Override
    public void clean() {

    }

}
