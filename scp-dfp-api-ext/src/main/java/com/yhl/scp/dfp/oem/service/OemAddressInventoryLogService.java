package com.yhl.scp.dfp.oem.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.oem.dto.OemAddressInventoryLogDTO;
import com.yhl.scp.dfp.oem.vo.OemAddressInventoryLogVO;

import java.util.List;

/**
 * <code>OemAddressInventoryLogService</code>
 * <p>
 * 主机厂地址_中间表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-13 21:09:11
 */
public interface OemAddressInventoryLogService extends BaseService<OemAddressInventoryLogDTO, OemAddressInventoryLogVO> {

    /**
     * 查询所有
     *
     * @return list {@link OemAddressInventoryLogVO}
     */
    List<OemAddressInventoryLogVO> selectAll();

    BaseResponse<Void> syncOemAddressLog(List<OemAddressInventoryLogDTO> list);

    BaseResponse<Void> syncOemAddress(String tenantId);

    List<LabelValue<String>> selectTransitStockDropdown(String transitStockLike, String transitStockCode);

    List<LabelValue<String>> selectStockLocationDropdown(String transitStockCode, String stockLocationLike);

    List<LabelValue<String>> selectOemAddressDropdown(String transitStockCode, String stockLocationCode, String oemAddressLike);

	List<LabelValue<String>> selectCustomerAddressDropdown(String customerCode);

}
