package com.yhl.scp.dfp.consistence.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <code>ConsistenceDemandForecastDataDTO</code>
 * <p>
 * 一致性业务预测数据DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 10:19:08
 */
@ApiModel(value = "一致性业务预测数据DTO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ConsistenceDemandForecastDataDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -57688767159616976L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 版本ID
     */
    @ApiModelProperty(value = "版本ID")
    private String versionId;
    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    private String demandCategory;
    /**
     * 预测类型：滚动预测/预测算法
     */
    @ApiModelProperty(value = "预测类型：滚动预测/预测算法")
    private String forecastType;
    /**
     * 滚动预测/预测算法数据源id
     */
    @ApiModelProperty(value = "滚动预测/预测算法数据源id")
    private String originId;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    private String oemCode;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    private String productCode;
    /**
     * 状态：未发布/已发布
     */
    @ApiModelProperty(value = "状态：未发布/已发布")
    private String versionStatus;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;

}
