package com.yhl.scp.dfp.warehouse.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>WarehouseReleaseRecordMonthVO</code>
 * <p>
 * WarehouseReleaseRecordMonthVO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-11 17:34:37
 */
@ApiModel(value = "仓库发货记录VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseReleaseRecordMonthVO implements Serializable {

    private static final long serialVersionUID = -5887551487032304864L;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 产线编码
     */
    @ApiModelProperty(value = "产线编码")
    @FieldInterpretation(value = "产线编码")
    private String productionLineCode;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @FieldInterpretation(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String itemCode;
    /**
     * 年月
     */
    @ApiModelProperty(value = "年月")
    @FieldInterpretation(value = "年月")
    private String yearMonth;
    /**
     * 发货数量
     */
    @ApiModelProperty(value = "发货数量")
    @FieldInterpretation(value = "发货数量")
    private BigDecimal sumQty;
    /**
     * 车辆计数标识
     */
    @ApiModelProperty(value = "车辆计数标识")
    @FieldInterpretation(value = "车辆计数标识")
    private Boolean vehicleCountFlag;

}