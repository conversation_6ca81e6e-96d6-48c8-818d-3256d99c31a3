package com.yhl.scp.dfp.versionData.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>VersionDataOriginMapDTO</code>
 * <p>
 * 原始版本数据映射关系DTO
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 17:26:16
 */
@ApiModel(value = "原始版本数据映射关系DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class VersionDataOriginMapDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -90091149625251730L;

        /**
     * 主键ID
     */
        @ApiModelProperty(value = "主键ID")
    private String id;
        /**
     * 版本ID
     */
        @ApiModelProperty(value = "版本ID")
    private String versionId;
        /**
     * 数据ID
     */
        @ApiModelProperty(value = "数据ID")
    private String demandId;
        /**
     * 备注
     */
        @ApiModelProperty(value = "备注")
    private String remark;

}
