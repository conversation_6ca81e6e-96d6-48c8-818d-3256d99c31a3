package com.yhl.scp.dfp.material.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <code>PartRiskLevelDetailDTO</code>
 * <p>
 * 零件风险等级详情DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 11:03:52
 */
@ApiModel(value = "零件风险等级详情DTO")
@Data
public class PartRiskLevelDetailDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -63065845970207902L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 零件风险等级ID
     */
    @ApiModelProperty(value = "零件风险等级ID")
    private String materialRiskLevelId;
    /**
     * 规则ID
     */
    @ApiModelProperty(value = "规则ID")
    private String riskLevelRuleId;
    /**
     * 表现情况
     */
    @ApiModelProperty(value = "表现情况")
    private String performanceSituation;
    /**
     * 表现评分
     */
    @ApiModelProperty(value = "表现评分")
    private String performanceScore;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;

}
