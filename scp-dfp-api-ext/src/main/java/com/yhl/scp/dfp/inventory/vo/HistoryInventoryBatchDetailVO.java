package com.yhl.scp.dfp.inventory.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>HistoryInventoryBatchDetailVO</code>
 * <p>
 * 历史库存批次明细VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-08 16:04:46
 */
@ApiModel(value = "历史库存批次明细VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class HistoryInventoryBatchDetailVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -80181540052839959L;

    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    @FieldInterpretation(value = "库存点代码")
    private String versionCode;
    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    @FieldInterpretation(value = "库存点代码")
    private String stockPointCode;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @FieldInterpretation(value = "物料编码")
    private String productCode;
    /**
     * 子库存
     */
    @ApiModelProperty(value = "子库存")
    @FieldInterpretation(value = "子库存")
    private String subinventory;
    /**
     * 子库存描述
     */
    @ApiModelProperty(value = "子库存描述")
    @FieldInterpretation(value = "子库存描述")
    private String subinventoryDescription;
    /**
     * 货位
     */
    @ApiModelProperty(value = "货位")
    @FieldInterpretation(value = "货位")
    private String freightSpace;
    /**
     * 货位描述
     */
    @ApiModelProperty(value = "货位描述")
    @FieldInterpretation(value = "货位描述")
    private String freightSpaceDescription;
    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    @FieldInterpretation(value = "批次")
    private String batch;
    /**
     * 条码号
     */
    @ApiModelProperty(value = "条码号")
    @FieldInterpretation(value = "条码号")
    private String barCode;
    /**
     * 现有量
     */
    @ApiModelProperty(value = "现有量")
    @FieldInterpretation(value = "现有量")
    private String currentQuantity;
    /**
     * 客户号
     */
    @ApiModelProperty(value = "客户号")
    @FieldInterpretation(value = "客户号")
    private String customerNum;
    /**
     * 零件号
     */
    @ApiModelProperty(value = "零件号")
    @FieldInterpretation(value = "零件号")
    private String partNum;
    /**
     * 入库时间
     */
    @ApiModelProperty(value = "入库时间")
    @FieldInterpretation(value = "入库时间")
    private String assignedTime;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    @FieldInterpretation(value = "最后更新时间")
    private String lastUpdateDate;
    /**
     * 库龄
     */
    @ApiModelProperty(value = "库龄")
    @FieldInterpretation(value = "库龄")
    private String stockAge;
    /**
     * 库龄天数
     */
    @ApiModelProperty(value = "库龄天数")
    @FieldInterpretation(value = "库龄天数")
    private String stockAgeDay;
    /**
     * 保质期
     */
    @ApiModelProperty(value = "保质期")
    @FieldInterpretation(value = "保质期")
    private String warrantyDate;
    /**
     * 距离失效时间
     */
    @ApiModelProperty(value = "距离失效时间")
    @FieldInterpretation(value = "距离失效时间")
    private String distanceEnableDate;
    /**
     * 数据来源（ERP/MES）
     */
    @ApiModelProperty(value = "数据来源（ERP/MES）")
    @FieldInterpretation(value = "数据来源（ERP/MES）")
    private String sourceType;
    /**
     * 原始报文组织ID
     */
    @ApiModelProperty(value = "原始报文组织ID")
    @FieldInterpretation(value = "原始报文组织ID")
    private String originalOrgId;
    /**
     * 分配状态
     */
    @ApiModelProperty(value = "分配状态")
    @FieldInterpretation(value = "分配状态")
    private String allocationStatus;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @FieldInterpretation(value = "版本号")
    private Integer versionValue;

    @Override
    public void clean() {

    }

}
