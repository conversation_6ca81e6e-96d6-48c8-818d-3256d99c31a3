package com.yhl.scp.dfp.market.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <code>MarketShareVO</code>
 * <p>
 * 市场占有率VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 11:03:13
 */
@ApiModel(value = "市场占有率VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MarketShareVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 370883761176004241L;

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    @FieldInterpretation(value = "客户编码")
    private String customerCode;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @FieldInterpretation(value = "客户名称")
    private String customerName;

    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;

    /**
     * 主机厂名称
     */
    @ApiModelProperty(value = "主机厂名称")
    @FieldInterpretation(value = "主机厂名称")
    private String oemName;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @FieldInterpretation(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 装车位置
     */
    @ApiModelProperty(value = "装车位置")
    @FieldInterpretation(value = "装车位置")
    private String loadingPosition;

    // 全球汽车销量详情
    private List<MarketShareDetailVO> detailList;

    @Override
    public void clean() {

    }

}
