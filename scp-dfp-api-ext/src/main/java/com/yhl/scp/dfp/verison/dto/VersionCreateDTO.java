package com.yhl.scp.dfp.verison.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <code>DemandVersionCreateDTO</code>
 * <p>
 * 版本创建DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-03 16:42:52
 */
@ApiModel(value = "版本创建DTO")
@Data
public class VersionCreateDTO implements Serializable {

    private static final long serialVersionUID = 3012931748971464980L;

    @ApiModelProperty("版本类型")
    private String versionType;

    @ApiModelProperty("生成类型")
    private String generateType;

    @ApiModelProperty(value = "计划周期")
    private String planPeriod;

    @ApiModelProperty(value = "目标原始需求版本ID")
    private String targetOriginDemandVersionId;

    @ApiModelProperty(value = "目标日需求版本ID")
    private String targetCleanDemandVersionId;

    @ApiModelProperty(value = "目标滚动预测版本ID")
    private String targetCleanForecastVersionId;

    @ApiModelProperty(value = "目标项目预测版本ID")
    private String targetProjectForecastVersionId;

    @ApiModelProperty(value = "目标业务预测版本ID")
    private String targetDemandForecastVersionId;

    @ApiModelProperty(value = "目标一致性业务预测版本ID")
    private String targetConsistenceDemandForecastVersionId;

    @ApiModelProperty(value = "目标发货计划版本ID")
    private String targetDeliveryPlanVersionId;

    @ApiModelProperty(value = "目标预测算法版本id")
    private String targetCleanAlgorithmVersionId;

    @ApiModelProperty(value = "主机厂编码来源")
    private List<OemResourceDTO> oemCodeResource;

    @ApiModelProperty(value = "主机厂编码和业务预测版本映射关系")
    private Map<String, String> oemCodeAndForecastVersionMap;

    @ApiModelProperty(value = "租户ID", hidden = true)
    private String tenantId;

    @ApiModelProperty(value = "数据库")
    private String scenario;

    @ApiModelProperty("当前版本号")
    private String currentVersionCode;

    @ApiModelProperty("当前版本ID")
    private String currentVersionId;

    @ApiModelProperty("上一版本号")
    private String targetVersionCode;

    @ApiModelProperty("上一版本ID")
    private String targetVersionId;

    @ApiModelProperty("上版本原始版本ID")
    private String previousOriginVersionId;

    @ApiModelProperty("创建人")
    private String creator;

}