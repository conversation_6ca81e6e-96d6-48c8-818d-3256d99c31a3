package com.yhl.scp.dfp.oem.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <code>OemRiskLevelVO</code>
 * <p>
 * 主机厂风险等级VO
 * </p>
 *
 * @version 1.0
 * @since 2024-07-28 21:44:33
 */
@ApiModel(value = "主机厂风险等级VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OemRiskLevelVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -84958731708948828L;

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    @FieldInterpretation(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "客户名称")
    @FieldInterpretation(value = "客户名称")
    private String customerName;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 主机厂名称
     */
    @ApiModelProperty(value = "主机厂名称")
    @FieldInterpretation(value = "主机厂名称")
    private String oemName;
    /**
     * 市场属性
     */
    @ApiModelProperty(value = "市场属性")
    @FieldInterpretation(value = "市场属性")
    private String marketType;
    /**
     * 评估时间
     */
    @ApiModelProperty(value = "评估时间")
    @FieldInterpretation(value = "评估时间")
    private String estimateTime;
    /**
     * 每月风险等级
     */
    @ApiModelProperty(value = "每月风险等级")
    @FieldInterpretation(value = "每月风险等级")
    private Map<String, String> riskLevelMap;
    /**
     * 出现经营方面负面信息
     */
    @ApiModelProperty(value = "出现经营方面负面信息")
    @FieldInterpretation(value = "出现经营方面负面信息")
    private String negativeBusiness;
    /**
     * 信保公司限制/取消限制
     */
    @ApiModelProperty(value = "信保公司限制/取消限制")
    @FieldInterpretation(value = "信保公司限制/取消限制")
    private String creditRestrict;
    /**
     * 持续2个月未能全额回款
     */
    @ApiModelProperty(value = "持续2个月未能全额回款")
    @FieldInterpretation(value = "持续2个月未能全额回款")
    private String twoMonthFullPayment;
    /**
     * 持续2个月装车量极小
     */
    @ApiModelProperty(value = "持续2个月装车量极小")
    @FieldInterpretation(value = "持续2个月装车量极小")
    private String twoMonthLoadingVolume;
    /**
     * 高风险判定
     */
    @ApiModelProperty(value = "高风险判定")
    @FieldInterpretation(value = "高风险判定")
    private String highRiskJudge;
    /**
     * 中风险判定
     */
    @ApiModelProperty(value = "中风险判定")
    @FieldInterpretation(value = "中风险判定")
    private String middleRiskJudge;
    /**
     * 主机厂风险等级
     */
    @ApiModelProperty(value = "主机厂风险等级")
    @FieldInterpretation(value = "主机厂风险等级")
    private String riskLevel;

    @Override
    public void clean() {

    }
}
