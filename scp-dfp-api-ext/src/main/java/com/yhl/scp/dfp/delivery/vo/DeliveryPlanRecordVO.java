package com.yhl.scp.dfp.delivery.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>DeliveryPlanRecordVO</code>
 * <p>
 * 发货计划下发记录VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-12 18:22:55
 */
@ApiModel(value = "发货计划下发记录VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryPlanRecordVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -97991686111266642L;

    /**
     * 版本ID
     */
    @ApiModelProperty(value = "版本ID")
    @FieldInterpretation(value = "版本ID")
    private String versionId;
    /**
     * 下发日期
     */
    @ApiModelProperty(value = "下发日期")
    @FieldInterpretation(value = "下发日期")
    private Date deliveryDate;
    /**
     * 下发单号
     */
    @ApiModelProperty(value = "下发单号")
    @FieldInterpretation(value = "下发单号")
    private String billNo;
    /**
     * 下发版本
     */
    @ApiModelProperty(value = "下发版本")
    @FieldInterpretation(value = "下发版本")
    private String deliveryVersion;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    @FieldInterpretation(value = "批次号")
    private String batchNo;
    /**
     * 计划员
     */
    @ApiModelProperty(value = "计划员")
    @FieldInterpretation(value = "计划员")
    private String planner;
    /**
     * 业务编码
     */
    @ApiModelProperty(value = "业务编码")
    @FieldInterpretation(value = "业务编码")
    private String businessCode;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;

    @Override
    public void clean() {

    }

}
