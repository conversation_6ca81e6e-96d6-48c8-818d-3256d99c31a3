package com.yhl.scp.dfp.stock.service;

import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.stock.dto.InventoryShiftDTO;
import com.yhl.scp.dfp.stock.vo.InventoryShiftVO;

import java.util.List;
import java.util.Map;

/**
 * <code>InventoryShiftService</code>
 * <p>
 * 库存推移表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-26 10:27:11
 */
public interface InventoryShiftService extends BaseService<InventoryShiftDTO, InventoryShiftVO> {

    /**
     * 查询所有
     *
     * @return list {@link InventoryShiftVO}
     */
    List<InventoryShiftVO> selectAll();

    /**
     * 查询库存推移表数据
     * @param inventoryShiftDTO
     * @return
     */
    List<InventoryShiftVO> queryInventoryShiftData(InventoryShiftDTO inventoryShiftDTO);

    List<String> getPageQueryCriteriaParam(String queryParam);

	List<InventoryShiftVO> selectByNewPage(Pagination pagination, String sortParam, String queryParam,
                                           boolean weeklySummary);

    List<InventoryShiftVO> selectPageByWeek(List<InventoryShiftVO> inventoryShiftVOList);

    List<InventoryShiftVO> selectVOByParams(Map<String, Object> params);

}
