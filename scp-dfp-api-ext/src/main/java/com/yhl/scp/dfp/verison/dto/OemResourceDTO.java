package com.yhl.scp.dfp.verison.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <code>OemResourceDTO</code>
 * <p>
 * 主机厂来源指定DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-05 16:24:31
 */
@ApiModel(value = "主机厂来源DTO")
@Data
public class OemResourceDTO {

    /**
     * 主机厂编码
     */
    @ApiModelProperty("主机厂编码")
    private String oemCode;

    /**
     * 原始需求版本ID
     */
    @ApiModelProperty("原始需求版本ID")
    private String originDemandVersionId;

    /**
     * 日需求版本ID
     */
    @ApiModelProperty("日需求版本ID")
    private String cleanDemandVersionId;

    /**
     * 滚动预测版本ID
     */
    @ApiModelProperty("滚动预测版本ID")
    private String cleanForecastVersionId;

    /**
     * 预测算法版本ID
     */
    @ApiModelProperty("预测算法版本ID")
    private String cleanAlgorithmVersionId;

    /**
     * 业务预测版本ID
     */
    @ApiModelProperty("业务预测版本ID")
    private String demandForecastVersionId;

    /**
     * 项目预测版本ID
     */
    @ApiModelProperty("项目预测版本ID")
    private String projectForecastVersionId;

}