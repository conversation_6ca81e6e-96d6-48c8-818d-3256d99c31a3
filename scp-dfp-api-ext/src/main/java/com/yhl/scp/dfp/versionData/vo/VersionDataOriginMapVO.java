package com.yhl.scp.dfp.versionData.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>VersionDataOriginMapVO</code>
 * <p>
 * 原始版本数据映射关系VO
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 17:26:16
 */
@ApiModel(value = "原始版本数据映射关系VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VersionDataOriginMapVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 329778572118345813L;

        /**
     * 版本ID
     */
        @ApiModelProperty(value = "版本ID")
    @FieldInterpretation(value = "版本ID")
    private String versionId;
        /**
     * 数据ID
     */
        @ApiModelProperty(value = "数据ID")
    @FieldInterpretation(value = "数据ID")
    private String demandId;

    @Override
    public void clean() {

    }

}
