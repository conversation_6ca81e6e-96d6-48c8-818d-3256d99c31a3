package com.yhl.scp.dfp.oem.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>OemProductLineMapVO</code>
 * <p>
 * 产线映射关系VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-20 14:50:48
 */
@ApiModel(value = "产线映射关系VO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OemProductLineMapVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -80695442160047184L;

    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 产线编码
     */
    @ApiModelProperty(value = "产线编码")
    @FieldInterpretation(value = "产线编码")
    private String lineCode;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @FieldInterpretation(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;
    /**
     * 是否由福耀提供
     */
    @ApiModelProperty(value = "是否由福耀提供")
    @FieldInterpretation(value = "是否由福耀提供")
    private String providedByFuyao;

    @Override
    public void clean() {

    }
    /**
     * 主机厂名称
     */
    @ApiModelProperty(value = "主机厂名称")
    @FieldInterpretation(value = "主机厂名称")
    private String oemName;
    /**
     * 产线名称
     */
    @ApiModelProperty(value = "产线名称")
    @FieldInterpretation(value = "产线名称")
    private String lineName;
}
