package com.yhl.scp.dfp.transport.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * <code>TransportRoutingResourceDTO</code>
 * <p>
 * 运输资源与路径关系DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-20 22:21:04
 */
@ApiModel(value = "运输资源与路径关系DTO")
@Data
@SuperBuilder
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TransportRoutingResourceDTO extends TransportRoutingResourceBasicDTO {

}
