package com.yhl.scp.dfp.demand.vo;

import com.yhl.scp.dfp.common.vo.DynamicDataDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <code>DemandForecastEstablishmentDetailVO</code>
 * <p>
 * 业务预测试制物品详情
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-14 15:07:42
 */
@Data
public class DemandForecastEstablishmentDetailVO {

    private String id;

    @ApiModelProperty(value = "类目")
    private String category;

    @ApiModelProperty(value = "类目")
    private String categoryName;

    @ApiModelProperty(value = "动态数据明细")
    private List<DynamicDataDetailVO> details;
}
