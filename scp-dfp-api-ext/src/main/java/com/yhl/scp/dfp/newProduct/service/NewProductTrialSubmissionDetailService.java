package com.yhl.scp.dfp.newProduct.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.newProduct.dto.NewProductTrialSubmissionDetailDTO;
import com.yhl.scp.dfp.newProduct.dto.NewProductTrialSubmissionInsertWorkOrderDTO;
import com.yhl.scp.dfp.newProduct.dto.NewProductTrialSubmissionUpdateWorkOrderDTO;
import com.yhl.scp.dfp.newProduct.vo.NewProductTrialSubmissionDetailVO;

import java.util.List;
import java.util.Map;

/**
 * <code>NewProductTrialSubmissionDetailService</code>
 * <p>
 * 新品试制提报详情应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 11:04:35
 */
public interface NewProductTrialSubmissionDetailService extends BaseService<NewProductTrialSubmissionDetailDTO, NewProductTrialSubmissionDetailVO> {

    /**
     * 查询所有
     *
     * @return list {@link NewProductTrialSubmissionDetailVO}
     */
    List<NewProductTrialSubmissionDetailVO> selectAll();

    /**
     * 批量删除
     * @param versionDTOList
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

    /**
     * 下发校验
     * @param detailIds
     */
    NewProductTrialSubmissionInsertWorkOrderDTO deliveryCheck(List<String> detailIds);

    /**
     * 更新工单信息
     * @param dto
     */
    void doUpdateWorkOrderInfo(NewProductTrialSubmissionUpdateWorkOrderDTO dto);
    List<NewProductTrialSubmissionDetailVO> selectVOByParams(Map<String, Object> params);

}
