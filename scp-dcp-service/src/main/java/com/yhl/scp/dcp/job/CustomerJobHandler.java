package com.yhl.scp.dcp.job;

import cn.hutool.core.map.MapUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.dcp.apiConfig.service.ApiConfigService;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.externalApi.ExternalApiHandler;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @ClassName CustomerJobHandler
 * @Description TODO
 * @Date 2024-09-03 17:31:27
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Component
@Slf4j
public class CustomerJobHandler {

    @Resource
    private ExternalApiHandler externalApiHandler;

    @Resource
    private ApiConfigService apiConfigService;

    @XxlJob("customerJobHandler")
    public ReturnT<String> customerJobHandler() throws Exception {
        try {
            Map<String, Object> params = MapUtil.newHashMap();
            params.put("apiCategory", ApiCategoryEnum.CUSTOMER.getCode());
            params.put("enabled", YesOrNoEnum.YES.getCode());
            List<ApiConfigVO> apiConfigVOS = apiConfigService.selectVOByParams(params);
            if (CollectionUtils.isEmpty(apiConfigVOS)) {
                XxlJobHelper.log("不存在需要同步的客户接口配置");
                return ReturnT.SUCCESS;
            }
            apiConfigVOS.stream().forEach(x -> {
                XxlJobHelper.log("开始同步租户：{}下的客户数据", x.getTenantCode());
                externalApiHandler.handle(String.join("_", x.getTenantCode(), x.getApiSource(), x.getApiCategory()), MapUtil.newHashMap());
                XxlJobHelper.log("租户：{}下的客户数据同步结束", x.getTenantCode());
            });
        } catch (Exception ex) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
