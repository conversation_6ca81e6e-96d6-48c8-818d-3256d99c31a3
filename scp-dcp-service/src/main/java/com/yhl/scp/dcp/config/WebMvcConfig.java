package com.yhl.scp.dcp.config;

import com.yhl.platform.common.interceptors.MyLocaleChangeInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <code>WebMvcConfig</code>
 * <p>
 * WebMvc配置
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-10 15:22:25
 */
public class WebMvcConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        MyLocaleChangeInterceptor myLocaleChangeInterceptor = new MyLocaleChangeInterceptor();
        registry.addInterceptor(myLocaleChangeInterceptor);
    }

}