package com.yhl.scp.dcp.externalApi.handler.wx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.externalApi.handler.Handler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <code>MessageHandler</code>
 * <p>
 * 企业微信消息处理
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-11 11:28:55
 */
@Slf4j
@Component(value = "messageHandler4Wx")
@RefreshScope
public class MessageHandler implements Handler {
    @Value("${message.notify.wx.botWebhookUrl:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3e7feb71-8ad0-4057-b908-13a3eb821de1}")
    private String botWebhookUrl;
    @Resource
    protected RestTemplate restTemplate;

    @Override
    public String handle(Map<String, Object> params) {
        log.info("调用企业微信消息发送开始,参数:{}!", JSON.toJSONString(params));
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        String body = JSONObject.toJSONString(params);
        log.info("企业微信消息发送请求体:{}", body);

        HttpEntity httpEntity = new HttpEntity(body, httpHeaders);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(botWebhookUrl, httpEntity, String.class);
        int statusCodeValue = responseEntity.getStatusCodeValue();
        log.info("调用企业微信消息发送返回状态码:{}", statusCodeValue);
        if (HttpStatus.OK.value() == statusCodeValue) {
            String responseEntityBody = responseEntity.getBody();
            log.info("调用消息发送完成,返回数据:{}!", responseEntityBody);
            Map<String, Object> map1 = JSON.parseObject(responseEntityBody, Map.class);
            if (map1.containsKey("errcode") && (int) map1.get("errcode") == 0) {
                return BaseResponse.OP_SUCCESS;
            }
        }
        return BaseResponse.OP_FAILURE;
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.WX.getCode(), ApiCategoryEnum.MESSAGE_NOTIFY.getCode());
    }
}
