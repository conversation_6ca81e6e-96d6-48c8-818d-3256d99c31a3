package com.yhl.scp.dcp.oauth;

import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

/**
 * <code>OAuth2ExceptionHandler</code>
 * <p>
 * OAuth2ExceptionHandler
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-24 11:38:55
 */
@ControllerAdvice
public class OAuth2ExceptionHandler {

    @ExceptionHandler(OAuth2Exception.class)
    public ResponseEntity<OAuth2Error> handleOAuth2Exception(OAuth2Exception ex) {
        OAuth2Error error = new OAuth2Error(
                ex.getOAuth2ErrorCode(),
                ex.getMessage(),
                ex.getAdditionalInformation()
        );
        return ResponseEntity
                .status(ex.getHttpErrorCode())
                .body(error);
    }
}
