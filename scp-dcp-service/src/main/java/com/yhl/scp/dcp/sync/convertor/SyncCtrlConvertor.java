package com.yhl.scp.dcp.sync.convertor;

import com.yhl.scp.dcp.sync.domain.entity.SyncCtrlDO;
import com.yhl.scp.dcp.sync.dto.SyncCtrlDTO;
import com.yhl.scp.dcp.sync.infrastructure.po.SyncCtrlPO;
import com.yhl.scp.dcp.sync.vo.SyncCtrlVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>SyncCtrlConvertor</code>
 * <p>
 * 外部api同步控制表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-16 18:17:39
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SyncCtrlConvertor {

    SyncCtrlConvertor INSTANCE = Mappers.getMapper(SyncCtrlConvertor.class);

    SyncCtrlDO dto2Do(SyncCtrlDTO obj);

    SyncCtrlDTO do2Dto(SyncCtrlDO obj);

    List<SyncCtrlDO> dto2Dos(List<SyncCtrlDTO> list);

    List<SyncCtrlDTO> do2Dtos(List<SyncCtrlDO> list);

    SyncCtrlVO do2Vo(SyncCtrlDO obj);

    SyncCtrlVO po2Vo(SyncCtrlPO obj);

    List<SyncCtrlVO> po2Vos(List<SyncCtrlPO> list);

    SyncCtrlPO do2Po(SyncCtrlDO obj);

    SyncCtrlDO po2Do(SyncCtrlPO obj);

    SyncCtrlDTO vo2Dto(SyncCtrlVO obj);

    SyncCtrlPO dto2Po(SyncCtrlDTO obj);

    List<SyncCtrlPO> dto2Pos(List<SyncCtrlDTO> obj);

}
