package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReleaseRecordDTO;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReleaseRecordFYSLDTO;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * description: 获取FYSL发货记录
 * author：李杰
 * email: <EMAIL>
 * date: 2024/12/2
 */
@Component
@Slf4j
public class ShippingRecordHandler extends SyncDataHandler<List<WarehouseReleaseRecordFYSLDTO>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<WarehouseReleaseRecordFYSLDTO> warehouseReleaseRecordDTOS) {
        Date lastUpdateDate = warehouseReleaseRecordDTOS.stream().map(WarehouseReleaseRecordFYSLDTO::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }
    /**
     * 获取同步分组
     *
     * @param apiConfigVO
     * @param params
     * @return
     */
    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return (String) params.get("organizeId");
    }
    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        if (log.isInfoEnabled()) {
            log.info("开始同步FYSL发货记录:{},{}", apiConfigVO, params);
        }
        try {
            //获取ERP的token
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            //BOM替代料关系地址接口
            String apiUri = apiConfigVO.getApiUri();
            String systemNumber = apiConfigVO.getSystemNumber();
            String lastUpdateStr = this.getSyncRefValue(apiConfigVO, params);
//            String url = apiUri + "/" + systemNumber + "/" + this.sequenceService.getSuffix(systemNumber, getCommand(), 5)
//                    + "?token=" + erpToken + "&orgId=" + params.get("organizeId")
//                    + "&lastUpdateDate=" + lastUpdateStr;
            String url = apiUri + "?token=" + erpToken + "&orgId=" + params.get("organizeId")
                    + "&lastUpdateDate=" + lastUpdateStr;
            if (log.isInfoEnabled()) {
                log.info("erpToken={},apiUri={},systemNumber={},url={}", erpToken, apiUri
                        , systemNumber, url);
            }
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                throw new BusinessException(StrUtil.format("同步FYSL发货记录失败,HTTP状态码:{}!", statusCodeValue));
            }
            ErpResponse erpResponse = JSONObject.parseObject(responseEntity.getBody(), ErpResponse.class);
            if (log.isInfoEnabled()) {
                log.info("同步FYSL发货记录完成,返回数据:{}!", JSONUtil.toJsonStr(erpResponse));
            }
            if (erpResponse.getSuccess()) {
                extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
                return JSONObject.toJSONString(erpResponse.getData());
            } else {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                log.error("同步FYSL发货记录报错，{}!", erpResponse.getMessage());
                return null;
            }
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    protected List<WarehouseReleaseRecordFYSLDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            return null;
        }
        List<WarehouseReleaseRecordFYSLDTO> list = JSONObject.parseArray(body, WarehouseReleaseRecordFYSLDTO.class);
        return list;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<WarehouseReleaseRecordFYSLDTO> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(list)) {
            log.error("ERP同步FYSL发货记录数据为空");
            return null;
        }
        //获取模块标识
        BaseResponse<String> defaultScenario1 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        String scenario = defaultScenario1.getData();
        List<WarehouseReleaseRecordDTO> arrayList = new ArrayList<>();
        list.stream().forEach(x -> {
            WarehouseReleaseRecordDTO warehouseReleaseRecordDTO = new WarehouseReleaseRecordDTO();
            BeanUtils.copyProperties(x, warehouseReleaseRecordDTO);
            warehouseReleaseRecordDTO.setSourceType("ERP");
            arrayList.add(warehouseReleaseRecordDTO);
        });
        dfpFeign.syncWareHouseReleaseFYSLData(scenario, arrayList);
        this.saveSyncCtrl(apiConfigVO, params, list);
        return "同步数据成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(), ApiCategoryEnum.SHIPPING_RECORD.getCode());
    }
}
