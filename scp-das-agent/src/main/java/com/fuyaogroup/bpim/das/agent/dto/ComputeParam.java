package com.fuyaogroup.bpim.das.agent.dto;

import lombok.Data;

/**
 * <code>计算求解参数</code>
 * <p>
 * ComputeParamDto
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-15 13:41:06
 */
@Data
public class ComputeParam {
    /**
     * 算法求解类型（ams）
     */
    private String type;
    /**
     * 目录路径
     */
    private String directory;
    /**
     * 过程日志路径
     */
    private String processLogPath;
    /**
     * 输出路径(${workspace}/executionNumber/daily/output_inner)
     */
    private String outputPath;
    /**
     * 执行编号
     */
    private String executionNumber;
}
