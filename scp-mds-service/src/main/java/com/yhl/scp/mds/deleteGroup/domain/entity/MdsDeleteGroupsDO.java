package com.yhl.scp.mds.deleteGroup.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MdsDeleteGroupsDO</code>
 * <p>
 * ERP删除组接口中间表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-14 20:24:53
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MdsDeleteGroupsDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -62451752812455204L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 库存点代码
     */
    private String stockPointCode;
    /**
     * 库存点名称
     */
    private String stockPointName;
    /**
     * 删除组
     */
    private String deleteGroupName;
    /**
     * 删除组类型（1:物料,2:清单,3:工艺路线,4:组件,5:工序,6:清单、工艺路线,7:物料、清单、工艺路线）
     */
    private String deleteType;
    /**
     * 删除组头层
     */
    private String itemConcatSegments;
    /**
     * 头层说明
     */
    private String itemDescription;
    /**
     * 删除类型（1:物料,2:物料清单,3:工艺路线,4:组件,5:工序）
     */
    private String deleteEntity;
    /**
     * 删除BOM头ID
     */
    private String billSequenceId;
    /**
     * 删除BOM行ID
     */
    private String componentSequenceId;
    /**
     * 删除工艺头ID
     */
    private String routingSequenceId;
    /**
     * 删除工艺行ID
     */
    private String operationSequenceId;
    /**
     * 行层工序
     */
    private String operationSeqNum;
    /**
     * 工艺路线行层部门
     */
    private String operationDepartmentCode;
    /**
     * 行层说明
     */
    private String description;
    /**
     * 删除状态
     */
    private String deleteStatus;
    /**
     * BOM行层组件
     */
    private String componentConcatSegments;
    /**
     * BOM行层序号
     */
    private String itemNum;
    /**
     * 生效时间
     */
    private Date effectivityDate;
    /**
     * 删除时间
     */
    private Date deleteDate;
    /**
     * 最后更新时间
     */
    private Date lastUpdateDate;
    /**
     * 版本
     */
    private Integer versionValue;

}
