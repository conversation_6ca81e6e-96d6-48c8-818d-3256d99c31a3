package com.yhl.scp.mds.job;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yhl.scp.mds.routing.service.*;
import com.yhl.scp.mds.routing.vo.ProductCandidateResourceVO;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.bom.service.MdsProductBomService;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.routing.vo.ProductRoutingVO;

import lombok.extern.slf4j.Slf4j;

/**
 * <code>RoutingJob</code>
 * <p>
 * 产品工艺路径转物品工艺路径
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-07 20:33:57
 */
@Component
@Slf4j
public class AllTransitionRoutingJob {

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private ProductRoutingService productRoutingService;

    @Resource
    private NewRoutingService newRoutingService;

    @Resource
    private NewRoutingStepService newRoutingStepService;

    @Resource
    private MdsProductBomService mdsProductBomService;

    @Resource
    private NewRoutingStepInputService newRoutingStepInputService;

    @Resource
    private NewProductCandidateResourceService newProductCandidateResourceService;

    @Resource
    private NewRoutingStepResourceService newRoutingStepResourceService;

    @Resource
    private NewStockPointService newStockPointService;

    @XxlJob("allTransitionRoutingJob")
    private ReturnT<String> allTransitionRoutingJob() {
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MDS.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            log.warn("租户下不存在MDS模块信息");
            return ReturnT.FAIL;
        }
        for (Scenario scenario : scenarios) {
            log.info("开始处理scenario：{}下的产品工艺路径转物品工艺路径job", scenario);
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            // 1.工艺路径，工艺路径步骤转化处理
            List<ProductRoutingVO> productRoutingList = productRoutingService.selectAll();
            List<String> routingSequenceIds = productRoutingList.stream().map(ProductRoutingVO::getRoutingSequenceId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(routingSequenceIds)) {
                newRoutingService.doTransitionRouting(routingSequenceIds, scenario.getDataBaseName());
            }
            // 2.输入物品
            List<ProductBomVO> productBomVO = mdsProductBomService.selectAll();
            List<String> componentSequenceId = productBomVO.stream().map(ProductBomVO::getComponentSequenceId)
                    .distinct().collect(Collectors.toList());
            List<NewStockPointVO> stockPoints = newStockPointService.selectAll();
            newRoutingStepInputService.doTransitionRoutingStepInput(componentSequenceId, null, null,
                    stockPoints, scenario.getDataBaseName());
            // 3.候选资源
            List<ProductCandidateResourceVO> productCandidateResourceList = newProductCandidateResourceService.selectAll();
            List<String> productCandidateResourceIds = productCandidateResourceList.stream()
                    .map(ProductCandidateResourceVO::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(productCandidateResourceIds)) {
                newRoutingStepResourceService.doTransitionRoutingStepResource(productCandidateResourceIds,
                        null, null, stockPoints, scenario.getDataBaseName());
            }
            // 4.维护第一道工序的输入物品，输出物品
            newRoutingStepService.doCreatFirstStepInputAndOut();
            DynamicDataSourceContextHolder.clearDataSource();
            log.info("scenario：{}下的产品工艺路径转物品工艺路径job结束", scenario);
        }
        return ReturnT.SUCCESS;
    }

}