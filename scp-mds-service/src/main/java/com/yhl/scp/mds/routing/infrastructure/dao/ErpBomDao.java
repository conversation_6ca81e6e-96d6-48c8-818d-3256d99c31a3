package com.yhl.scp.mds.routing.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mds.routing.infrastructure.po.ErpBomPO;
import com.yhl.scp.mds.routing.vo.ErpBomVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>ErpBomDao</code>
 * <p>
 * erp同步bom主数据DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-26 15:06:05
 */
public interface ErpBomDao extends BaseDao<ErpBomPO, ErpBomVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link ErpBomVO}
     */
    List<ErpBomVO> selectVOByParams(@Param("params") Map<String, Object> params);

}
