package com.yhl.scp.mds.routing.service.impl;


import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.product.infrastructure.po.StockPointPO;
import com.yhl.scp.mds.extension.routing.infrastructure.po.RoutingPO;
import com.yhl.scp.mds.extension.routing.infrastructure.po.RoutingStepPO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.newproduct.infrastructure.dao.NewProductStockPointDao;
import com.yhl.scp.mds.newproduct.infrastructure.po.NewProductStockPointPO;
import com.yhl.scp.mds.product.infrastructure.dao.StockPointDao;
import com.yhl.scp.mds.routing.dto.RoutingStepInputSyncDTO;
import com.yhl.scp.mds.routing.infrastructure.dao.RoutingDao;
import com.yhl.scp.mds.routing.infrastructure.dao.RoutingStepDao;
import com.yhl.scp.mds.routing.infrastructure.dao.RoutingStepInputNewDao;
import com.yhl.scp.mds.routing.infrastructure.po.RoutingStepInputNewPO;
import com.yhl.scp.mds.routing.service.RoutingStepInputNewService;

import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
public class RoutingStepInputNewServiceImpl implements RoutingStepInputNewService {

    @Resource
    private RoutingStepInputNewDao inputNewDao;

    @Resource
    private RoutingDao routingDao;

    @Resource
    private RoutingStepDao routingStepDao;

    @Resource
    private NewProductStockPointDao newProductStockPointDao;

    @Resource
    private StockPointDao stockPointDao;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Override
    public BaseResponse<String> doSyncData(List<RoutingStepInputSyncDTO> bomSyncDTOList) {
        String operator = SystemHolder.getUserId() == null ? "-1" : SystemHolder.getUserId();
        Date date = new Date();
        List<RoutingStepInputNewPO> insertPoList = new ArrayList<>();
        List<RoutingStepInputNewPO> updatePoList = new ArrayList<>();
        bomSyncDTOList.stream().forEach(x -> {
            //查看当前表是否有数据
            HashMap<String, Object> sameDataMap = MapUtil.newHashMap();
            sameDataMap.put("headerId", x.getBillSequenceId());//头id
            sameDataMap.put("rowId", x.getComponentSequenceId());//行id
            List<RoutingStepInputNewPO> routingStepInputPOS = inputNewDao.selectByParams(sameDataMap);
            if (routingStepInputPOS.isEmpty()) {
                RoutingStepInputNewPO routingStepInputNewPO = new RoutingStepInputNewPO();
                //通过组织代码（库存点编码）获取库存点id
                HashMap<String, Object> map = MapUtil.newHashMap();
                map.put("stockPointCode", x.getOrganizationCode());
                List<StockPointPO> stockPointPOS = stockPointDao.selectByParams(map);
                if (!stockPointPOS.isEmpty()) {
                    String StockPointId = stockPointPOS.get(0).getId();
                    routingStepInputNewPO.setStockPointId(StockPointId);
                    //通过库存点id和本厂编号于生产路径获取路径id
                    HashMap<String, Object> map1 = MapUtil.newHashMap();
                    map1.put("stockPointId", StockPointId);
                    map1.put("productCode", x.getAssemblyItemCode());
                    List<RoutingPO> routingPOS = routingDao.selectByParams(map1);
                    if (!routingPOS.isEmpty()) {
                        String routingId = routingPOS.get(0).getId();
                        routingStepInputNewPO.setRoutingId(routingId);
                        //通过路径id和工序（顺序号）获取工艺id
                        HashMap<String, Object> map2 = MapUtil.newHashMap();
                        map2.put("routingId", routingId);
                        map2.put("sequenceNo", x.getOperationSeqNum());
                        List<RoutingStepPO> routingStepPOS = routingStepDao.selectByParams(map2);
                        if (!routingStepPOS.isEmpty()) {
                            routingStepInputNewPO.setRoutingStepId(routingStepPOS.get(0).getId());
                        }
                    }
                }
                //通过组件（输入物品）和组织代码（库存点编码）获取输入物品id
                map.put("productCode", x.getComponentItemCode());
                List<NewProductStockPointPO> productStockPointPOS = newProductStockPointDao.selectByParams(map);
                if (!productStockPointPOS.isEmpty()) {
                    routingStepInputNewPO.setInputProductId(productStockPointPOS.get(0).getId());
                }
                routingStepInputNewPO.setYield(x.getComponentYieldFactor()); //成品率
                routingStepInputNewPO.setInputFactor(x.getComponentQuantity());//单位输入量
                routingStepInputNewPO.setEffectiveTime(x.getStartDate());//生效时间
                routingStepInputNewPO.setExpiryTime(x.getDisableDate());//失效时间
                routingStepInputNewPO.setSupplyType(x.getWipSupplyType());//供应类型
                routingStepInputNewPO.setHeaderId(x.getBillSequenceId());//头id
                routingStepInputNewPO.setRowId(x.getComponentSequenceId());//行id
                routingStepInputNewPO.setLastUpdateDate(x.getLastUpdateDate());
                insertPoList.add(routingStepInputNewPO);
            } else {
                RoutingStepInputNewPO oldPo = routingStepInputPOS.get(0);
                if (Objects.isNull(oldPo.getLastUpdateDate()) || Objects.isNull(x.getLastUpdateDate())
                        || oldPo.getLastUpdateDate().compareTo(x.getLastUpdateDate()) == -1) {
                    oldPo.setModifier(operator);
                    oldPo.setModifyTime(date);
                    oldPo.setYield(x.getComponentYieldFactor()); //成品率
                    oldPo.setInputFactor(x.getComponentQuantity());//单位输入量
                    oldPo.setEffectiveTime(x.getStartDate());//生效时间
                    oldPo.setExpiryTime(x.getDisableDate());//失效时间
                    oldPo.setSupplyType(x.getWipSupplyType());//供应类型
                    oldPo.setLastUpdateDate(x.getLastUpdateDate());

                    //通过组织代码（库存点编码）获取库存点id
                    HashMap<String, Object> map = MapUtil.newHashMap();
                    map.put("stockPointCode", x.getOrganizationCode());
                    List<StockPointPO> stockPointPOS = stockPointDao.selectByParams(map);
                    if (!stockPointPOS.isEmpty()) {
                        String StockPointId = stockPointPOS.get(0).getId();
                        oldPo.setStockPointId(StockPointId);
                        //通过库存点id和本厂编号于生产路径获取路径id
                        HashMap<String, Object> map1 = MapUtil.newHashMap();
                        map1.put("stockPointId", StockPointId);
                        map1.put("productCode", x.getAssemblyItemCode());
                        List<RoutingPO> routingPOS = routingDao.selectByParams(map1);
                        if (!routingPOS.isEmpty()) {
                            String routingId = routingPOS.get(0).getId();
                            oldPo.setRoutingId(routingId);
                            //通过路径id和工序（顺序号）获取工艺id
                            HashMap<String, Object> map2 = MapUtil.newHashMap();
                            map2.put("routingId", routingId);
                            map2.put("sequenceNo", x.getOperationSeqNum());
                            List<RoutingStepPO> routingStepPOS = routingStepDao.selectByParams(map2);
                            if (!routingStepPOS.isEmpty()) {
                                oldPo.setRoutingStepId(routingStepPOS.get(0).getId());
                            }
                        }
                    }
                    //通过组件（输入物品）和组织代码（库存点编码）获取输入物品id
                    map.put("productCode", x.getComponentItemCode());
                    List<NewProductStockPointPO> productStockPointPOS = newProductStockPointDao.selectByParams(map);
                    if (!productStockPointPOS.isEmpty()) {
                        oldPo.setInputProductId(productStockPointPOS.get(0).getId());
                    }
                    updatePoList.add(oldPo);
                }
            }

        });
        if (!insertPoList.isEmpty()) {
            BasePOUtils.insertBatchFiller(insertPoList);
            inputNewDao.insertBatchWithPrimaryKey(insertPoList);
        }
        if (!updatePoList.isEmpty()) {
            inputNewDao.updateBatch(updatePoList);
        }
        return null;
    }

    @Override
    public BaseResponse<Void> syncData(String beginTime, String orgCode, String tenantCode) {
        log.info("开始同步生产路径步骤输入物品数据");
        try {
            HashMap<String, Object> map = MapUtil.newHashMap();
            map.put("organizationCode", orgCode);
            if (StringUtils.isEmpty(tenantCode)) {
                tenantCode = SystemHolder.getTenantId();
                map.put("triggerType", YesOrNoEnum.YES.getCode());
            }
            newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.ERP.getCode(),
                    ApiCategoryEnum.ROUTING_STEP_INPUT.getCode(), map);
            return BaseResponse.success("数据同步成功");
        } catch (Exception e) {
            log.error("数据执行报错，{}", e.getMessage());
            return BaseResponse.error("数据执行报错，" + e.getMessage());
        }
    }

	@Override
	public List<RoutingStepInputVO> selectByParams(Map<String, Object> params) {
		return inputNewDao.selectVOByParams(params);
	}
}
