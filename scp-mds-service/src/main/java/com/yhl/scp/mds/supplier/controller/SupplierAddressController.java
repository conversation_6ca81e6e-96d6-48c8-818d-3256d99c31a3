package com.yhl.scp.mds.supplier.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mds.supplier.dto.SupplierAddressDTO;
import com.yhl.scp.mds.supplier.service.SupplierAddressService;
import com.yhl.scp.mds.supplier.vo.SupplierAddressVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>SupplierAddressController</code>
 * <p>
 * 供应商地址控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-30 19:49:24
 */
@Slf4j
@Api(tags = "供应商地址控制器")
@RestController
@RequestMapping("supplierAddress")
public class SupplierAddressController extends BaseController {

    @Resource
    private SupplierAddressService supplierAddressService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<SupplierAddressVO>> page() {
        List<SupplierAddressVO> supplierAddressList = supplierAddressService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<SupplierAddressVO> pageInfo = new PageInfo<>(supplierAddressList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody SupplierAddressDTO supplierAddressDTO) {
        return supplierAddressService.doCreate(supplierAddressDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody SupplierAddressDTO supplierAddressDTO) {
        return supplierAddressService.doUpdate(supplierAddressDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        supplierAddressService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<SupplierAddressVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, supplierAddressService.selectByPrimaryKey(id));
    }

}
