package com.yhl.scp.mds.routing.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.date.StopWatch;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.utils.BulkOperationUtils;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.baseResource.service.PhysicalResourceService;
import com.yhl.scp.mds.baseResource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.bom.service.MdsProductBomService;
import com.yhl.scp.mds.bom.service.MdsProductBomVersionService;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.routing.convertor.NewRoutingStepConvertor;
import com.yhl.scp.mds.routing.convertor.NewRoutingStepInputConvertor;
import com.yhl.scp.mds.routing.convertor.NewRoutingStepOutputConvertor;
import com.yhl.scp.mds.routing.convertor.NewRoutingStepResourceConvertor;
import com.yhl.scp.mds.routing.domain.entity.NewRoutingStepDO;
import com.yhl.scp.mds.routing.domain.service.NewRoutingStepDomainService;
import com.yhl.scp.mds.routing.dto.NewRoutingStepDTO;
import com.yhl.scp.mds.routing.dto.NewRoutingStepInputDTO;
import com.yhl.scp.mds.routing.dto.NewRoutingStepOutputDTO;
import com.yhl.scp.mds.routing.dto.NewRoutingStepResourceDTO;
import com.yhl.scp.mds.routing.infrastructure.dao.NewRoutingStepDao;
import com.yhl.scp.mds.routing.infrastructure.dao.NewRoutingStepInputDao;
import com.yhl.scp.mds.routing.infrastructure.dao.NewRoutingStepOutputDao;
import com.yhl.scp.mds.routing.infrastructure.dao.NewRoutingStepResourceDao;
import com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepInputPO;
import com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepOutputPO;
import com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepPO;
import com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepResourcePO;
import com.yhl.scp.mds.routing.service.NewRoutingService;
import com.yhl.scp.mds.routing.service.NewRoutingStepInputService;
import com.yhl.scp.mds.routing.service.NewRoutingStepService;
import com.yhl.scp.mds.routing.vo.NewRoutingStepInputVO;
import com.yhl.scp.mds.routing.vo.NewRoutingStepVO;
import com.yhl.scp.mds.routing.vo.NewRoutingVO;

import lombok.extern.slf4j.Slf4j;

/**
 * <code>RoutingStepServiceImpl</code>
 * <p>
 * 生产路径步骤应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20 11:27:00
 */
@Slf4j
@Service
public class NewRoutingStepServiceImpl extends AbstractService implements NewRoutingStepService {

    private static final String CREATE_SYSTEM_JOB = "system-job";

    private static final String CREATE_SYSTEM_OUT_JOB = "system-out-job";

    private static final String DEFAULT_RESOURCE_CODE = "S2XL01";

    @Resource
    private NewRoutingStepDao newRoutingStepDao;

    @Resource
    private NewRoutingStepDomainService newRoutingStepDomainService;

    @Resource
    private NewRoutingService newRoutingService;

    @Resource
    private NewRoutingStepInputService newRoutingStepInputService;

    @Resource
    private MdsProductBomService mdsProductBomService;

    @Resource
    private MdsProductBomVersionService mdsProductBomVersionService;

    @Resource
    private NewRoutingStepInputDao newRoutingStepInputDao;

    @Resource
    private NewRoutingStepOutputDao newRoutingStepOutputDao;

    @Resource
    private NewRoutingStepResourceDao newRoutingStepResourceDao;

    @Resource
    private PhysicalResourceService physicalResourceService;

    @Override
    public BaseResponse<Void> doCreate(NewRoutingStepDTO routingStepDTO) {
        // 0.数据转换
        NewRoutingStepDO routingStepDO = NewRoutingStepConvertor.INSTANCE.dto2Do(routingStepDTO);
        NewRoutingStepPO routingStepPO = NewRoutingStepConvertor.INSTANCE.dto2Po(routingStepDTO);
        // 1.数据校验
        newRoutingStepDomainService.validation(routingStepDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(routingStepPO);
        newRoutingStepDao.insertWithPrimaryKey(routingStepPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(NewRoutingStepDTO routingStepDTO) {
        // 0.数据转换
        NewRoutingStepDO routingStepDO = NewRoutingStepConvertor.INSTANCE.dto2Do(routingStepDTO);
        NewRoutingStepPO routingStepPO = NewRoutingStepConvertor.INSTANCE.dto2Po(routingStepDTO);
        // 1.数据校验
        newRoutingStepDomainService.validation(routingStepDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(routingStepPO);
        newRoutingStepDao.update(routingStepPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<NewRoutingStepDTO> list) {
        List<NewRoutingStepPO> newList = NewRoutingStepConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
                newRoutingStepDao.insertBatchWithPrimaryKey(poList), 2500);
    }

    @Override
    public void doUpdateBatch(List<NewRoutingStepDTO> list) {
        List<NewRoutingStepPO> newList = NewRoutingStepConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
                newRoutingStepDao.updateBatch(poList), 2500);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return newRoutingStepDao.deleteBatch(idList);
        }
        return newRoutingStepDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public NewRoutingStepVO selectByPrimaryKey(String id) {
        NewRoutingStepPO po = newRoutingStepDao.selectByPrimaryKey(id);
        return NewRoutingStepConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mds_rou_routing_step")
    public List<NewRoutingStepVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mds_rou_routing_step")
    public List<NewRoutingStepVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<NewRoutingStepVO> dataList = newRoutingStepDao.selectByCondition(sortParam, queryCriteriaParam);
        NewRoutingStepServiceImpl target = SpringBeanUtils.getBean(NewRoutingStepServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<NewRoutingStepVO> selectByParams(Map<String, Object> params) {
        List<NewRoutingStepPO> list = newRoutingStepDao.selectByParams(params);
        return NewRoutingStepConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<NewRoutingStepVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.NEW_ROUTING_STEP.getCode();
    }

    @Override
    public List<NewRoutingStepVO> invocation(List<NewRoutingStepVO> dataList, Map<String, Object> params,
											 String invocation) {
        return dataList;
    }

    @Override
    public List<NewRoutingStepVO> selectVOByPrimaryKeys(List<String> routingStepIds) {
        List<NewRoutingStepPO> routingStepPOList = newRoutingStepDao.selectByPrimaryKeys(routingStepIds);
        return NewRoutingStepConvertor.INSTANCE.po2Vos(routingStepPOList);
    }

    @Override
    public void doCreateBatchWithPrimaryKey(List<NewRoutingStepDTO> list) {
        List<NewRoutingStepPO> newList = NewRoutingStepConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
				newRoutingStepDao.insertBatchWithPrimaryKey(poList), 2500);
    }

    @Override
    public void doUpdateBatchSelective(List<NewRoutingStepDTO> list) {
        List<NewRoutingStepPO> newList = NewRoutingStepConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
				newRoutingStepDao.updateBatchSelective(poList), 2500);
    }

    @Override
    public void doLogicDeleteBatchByRoutingIds(List<String> routingIds) {
        newRoutingStepDao.doLogicDeleteBatchByRoutingIds(routingIds);
    }

    @Override
    public void doLogicDeleteBatchByIds(List<String> ids) {
        newRoutingStepDao.doLogicDeleteBatchByIds(ids);
    }

    @Override
    public void doCreatFirstStepInputAndOut() {
        StopWatch stopWatch = new StopWatch("第一道工序的输入物品，输出物品处理");
        // 1.查询所有有效的工艺路径步骤，按照routingId分组，按照sequenceNo正序排序
        stopWatch.start("查询有效的工艺路径");
        List<NewRoutingVO> routingVOList = newRoutingService.selectByParams(ImmutableMap
				.of("enabled", YesOrNoEnum.YES.getCode()));
        stopWatch.stop();
        if (CollectionUtils.isEmpty(routingVOList)) {
            return;
        }
        List<String> routingProductIds = routingVOList.stream().map(NewRoutingVO::getProductId)
				.distinct().collect(Collectors.toList());
        List<String> routingIds = routingVOList.stream().map(NewRoutingVO::getId).collect(Collectors.toList());

        stopWatch.start("查询有效的路径步骤");
        List<NewRoutingStepPO> routingStepList = newRoutingStepDao.selectByParams(ImmutableMap
				.of("enabled", YesOrNoEnum.YES.getCode(), "routingIds", routingIds));
        stopWatch.stop();

        Map<String, List<NewRoutingStepPO>> routingStepMap = routingStepList.stream()
				.collect(Collectors.groupingBy(NewRoutingStepPO::getRoutingId));
        Map<String, String> stepRoutingIdMap = routingStepList.stream().collect(Collectors
				.toMap(NewRoutingStepPO::getId, NewRoutingStepPO::getRoutingId, (v1, v2) -> v1));
        // 2.获取所有工艺路径的第二道工艺路径步骤的输入物品，判断这个输入物品是否是毛胚
        Map<String, String> firstStepMap = new HashMap<>();
        for (Entry<String, List<NewRoutingStepPO>> routingStepEntry : routingStepMap.entrySet()) {
            List<NewRoutingStepPO> routingGroupList = routingStepEntry.getValue();
            if (CollectionUtils.isEmpty(routingGroupList) || routingGroupList.size() < 2) {
                continue;
            }
            routingGroupList = routingGroupList.stream().sorted(Comparator
					.comparing(NewRoutingStepPO::getSequenceNo)).collect(Collectors.toList());
            firstStepMap.put(routingGroupList.get(1).getId(), routingGroupList.get(0).getId());
        }
        List<String> secondStepIds = new ArrayList<>(firstStepMap.keySet());

        stopWatch.start("查询毛胚的输入物品");
        List<NewRoutingStepInputVO> inputList = newRoutingStepInputService.selectMbplByStepIds(secondStepIds);
        stopWatch.stop();
        if (CollectionUtils.isEmpty(inputList)) {
            // 维护所有工艺路径的最后一道步骤的输出物品
            batchAddLastRoutingStepOutput(routingVOList, routingStepMap);
            return;
        }
        // 3.将这个毛胚当成前工序的输出物品
        List<NewRoutingStepOutputDTO> insertBatchOutputList = new ArrayList<>();
        Map<String, List<String>> productStepMap = new HashMap<>();
        for (NewRoutingStepInputVO newRoutingStepInputVO : inputList) {
            NewRoutingStepOutputDTO add = new NewRoutingStepOutputDTO();
            add.setId(newRoutingStepInputVO.getId());
            add.setRoutingId(newRoutingStepInputVO.getRoutingId());
            add.setRoutingStepId(firstStepMap.get(newRoutingStepInputVO.getRoutingStepId()));
            add.setOutputProductId(newRoutingStepInputVO.getInputProductId());
            add.setStockPointId(newRoutingStepInputVO.getStockPointId());
            add.setOutputFactor(BigDecimal.ONE);
            add.setYield(BigDecimal.ONE);
            add.setEnabled(YesOrNoEnum.YES.getCode());
            add.setEffective(YesOrNoEnum.YES.getCode());
            insertBatchOutputList.add(add);
            // 按照产品id，维护对应的工艺路径步骤id
            List<String> routingStepIds = productStepMap.get(add.getOutputProductId());
            if (CollectionUtils.isEmpty(routingStepIds)) {
                routingStepIds = new ArrayList<>();
            }
            routingStepIds.add(add.getRoutingStepId());
            productStepMap.put(add.getOutputProductId(), routingStepIds);
        }
        // 4.根据毛胚ID,去mds_rou_product_bom.product_id 找到对应的数据
        List<String> inputProductIds = new ArrayList<>(productStepMap.keySet());
        stopWatch.start("查询bom版本数据");
        List<ProductBomVersionVO> productBomVersionVOList = mdsProductBomVersionService.selectByParams(ImmutableMap
				.of("enabled", YesOrNoEnum.YES.getCode(), "productIds", inputProductIds));
        stopWatch.stop();

        stopWatch.start("核心处理逻辑");
        List<NewRoutingStepInputDTO> insertBatchInputList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(productBomVersionVOList)) {
            List<String> bomVersionIds = productBomVersionVOList.stream().map(ProductBomVersionVO::getId)
					.collect(Collectors.toList());
            Map<String, String> bomVersionMap = productBomVersionVOList.stream().collect(Collectors
					.toMap(ProductBomVersionVO::getId, ProductBomVersionVO::getProductId, (v1, v2) -> v1));
            List<ProductBomVO> productBomVOList = mdsProductBomService.selectByParams(ImmutableMap
					.of("enabled", YesOrNoEnum.YES.getCode(), "bomVersionIds", bomVersionIds));
            Map<String, List<ProductBomVO>> productBomVOMap = productBomVOList.stream().collect(Collectors
					.groupingBy(ProductBomVO::getBomVersionId));
            for (Entry<String, List<ProductBomVO>> productBomVOEntry : productBomVOMap.entrySet()) {
                String bomVersionId = productBomVOEntry.getKey();
                String productId = bomVersionMap.get(bomVersionId);
                List<String> routingStepIds = productStepMap.get(productId);
                List<ProductBomVO> addProductBomVOList = productBomVOEntry.getValue();
                for (String routingStepId : routingStepIds) {
                    // 维护输入物品数据
                    for (ProductBomVO productBomVO : addProductBomVOList) {
                        String keyMaterial = YesOrNoEnum.NO.getCode();
                        if (routingProductIds.contains(productBomVO.getIoProductId())) {
                            keyMaterial = YesOrNoEnum.YES.getCode();
                        }
                        NewRoutingStepInputDTO newRoutingStepInputDTO = new NewRoutingStepInputDTO();
                        newRoutingStepInputDTO.setId(UUIDUtil.getUUID());
                        initNewRoutingStepInput(stepRoutingIdMap, routingStepId, productBomVO, keyMaterial,
								newRoutingStepInputDTO);
                        insertBatchInputList.add(newRoutingStepInputDTO);
                    }
                }
            }
        }
        stopWatch.stop();
        // 处理数据，先删除之前新增的，然后再进行新增
        newRoutingStepOutputDao.deleteByCreator(CREATE_SYSTEM_JOB);
        newRoutingStepInputDao.deleteByCreator(CREATE_SYSTEM_JOB);

        stopWatch.start("数据入库");
        if (CollectionUtils.isNotEmpty(insertBatchOutputList)) {
            List<NewRoutingStepOutputPO> newList = NewRoutingStepOutputConvertor.INSTANCE.dto2Pos(insertBatchOutputList);
            BasePOUtils.updateBatchFiller(newList);
            newList.forEach(e -> {
                e.setCreateTime(new Date());
                e.setCreator(CREATE_SYSTEM_JOB);
            });
            BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
                    newRoutingStepOutputDao.insertBatchWithPrimaryKey(poList), 2500);
        }
        if (CollectionUtils.isNotEmpty(insertBatchInputList)) {
            List<NewRoutingStepInputPO> newList = NewRoutingStepInputConvertor.INSTANCE.dto2Pos(insertBatchInputList);
            BasePOUtils.updateBatchFiller(newList);
            newList.forEach(e -> {
                e.setCreateTime(new Date());
                e.setCreator(CREATE_SYSTEM_JOB);
            });
            BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
                    newRoutingStepInputDao.insertBatchWithPrimaryKey(poList), 2500);
        }
        stopWatch.stop();

        // 维护所有工艺路径的最后一道步骤的输出物品
        stopWatch.start("维护所有工艺路径的最后一道步骤的输出物品");
        batchAddLastRoutingStepOutput(routingVOList, routingStepMap);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
    }

    /**
     * 维护所有工艺路径的最后一道步骤的输出物品
     * @param routingList 工艺路径
     * @param routingStepMap 工艺路径步骤
     */
    private void batchAddLastRoutingStepOutput(List<NewRoutingVO> routingList,
                                               Map<String, List<NewRoutingStepPO>> routingStepMap) {
        List<NewRoutingStepOutputPO> resultList = new ArrayList<>();
        Date date = new Date();
        for (NewRoutingVO routing : routingList) {
            String id = routing.getId();
            List<NewRoutingStepPO> steps = routingStepMap.get(id);
            if (CollectionUtils.isEmpty(steps)) {
                continue;
            }
            NewRoutingStepPO routingStep = steps.stream().sorted(Comparator
                    .comparing(NewRoutingStepPO::getSequenceNo).reversed()).collect(Collectors.toList()).get(0);
            NewRoutingStepOutputPO routingStepOutputDO = new NewRoutingStepOutputPO();
            routingStepOutputDO.setId(UUIDUtil.getUUID());
            routingStepOutputDO.setRoutingId(id);
            routingStepOutputDO.setRoutingStepId(routingStep.getId());
            routingStepOutputDO.setOutputProductId(routing.getProductId());
            routingStepOutputDO.setStockPointId(routing.getStockPointId());
            routingStepOutputDO.setOutputFactor(BigDecimal.ONE);
            routingStepOutputDO.setYield(BigDecimal.ONE);
            routingStepOutputDO.setMainProduct(YesOrNoEnum.YES.getCode());
            routingStepOutputDO.setModifier(CREATE_SYSTEM_OUT_JOB);
            routingStepOutputDO.setCreator(CREATE_SYSTEM_OUT_JOB);
            routingStepOutputDO.setCreateTime(date);
            routingStepOutputDO.setModifyTime(date);
            resultList.add(routingStepOutputDO);
        }
        newRoutingStepOutputDao.deleteByCreator(CREATE_SYSTEM_OUT_JOB);
        if (CollectionUtils.isNotEmpty(resultList)) {
            BulkOperationUtils.bulkUpdateOrCreate(resultList, poList ->
                    newRoutingStepOutputDao.insertBatchWithPrimaryKey(poList), 2500);
        }
    }

    private void initNewRoutingStepInput(Map<String, String> stepRoutingIdMap, String routingStepId,
                                         ProductBomVO productBomVO, String keyMaterial,
                                         NewRoutingStepInputDTO newRoutingStepInputDTO) {
        newRoutingStepInputDTO.setRoutingId(stepRoutingIdMap.get(routingStepId));
        newRoutingStepInputDTO.setRoutingStepId(routingStepId);
        newRoutingStepInputDTO.setInputProductId(productBomVO.getIoProductId());
        newRoutingStepInputDTO.setStockPointId(productBomVO.getIoStockPointId());
        newRoutingStepInputDTO.setMainMaterial(productBomVO.getMainMaterial());
        newRoutingStepInputDTO.setKeyMaterial(keyMaterial);
        newRoutingStepInputDTO.setYield(productBomVO.getYield());
        newRoutingStepInputDTO.setScrap(productBomVO.getScrap());
        newRoutingStepInputDTO.setInputFactor(productBomVO.getIoFactor());
        newRoutingStepInputDTO.setUnitProductionCost(productBomVO.getUnitProductionCost());
        newRoutingStepInputDTO.setScrapStrategy(productBomVO.getScrapStrategy());
        newRoutingStepInputDTO.setPercentageScrapRate(productBomVO.getPercentageScrapRate());
        newRoutingStepInputDTO.setAltMode(productBomVO.getAltMode());
        newRoutingStepInputDTO.setAltRatio(productBomVO.getAltRatio());
        newRoutingStepInputDTO.setAltMaterialGroup(productBomVO.getAltMaterialGroup());
        newRoutingStepInputDTO.setMatchCode(productBomVO.getMatchCode());
        newRoutingStepInputDTO.setConnectionTask(productBomVO.getConnectionTask());
        newRoutingStepInputDTO.setConnectionType(productBomVO.getConnectionType());
        newRoutingStepInputDTO.setMaxConnectionDuration(productBomVO.getMaxConnectionDuration());
        newRoutingStepInputDTO.setMinConnectionDuration(productBomVO.getMinConnectionDuration());
        newRoutingStepInputDTO.setCountingUnitId(productBomVO.getCountingUnitId());
        newRoutingStepInputDTO.setEffective(productBomVO.getEnabled());
        newRoutingStepInputDTO.setEnabled(productBomVO.getEnabled());
        newRoutingStepInputDTO.setSupplyType(productBomVO.getWipSupplyType());
        newRoutingStepInputDTO.setEffectiveTime(productBomVO.getStartTime());
        newRoutingStepInputDTO.setExpiryTime(productBomVO.getEndTime());
        if (newRoutingStepInputDTO.getExpiryTime() != null
                && newRoutingStepInputDTO.getExpiryTime().before(new Date())) {
            newRoutingStepInputDTO.setEnabled(YesOrNoEnum.NO.getCode());
            newRoutingStepInputDTO.setEffective(YesOrNoEnum.NO.getCode());
        }
    }

    @Override
    public void doAddDefaultResource() {
        // 每次都是先删除，后维护
        newRoutingStepResourceDao.deleteByCreator(CREATE_SYSTEM_JOB);
        // 获取指定资源数据信息
        List<PhysicalResourceVO> resourceList = physicalResourceService.selectByParams(ImmutableMap
                .of("enabled", YesOrNoEnum.YES.getCode(), "physicalResourceCode", DEFAULT_RESOURCE_CODE));
        if (CollectionUtils.isEmpty(resourceList)) {
            return;
        }
        String standardResourceId = resourceList.get(0).getStandardResourceId();
        String physicalResourceId = resourceList.get(0).getId();
        // 1.查询所有S2的10工序的工艺路径步骤信息
        List<NewRoutingStepVO> routingStepList = newRoutingStepDao.selectTenRoutingStep();
        // 2.查询候选资源存在的工艺路步骤id
        List<String> routingStepIds = newRoutingStepResourceDao.selectAllRoutingStepIds();
        // 3.没有候选资源的手动维护候选资源
        List<NewRoutingStepResourceDTO> insertBatchResourceList = new ArrayList<>();
        for (NewRoutingStepVO routingStep : routingStepList) {
            String routingStepId = routingStep.getId();
            if (!routingStepIds.contains(routingStepId)) {
                // 新增候选资源
                // 单件生产时间默认： 优先级默认1，清理必要资源量默认1，制造必要资源量默认1，设置必要资源量默认1
                NewRoutingStepResourceDTO newResourceDTO = NewRoutingStepResourceDTO.builder()
                        .id(UUIDUtil.getUUID()).routingId(routingStep.getRoutingId())
                        .routingStepId(routingStep.getId()).standardResourceId(standardResourceId)
                        .physicalResourceId(physicalResourceId).unitProductionTime(BigDecimal.ONE)
                        .setupUnitBatchSize(BigDecimal.ONE).productionUnitBatchSize(BigDecimal.ONE)
                        .cleanupUnitBatchSize(BigDecimal.ONE).priority(1).build();
                insertBatchResourceList.add(newResourceDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(insertBatchResourceList)) {
            List<NewRoutingStepResourcePO> newList = NewRoutingStepResourceConvertor.INSTANCE
                    .dto2Pos(insertBatchResourceList);
            BasePOUtils.insertBatchFiller(newList);
            newList.forEach(e -> {
                e.setEnabled(YesOrNoEnum.YES.getCode());
                e.setEffective(YesOrNoEnum.YES.getCode());
                e.setCreateTime(new Date());
                e.setCreator(CREATE_SYSTEM_JOB);
            });
            BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
                    newRoutingStepResourceDao.insertBatchWithPrimaryKey(poList), 2500);
        }
    }

    @Override
    public void updateEnableForExpiryTime() {
        newRoutingStepDao.updateEnableForExpiryTime();
    }

}