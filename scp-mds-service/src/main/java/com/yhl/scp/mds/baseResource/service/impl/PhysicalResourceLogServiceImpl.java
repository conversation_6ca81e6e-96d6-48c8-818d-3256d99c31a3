package com.yhl.scp.mds.baseResource.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.NormalEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.utils.BasePOUtils;

import com.yhl.scp.mds.baseResource.convertor.PhysicalResourceLogConvertor;
import com.yhl.scp.mds.baseResource.domain.entity.PhysicalResourceLogDO;
import com.yhl.scp.mds.baseResource.domain.service.PhysicalResourceLogDomainService;
import com.yhl.scp.mds.baseResource.dto.PhysicalResourceDTO;
import com.yhl.scp.mds.baseResource.dto.PhysicalResourceLogDTO;
import com.yhl.scp.mds.baseResource.infrastructure.dao.PhysicalResourceLogDao;
import com.yhl.scp.mds.baseResource.infrastructure.dao.StandardResource2Dao;
import com.yhl.scp.mds.baseResource.infrastructure.po.PhysicalResourceLogPO;
import com.yhl.scp.mds.baseResource.infrastructure.po.StandardResourcePO;
import com.yhl.scp.mds.baseResource.service.PhysicalResourceLogService;
import com.yhl.scp.mds.baseResource.service.PhysicalResourceService;
import com.yhl.scp.mds.baseResource.vo.PhysicalResourceLogVO;
import com.yhl.scp.mds.baseResource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.routing.service.StandardStepService;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PhysicalResourceLogServiceImpl extends AbstractService implements PhysicalResourceLogService {

    @Resource
    private PhysicalResourceLogDao physicalResourceLogDao;

    @Resource
    private PhysicalResourceLogDomainService physicalResourceLogDomainService;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private PhysicalResourceService physicalResourceService;

    @Resource
    private StandardResource2Dao standardResource2Dao;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewStockPointService newStockPointService;

    @Resource
    private StandardStepService standardStepService;

    @Override
    public BaseResponse<Void> doCreate(PhysicalResourceLogDTO physicalResourceLogDTO) {
        // 0.数据转换
        PhysicalResourceLogDO physicalResourceLogDO = PhysicalResourceLogConvertor.INSTANCE.dto2Do(physicalResourceLogDTO);
        PhysicalResourceLogPO physicalResourceLogPO = PhysicalResourceLogConvertor.INSTANCE.dto2Po(physicalResourceLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        physicalResourceLogDomainService.validation(physicalResourceLogDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(physicalResourceLogPO);
        physicalResourceLogDao.insert(physicalResourceLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(PhysicalResourceLogDTO physicalResourceLogDTO) {
        // 0.数据转换
        PhysicalResourceLogDO physicalResourceLogDO = PhysicalResourceLogConvertor.INSTANCE.dto2Do(physicalResourceLogDTO);
        PhysicalResourceLogPO physicalResourceLogPO = PhysicalResourceLogConvertor.INSTANCE.dto2Po(physicalResourceLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        physicalResourceLogDomainService.validation(physicalResourceLogDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(physicalResourceLogPO);
        physicalResourceLogDao.update(physicalResourceLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<PhysicalResourceLogDTO> list) {
        List<PhysicalResourceLogPO> newList = PhysicalResourceLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        physicalResourceLogDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<PhysicalResourceLogDTO> list) {
        List<PhysicalResourceLogPO> newList = PhysicalResourceLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        physicalResourceLogDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return physicalResourceLogDao.deleteBatch(idList);
        }
        return physicalResourceLogDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public PhysicalResourceLogVO selectByPrimaryKey(String id) {
        PhysicalResourceLogPO po = physicalResourceLogDao.selectByPrimaryKey(id);
        return PhysicalResourceLogConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "PHYSICAL_RESOURCE_LOG")
    public List<PhysicalResourceLogVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "PHYSICAL_RESOURCE_LOG")
    public List<PhysicalResourceLogVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<PhysicalResourceLogVO> dataList = physicalResourceLogDao.selectByCondition(sortParam, queryCriteriaParam);
        PhysicalResourceLogServiceImpl target = SpringBeanUtils.getBean(PhysicalResourceLogServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<PhysicalResourceLogVO> selectByParams(Map<String, Object> params) {
        List<PhysicalResourceLogPO> list = physicalResourceLogDao.selectByParams(params);
        return PhysicalResourceLogConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<PhysicalResourceLogVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    /**
     * 同步接口
     *
     * @param beginTime
     * @param endTime
     * @param tenantCode
     * @return
     */
    @Override
    public BaseResponse<Void> syncData(String beginTime, String endTime, String tenantCode) {
        log.info("开始同步主资源数据");
        try {
            HashMap<String, Object> map = MapUtil.newHashMap();
            if (StringUtils.isEmpty(tenantCode)) {
                tenantCode = SystemHolder.getTenantId();
                map.put("triggerType", YesOrNoEnum.YES.getCode());
                map.put("lastUpdateDate", beginTime);
                map.put("endDate", endTime);
            }
            List<NewStockPointVO> newStockPointVOS = newStockPointService.selectAll();
            List<String> organizations = new ArrayList<>();
            for (NewStockPointVO vo : newStockPointVOS) {
                String interfaceFlag = vo.getInterfaceFlag();
                if (org.apache.commons.lang.StringUtils.isNotEmpty(interfaceFlag)) {
                    String[] split = interfaceFlag.split(",");
                    boolean flag = Arrays.stream(split).anyMatch(x -> ApiCategoryEnum.PHYSICAL_RESOURCE.getCode().equals(x));
                    if (flag) {
                        organizations.add(vo.getStockPointCode());
                    }
                }
            }
            map.put("organizations",organizations);
            //同步数据到中间表
            newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.MES.getCode(),
                    ApiCategoryEnum.PHYSICAL_RESOURCE.getCode(), map);

            return BaseResponse.success("同步数据完成");
        } catch (Exception e) {
            log.error("同步资源组数据报错，{}", e.getMessage());
            throw new BusinessException("同步资源组数据报错，{}", e.getMessage());
        }
    }

    /**
     * 补充资源数据
     *
     * @return
     */
    @Override
    public BaseResponse<Void> completePhysicalResourceData() {
        //获取有更新的数据
        List<PhysicalResourceDTO> updateList = new ArrayList<>();
        //先找出物理资源（主资源）中 标注资源ID为空的数据集合
        HashMap<String, Object> map = MapUtil.newHashMap();
        map.put("standardResourceIdNull", YesOrNoEnum.YES.getCode());
        List<PhysicalResourceVO> physicalResourceVOS = physicalResourceService.selectByParams(map);
        if (physicalResourceVOS.isEmpty()) {
            return BaseResponse.success();
        }
        Set<String> physicalResourceCodes = physicalResourceVOS.stream().map(PhysicalResourceVO::getPhysicalResourceCode).collect(Collectors.toSet());
        // 匹配中间日志表中对应的生产线族数据  日志表数据
        HashMap<String, Object> currentFamilyLogMap = MapUtil.newHashMap();
        currentFamilyLogMap.put("prodLineFamilyCodes", physicalResourceCodes);
        List<PhysicalResourceLogVO> vosByLineFamilyCodes = this.selectByParams(currentFamilyLogMap);
        Map<String, PhysicalResourceLogVO> lineFamilyMap = vosByLineFamilyCodes.stream().collect(Collectors.toMap(
                item -> item.getProdLineFamilyCode(),
                item -> item,
                (oldData, newData) -> oldData
        ));
        Set<String> prodLineFamilyCodes = lineFamilyMap.keySet();

        // 匹配中间日志表中对应的非生产线族数据 日志表数据
        HashMap<String, Object> currentNoLogMap = MapUtil.newHashMap();
        currentNoLogMap.put("prodLineCodes", physicalResourceCodes);
        List<PhysicalResourceLogVO> vosByLineCodes = this.selectByParams(currentNoLogMap);
        Map<String, PhysicalResourceLogVO> lineMap = vosByLineCodes.stream().collect(Collectors.toMap(
                item -> item.getProdLineCode(),
                item -> item,
                (oldData, newData) -> oldData
        ));
        Set<String> prodLineCodes = lineMap.keySet();

        //获取prodLineGroupCode集合
        Set<String> groupCodes = vosByLineFamilyCodes.stream().map(PhysicalResourceLogVO::getProdLineGroupCode).collect(Collectors.toSet());
        groupCodes.addAll(vosByLineCodes.stream().map(PhysicalResourceLogVO::getProdLineGroupCode).collect(Collectors.toSet()));
        //获取标准资源中获取prodLineGroupCode集合对应的数据
        HashMap<String, Object> standardMap = MapUtil.newHashMap();
        standardMap.put("standardResourceCodes", groupCodes);
        List<StandardResourcePO> standardResourcePOS = standardResource2Dao.selectByParams(standardMap);
        Map<String, StandardResourcePO> collect = standardResourcePOS.stream().collect(Collectors.toMap(
                item -> item.getStandardResourceCode(),
                item -> item,
                (oldData, newData) -> oldData
        ));
        Set<String> standardResourceCodes = collect.keySet();

        for (PhysicalResourceVO vo : physicalResourceVOS) {
            String prodLineGroupCode = null;
            //获取主资源主表中物理对象编码
            String physicalResourceCode = vo.getPhysicalResourceCode();
            if (prodLineFamilyCodes.contains(physicalResourceCode)) {
                prodLineGroupCode = lineFamilyMap.get(physicalResourceCode).getProdLineGroupCode();
            } else if (prodLineCodes.contains(physicalResourceCode)) {
                prodLineGroupCode = lineMap.get(physicalResourceCode).getProdLineGroupCode();
            }
            //通过主资源组代码找到对应的ID 并塞入到updateList中
            if (StringUtils.isNotEmpty(prodLineGroupCode)) {
                if (standardResourceCodes.contains(prodLineGroupCode)) {
                    vo.setStandardResourceId(collect.get(prodLineGroupCode).getId());
                    PhysicalResourceDTO physicalResourceDTO = new PhysicalResourceDTO();
                    BeanUtils.copyProperties(vo, physicalResourceDTO);
                    updateList.add(physicalResourceDTO);
                }
            }
        }
        if (!updateList.isEmpty()) {
            physicalResourceService.doUpdateBatch(updateList);
        }

        return BaseResponse.success();
    }

    /**
     * 手动postman调用接口
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Override
    public BaseResponse<Void> syncDataByManual(String beginTime, String endTime) {
        log.info("开始同步资源组数据");
        try {
            List<Scenario> data = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MDS.getCode()).getData();
            String tenantCode = data.get(0).getTenantId();
            HashMap<String, Object> map = MapUtil.newHashMap();
            map.put("triggerType", YesOrNoEnum.YES.getCode());
            map.put("lastUpdateDate", beginTime);
            map.put("endDate", endTime);
            //同步数据到中间表
            newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.MES.getCode(),
                    ApiCategoryEnum.PHYSICAL_RESOURCE.getCode(), map);

            return BaseResponse.success("同步数据完成");
        } catch (Exception e) {
            log.error("同步资源组数据报错，{}", e.getMessage());
            throw new BusinessException("同步资源组数据报错，{}", e.getMessage());
        }
    }

    /**
     * 同步数据到主资源中间表
     *
     * @param list
     * @return
     */
    @Override
    public BaseResponse<Void> syncPhysicalResourceLogData(List<PhysicalResourceLogDTO> list) {
        List<PhysicalResourceLogDTO> insertDtoS = new ArrayList<>();
        List<PhysicalResourceLogDTO> updateDtoS = new ArrayList<>();
        //获取同步的数据，并以id作为map的key
        Map<String, PhysicalResourceLogDTO> collect = list.stream().collect(Collectors.toMap(
                item -> item.getId(),
                item -> item,
                (item1, item2) -> item1.getLastUpdateDate().compareTo(item2.getLastUpdateDate()) > 1 ? item1 : item2
        ));
        Set<String> ids = collect.keySet();
        //同步再bpim中存在的相同id数据
        HashMap<String, Object> map = MapUtil.newHashMap();
        map.put("ids", ids);
        List<PhysicalResourceLogPO> physicalResourceLogPOS = physicalResourceLogDao.selectByParams(map);
        Set<String> existingIds = physicalResourceLogPOS.stream().map(PhysicalResourceLogPO::getId).collect(Collectors.toSet());
        //区别新增还是已存在数据
        for (String id : ids) {
            PhysicalResourceLogDTO physicalResourceLogDTO = collect.get(id);
            if (existingIds.contains(id)) {
                updateDtoS.add(physicalResourceLogDTO);
            } else {
                insertDtoS.add(physicalResourceLogDTO);
            }
        }
        if (!insertDtoS.isEmpty()) {
            doCreateNewBatch(insertDtoS);
        }
        if (!updateDtoS.isEmpty()) {
            doUpdateBatch(updateDtoS);
        }
        //将中间表的数据插入到主表
        this.syncPhysicalResourceData(list);
        return BaseResponse.success("数据同步成功");
    }

    /**
     * 代替doCreateBatch
     *
     * @param list
     */
    @Override
    public void doCreateNewBatch(List<PhysicalResourceLogDTO> list) {
        List<PhysicalResourceLogPO> newList = PhysicalResourceLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        physicalResourceLogDao.insertBatchWithPrimaryKey(newList);
    }

    /**
     * 同步数据到主资源主表
     *
     * @param list
     * @return
     */
    @Override
    public BaseResponse<Void> syncPhysicalResourceData(List<PhysicalResourceLogDTO> list) {
        // 插入到主表
        List<PhysicalResourceDTO> insertList = new ArrayList<>();
        //更新主表
        List<PhysicalResourceDTO> updateList = new ArrayList<>();

        //todo 获取同步的生产线族编码集合
        Set<String> prodLineFamilyCodes = list.stream().filter(x -> StringUtils.isNotEmpty(x.getProdLineFamilyCode()))
                .map(PhysicalResourceLogDTO::getProdLineFamilyCode).collect(Collectors.toSet());

        Map<String, PhysicalResourceVO> lineFamilyMap = MapUtil.newHashMap();
        Set<String> lineFamilyCodes = new HashSet<>();
        LinkedHashMap<String, List<PhysicalResourceLogPO>> sameLineFamilyMap = new LinkedHashMap<>();
        if (!prodLineFamilyCodes.isEmpty()) {
            //根据生产线族编码集合查询bpim主资源主表已有的数据
            HashMap<String, Object> physicalResourceMap = MapUtil.newHashMap();
            physicalResourceMap.put("physicalResourceCodes", prodLineFamilyCodes);
            List<PhysicalResourceVO> prodLineFamilyVos = physicalResourceService.selectByParams(physicalResourceMap);
            //以PhysicalResourceCode为key
            lineFamilyMap = prodLineFamilyVos.stream().collect(Collectors.toMap(
                    item -> item.getPhysicalResourceCode(),
                    item -> item
            ));
            //对应的生产线族集合的codes
            lineFamilyCodes = lineFamilyMap.keySet();
            //查找中间表中
            HashMap<String, Object> midLogMap = MapUtil.newHashMap();
            midLogMap.put("prodLineFamilyCodes", prodLineFamilyCodes);
            midLogMap.put("enableFlag", YesOrNoEnum.YES.getCode());
            List<PhysicalResourceLogPO> physicalResourceLogPOS = physicalResourceLogDao.selectByParams(midLogMap);
            //以ProdLineFamilyCode为key，对应的数据集合作为value
            sameLineFamilyMap = physicalResourceLogPOS.stream().
                    collect(Collectors.groupingBy(PhysicalResourceLogPO::getProdLineFamilyCode,
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    sonList -> {
                                        return sonList;
                                    }
                            )
                    )).
                    entrySet().stream().
                    sorted(Map.Entry.comparingByKey()).
                    collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (oldValue, newValue) -> oldValue,
                            LinkedHashMap::new
                    ));
        }

        // todo 获取非生产线族编码集合
        Set<String> prodLineCodes = list.stream().filter(x -> StringUtils.isEmpty(x.getProdLineFamilyCode()))
                .map(PhysicalResourceLogDTO::getProdLineCode).collect(Collectors.toSet());

        Map<String, PhysicalResourceVO> lineMap = new HashMap<>();
        Set<String> lineCodes = new HashSet();
        if (!prodLineCodes.isEmpty()) {
            HashMap<String, Object> physicalResource2Map = MapUtil.newHashMap();
            physicalResource2Map.put("physicalResourceCodes", prodLineCodes);
            List<PhysicalResourceVO> prodLineVos = physicalResourceService.selectByParams(physicalResource2Map);
            //以PhysicalResourceCode为key
            lineMap = prodLineVos.stream().collect(Collectors.toMap(
                    item -> item.getPhysicalResourceCode(),
                    item -> item
            ));
            //对应的生产线族集合的codes
            lineCodes = lineMap.keySet();
        }

        for (PhysicalResourceLogDTO dto : list) {
            String sequenceCode = dto.getSequenceCode();
            //判断是否存在生产线族
            if (StringUtils.isNotEmpty(dto.getProdLineFamilyCode())) {
                String prodLineFamilyCode = dto.getProdLineFamilyCode();
                //判断新增还是修改
                if (lineFamilyCodes.contains(prodLineFamilyCode)) {
                    //查找中间表是否有相同数据
                    List<PhysicalResourceLogPO> physicalResourceLogPOS = sameLineFamilyMap.get(prodLineFamilyCode);
                    PhysicalResourceVO oldPhysicalResource = lineFamilyMap.get(prodLineFamilyCode);
                    //判断中间表中是否还有有效的数据
                    if (YesOrNoEnum.YES.getCode().equals(dto.getEnableFlag())) {
                        oldPhysicalResource.setPhysicalResourceName(dto.getProdLineFamilyDesc());
                        oldPhysicalResource.setResourceQuantityCoefficient(String.valueOf(physicalResourceLogPOS.size()));
                        oldPhysicalResource.setEnabled(YesOrNoEnum.YES.getCode());
                        //后续补充工序
                        if (StringUtils.isNotEmpty(sequenceCode)){
                            oldPhysicalResource.setSequenceCode(sequenceCode);
                        }
                    } else {
                        if (physicalResourceLogPOS.isEmpty()) {
                            oldPhysicalResource.setPhysicalResourceName(dto.getProdLineFamilyDesc());
                            oldPhysicalResource.setResourceQuantityCoefficient(String.valueOf(0));
                            oldPhysicalResource.setEnabled(YesOrNoEnum.NO.getCode());
                            //后续补充工序
                            if (StringUtils.isNotEmpty(sequenceCode)){
                                oldPhysicalResource.setSequenceCode(sequenceCode);
                            }
                        } else {
                            oldPhysicalResource.setPhysicalResourceName(dto.getProdLineFamilyDesc());
                            oldPhysicalResource.setResourceQuantityCoefficient(String.valueOf(physicalResourceLogPOS.size()));
                            oldPhysicalResource.setEnabled(YesOrNoEnum.YES.getCode());
                            //后续补充工序
                            if (StringUtils.isNotEmpty(sequenceCode)){
                                oldPhysicalResource.setSequenceCode(sequenceCode);
                            }
                        }
                    }
                    PhysicalResourceDTO physicalResourceDTO = new PhysicalResourceDTO();
                    BeanUtils.copyProperties(oldPhysicalResource, physicalResourceDTO);
                    updateList.add(physicalResourceDTO);
                } else {
                    if (YesOrNoEnum.YES.getCode().equals(dto.getEnableFlag())) {
                        Set<String> collect = insertList.stream().map(PhysicalResourceDTO::getPhysicalResourceCode).collect(Collectors.toSet());
                        if (!collect.contains(prodLineFamilyCode)){
                            PhysicalResourceDTO physicalResourceDTO = newPhysicalResourceDto(dto, true);
                            //查找中间表是否有相同数据
                            List<PhysicalResourceLogPO> physicalResourceLogPOS = sameLineFamilyMap.get(prodLineFamilyCode);
                            physicalResourceDTO.setResourceQuantityCoefficient(String.valueOf(physicalResourceLogPOS.size()));
                            insertList.add(physicalResourceDTO);
                        }
                    }
                }
            } else {
                String prodLineCode = dto.getProdLineCode();
                //判断新增还是修改
                if (lineCodes.contains(prodLineCode)) {
                    PhysicalResourceVO oldPhysicalResource = lineMap.get(prodLineCode);
                    if (YesOrNoEnum.YES.getCode().equals(dto.getEnableFlag())) {
                        oldPhysicalResource.setPhysicalResourceName(dto.getProdLineDesc());
                        oldPhysicalResource.setResourceQuantityCoefficient(String.valueOf(1));
                        oldPhysicalResource.setEnabled(YesOrNoEnum.YES.getCode());
                        //后续补充工序
                        if (StringUtils.isNotEmpty(sequenceCode)){
                            oldPhysicalResource.setSequenceCode(sequenceCode);
                        }
                    } else {
                        oldPhysicalResource.setPhysicalResourceName(dto.getProdLineDesc());
                        oldPhysicalResource.setResourceQuantityCoefficient(String.valueOf(0));
                        oldPhysicalResource.setEnabled(YesOrNoEnum.NO.getCode());
                        //后续补充工序
                        if (StringUtils.isNotEmpty(sequenceCode)){
                            oldPhysicalResource.setSequenceCode(sequenceCode);
                        }
                    }
                    PhysicalResourceDTO physicalResourceDTO = new PhysicalResourceDTO();
                    BeanUtils.copyProperties(oldPhysicalResource, physicalResourceDTO);
                    updateList.add(physicalResourceDTO);

                } else {
                    if (YesOrNoEnum.YES.getCode().equals(dto.getEnableFlag())) {
                        PhysicalResourceDTO physicalResourceDTO = newPhysicalResourceDto(dto, false);
                        insertList.add(physicalResourceDTO);
                    }
                }
            }
        }
        if (!insertList.isEmpty()) {
            physicalResourceService.doCreateBatch(insertList);
        }
        if (!updateList.isEmpty()) {
            physicalResourceService.doUpdateBatch(updateList);
        }
        return BaseResponse.success("数据同步成功");
    }

    /**
     * 数据的赋值 new Object
     *
     * @param dto
     * @param isFamily
     * @return
     */
    private PhysicalResourceDTO newPhysicalResourceDto(PhysicalResourceLogDTO dto, boolean isFamily) {
        PhysicalResourceDTO physicalResourceDTO = new PhysicalResourceDTO();
        //是否生产线族
        if (isFamily) {
            physicalResourceDTO.setPhysicalResourceCode(dto.getProdLineFamilyCode());
            physicalResourceDTO.setPhysicalResourceName(dto.getProdLineFamilyDesc());
            physicalResourceDTO.setResourceType(NormalEnum.MULTIPLE.getCode());
        } else {
            physicalResourceDTO.setPhysicalResourceCode(dto.getProdLineCode());
            physicalResourceDTO.setPhysicalResourceName(dto.getProdLineDesc());
            physicalResourceDTO.setResourceType(NormalEnum.SINGLE.getCode());
        }
        //工序代码
        String sequenceCode = dto.getSequenceCode();
        //组织
        String plantCode = physicalResourceDTO.getPhysicalResourceCode().substring(0, 2);
        physicalResourceDTO.setSequenceCode(sequenceCode);
        //获取标准工序
        List<StandardStepVO> standardStepVOS = standardStepService.selectAll();
        List<String> formingProcess = standardStepVOS.stream()
                .filter(item -> "FORMING_PROCESS".equals(item.getStandardStepType()))
                .map(item -> item.getStockPointCode() + "-" + item.getStandardStepCode())
                .collect(Collectors.toList());
        //判断是否关键工序的产线而设置子任务类型 关键工序为S1且20 或S2且40为关键工序
        if (StringUtils.isEmpty(sequenceCode)) {
            physicalResourceDTO.setSubtaskType(NormalEnum.WORK.getCode());
        } else {
            if (formingProcess.contains(plantCode + "-" + sequenceCode)) {
                physicalResourceDTO.setSubtaskType(NormalEnum.SET.getCode() + "," + NormalEnum.WORK.getCode());
            } else {
                physicalResourceDTO.setSubtaskType(NormalEnum.WORK.getCode());
            }
        }
        HashMap<String, Object> standardMap = MapUtil.newHashMap();
        standardMap.put("standardResourceCode", dto.getProdLineGroupCode());
        List<StandardResourcePO> standardResourcePOS = standardResource2Dao.selectByParams(standardMap);
        if (!standardResourcePOS.isEmpty()) {
            physicalResourceDTO.setStandardResourceId(standardResourcePOS.get(0).getId());
        }
        physicalResourceDTO.setResourceQuantityCoefficient(String.valueOf(1));
        //设置默认值
        physicalResourceDTO.setResourceCategory(NormalEnum.MAIN.getCode());
        physicalResourceDTO.setAssignQuantityType(NormalEnum.TASK_NUM_LIMIT.getCode());
        physicalResourceDTO.setDisplayIndex(0);
        physicalResourceDTO.setBottleneck(YesOrNoEnum.YES.getCode());
        physicalResourceDTO.setInfiniteCapacity(YesOrNoEnum.NO.getCode());
        physicalResourceDTO.setVariableWorkHours(YesOrNoEnum.NO.getCode());
        physicalResourceDTO.setProductionEfficiency(new BigDecimal("0.8"));
        physicalResourceDTO.setSetupEfficiency(new BigDecimal(1));
        physicalResourceDTO.setCleanupEfficiency(new BigDecimal(1));
        physicalResourceDTO.setStrictProductionLineConstraints(YesOrNoEnum.NO.getCode());
        physicalResourceDTO.setNoBufferActionType(NormalEnum.NO_LIMIT.getCode());
        physicalResourceDTO.setLotSize(1);
        physicalResourceDTO.setProductionDurationLogic(NormalEnum.MAX_TIME.getCode());
        physicalResourceDTO.setSetupAndCleanupDurationLogic(NormalEnum.MAX_TIME.getCode());
        physicalResourceDTO.setDynamicSetupAndCleanupDurationLogic(NormalEnum.MAX_TIME.getCode());
        return physicalResourceDTO;
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<PhysicalResourceLogVO> invocation(List<PhysicalResourceLogVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
