package com.yhl.scp.mds.newproduct.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.newproduct.domain.entity.NewProductStockPointDO;
import com.yhl.scp.mds.newproduct.infrastructure.dao.NewProductStockPointDao;
import com.yhl.scp.mds.newproduct.infrastructure.po.NewProductStockPointPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <code>ProductStockPointDomainService</code>
 * <p>
 * 物品领域业务
 * </p>
 *
 * @version 1.0
 * @since 2024-07-30 16:02:09
 */
@Service
public class NewProductStockPointDomainService {

    @Resource
    private NewProductStockPointDao newProductStockPointDao;

    /**
     * 数据校验
     *
     * @param newProductStockPointDO 领域对象
     */
    public void validation(NewProductStockPointDO newProductStockPointDO) {
        checkNotNull(newProductStockPointDO);
        checkUniqueCode(newProductStockPointDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param newProductStockPointDO 领域对象
     */
    private void checkNotNull(NewProductStockPointDO newProductStockPointDO) {
        if (StringUtils.isBlank(newProductStockPointDO.getProductCode())) {
            throw new BusinessException("物品代码，不能为空");
        }
        if (StringUtils.isBlank(newProductStockPointDO.getProductCode())) {
            throw new BusinessException("物品名称，不能为空");
        }
    }

    /**
     * 唯一性校验
     *
     * @param newProductStockPointDO 领域对象
     */
    private void checkUniqueCode(NewProductStockPointDO newProductStockPointDO) {
        Map<String, Object> params = new HashMap<>(4);
        params.put("productCode", newProductStockPointDO.getProductCode());
        if (StringUtils.isBlank(newProductStockPointDO.getId())) {
            List<NewProductStockPointPO> list = newProductStockPointDao.selectByParams(params);
            if (CollectionUtils.isNotEmpty(list)) {
                throw new BusinessException("新增失败，物品代码已存在：" + newProductStockPointDO.getProductCode());
            }
        } else {
            NewProductStockPointPO old = newProductStockPointDao.selectByPrimaryKey(newProductStockPointDO.getId());
            if (!newProductStockPointDO.getProductCode().equals(old.getProductCode())) {
                List<NewProductStockPointPO> list = newProductStockPointDao.selectByParams(params);
                if (CollectionUtils.isNotEmpty(list)) {
                    throw new BusinessException("修改失败，物品代码已存在：" + newProductStockPointDO.getProductCode());
                }
            }
            if (!newProductStockPointDO.getVersionValue().equals(old.getVersionValue())) {
                throw new BusinessException("修改失败，数据已被修改，请刷新后重试");
            }
        }
    }

    /**
     * 删除数据校验
     *
     * @param removeVersionDTOS
     */
    public void checkDelete(List<RemoveVersionDTO> removeVersionDTOS) {
        if (CollectionUtils.isEmpty(removeVersionDTOS)) {
            return;
        }
        for (RemoveVersionDTO removeVersionDTO : removeVersionDTOS) {
            NewProductStockPointPO old = newProductStockPointDao.selectByPrimaryKey(removeVersionDTO.getId());
            if (Objects.isNull(old)) {
                throw new BusinessException("删除失败，数据不存在：" + removeVersionDTO.getId());
            }
            if (!removeVersionDTO.getVersionValue().equals(old.getVersionValue())) {
                throw new BusinessException("删除失败，数据已被修改，请刷新后重试");
            }
        }
    }

}
