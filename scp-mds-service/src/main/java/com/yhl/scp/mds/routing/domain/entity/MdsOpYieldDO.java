package com.yhl.scp.mds.routing.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MdsOpYieldDO</code>
 * <p>
 * mes工序成品率接口同步中间表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-14 11:44:32
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MdsOpYieldDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 363071961527585143L;

    /**
     * 主键id
     */
    private String id;
    /**
     * 物品id
     */
    private String productId;
    /**
     * 物品编码
     */
    private String productCode;
    /**
     * 物品规格
     */
    private String productSpec;
    /**
     * 公司代码
     */
    private String planArea;
    /**
     * 库存点ID
     */
    private String stockPointId;
    /**
     * 库存点代码
     */
    private String stockPointCode;
    /**
     * 年月
     */
    private String yearMm;
    /**
     * 成品率
     */
    private String opYield;
    /**
     * 最后更新时间
     */
    private Date lastUpdateDate;
    /**
     * 工序
     */
    private String opProcess;
    private String kid;
    /**
     * 版本
     */
    private Integer versionValue;

}
