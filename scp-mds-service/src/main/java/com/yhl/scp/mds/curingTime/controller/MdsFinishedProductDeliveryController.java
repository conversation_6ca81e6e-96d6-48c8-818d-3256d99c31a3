package com.yhl.scp.mds.curingTime.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mds.curingTime.dto.MdsFinishedProductDeliveryDTO;
import com.yhl.scp.mds.curingTime.service.MdsFinishedProductDeliveryService;
import com.yhl.scp.mds.curingTime.vo.MdsFinishedProductDeliveryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MdsFinishedProductDeliveryController</code>
 * <p>
 * mes系统成品发送属性接口同步中间表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-27 10:46:23
 */
@Slf4j
@Api(tags = "mes系统成品发送属性接口同步中间表控制器")
@RestController
@RequestMapping("mdsFinishedProductDelivery")
public class MdsFinishedProductDeliveryController extends BaseController {

    @Resource
    private MdsFinishedProductDeliveryService mdsFinishedProductDeliveryService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<MdsFinishedProductDeliveryVO>> page() {
        List<MdsFinishedProductDeliveryVO> mdsFinishedProductDeliveryList = mdsFinishedProductDeliveryService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MdsFinishedProductDeliveryVO> pageInfo = new PageInfo<>(mdsFinishedProductDeliveryList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MdsFinishedProductDeliveryDTO mdsFinishedProductDeliveryDTO) {
        return mdsFinishedProductDeliveryService.doCreate(mdsFinishedProductDeliveryDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MdsFinishedProductDeliveryDTO mdsFinishedProductDeliveryDTO) {
        return mdsFinishedProductDeliveryService.doUpdate(mdsFinishedProductDeliveryDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        mdsFinishedProductDeliveryService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<MdsFinishedProductDeliveryVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, mdsFinishedProductDeliveryService.selectByPrimaryKey(id));
    }

}
