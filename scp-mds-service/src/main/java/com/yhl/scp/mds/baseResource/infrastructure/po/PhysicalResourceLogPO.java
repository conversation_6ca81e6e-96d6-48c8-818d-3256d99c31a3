package com.yhl.scp.mds.baseResource.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

public class PhysicalResourceLogPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -64765950665960970L;

    /**
     * 资源代码
     */
    private String prodLineCode;
    /**
     * 资源名称
     */
    private String prodLineDesc;
    /**
     * 主资源组代码
     */
    private String prodLineGroupCode;
    /**
     * 主资源组名称
     */
    private String prodLineGroupDesc;
    /**
     * 工序代码
     */
    private String sequenceCode;
    /**
     * 工序名称
     */
    private String sequenceDesc;
    /**
     * 生产线族代码
     */
    private String prodLineFamilyCode;
    /**
     * 生产线族名称
     */
    private String prodLineFamilyDesc;
    /**
     * 是否有效
     */
    private String enableFlag;
    /**
     * 生产组织代码
     */
    private String plantCode;
    /**
     * 生产组织名称
     */
    private String plantDesc;
    /**
     * 最后更新时间
     */
    private Date lastUpdateDate;

    public String getProdLineCode() {
        return prodLineCode;
    }

    public void setProdLineCode(String prodLineCode) {
        this.prodLineCode = prodLineCode;
    }

    public String getProdLineDesc() {
        return prodLineDesc;
    }

    public void setProdLineDesc(String prodLineDesc) {
        this.prodLineDesc = prodLineDesc;
    }

    public String getProdLineGroupCode() {
        return prodLineGroupCode;
    }

    public void setProdLineGroupCode(String prodLineGroupCode) {
        this.prodLineGroupCode = prodLineGroupCode;
    }

    public String getProdLineGroupDesc() {
        return prodLineGroupDesc;
    }

    public void setProdLineGroupDesc(String prodLineGroupDesc) {
        this.prodLineGroupDesc = prodLineGroupDesc;
    }

    public String getSequenceCode() {
        return sequenceCode;
    }

    public void setSequenceCode(String sequenceCode) {
        this.sequenceCode = sequenceCode;
    }

    public String getSequenceDesc() {
        return sequenceDesc;
    }

    public void setSequenceDesc(String sequenceDesc) {
        this.sequenceDesc = sequenceDesc;
    }

    public String getProdLineFamilyCode() {
        return prodLineFamilyCode;
    }

    public void setProdLineFamilyCode(String prodLineFamilyCode) {
        this.prodLineFamilyCode = prodLineFamilyCode;
    }

    public String getProdLineFamilyDesc() {
        return prodLineFamilyDesc;
    }

    public void setProdLineFamilyDesc(String prodLineFamilyDesc) {
        this.prodLineFamilyDesc = prodLineFamilyDesc;
    }

    public String getEnableFlag() {
        return enableFlag;
    }

    public void setEnableFlag(String enableFlag) {
        this.enableFlag = enableFlag;
    }

    public String getPlantCode() {
        return plantCode;
    }

    public void setPlantCode(String plantCode) {
        this.plantCode = plantCode;
    }

    public String getPlantDesc() {
        return plantDesc;
    }

    public void setPlantDesc(String plantDesc) {
        this.plantDesc = plantDesc;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

}
