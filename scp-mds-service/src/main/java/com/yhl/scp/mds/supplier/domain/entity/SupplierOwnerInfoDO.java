package com.yhl.scp.mds.supplier.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>SupplierOwnerInfoDO</code>
 * <p>
 * DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-02 16:20:47
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierOwnerInfoDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -70551108091834005L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 物品ID
     */
    private String productId;
    /**
     * 物品代码
     */
    private String productCode;
    /**
     * 是否寄售(N:否/Y:是)
     */
    private String ownerType;
    /**
     * 最后更新时间
     */
    private Date lastUpdateDate;
    /**
     * 合格供应商 ID
     */
    private String supplierId;
    /**
     * 供应商代码
     */
    private String supplierCode;
    /**
     * 计划区域
     */
    private String plantId;
    /**
     * 库存点说明
     */
    private String locatorDesc;
    /**
     * 库存点编码
     */
    private String stockPointCode;
    /**
     * 版本
     */
    private Integer versionValue;

}
