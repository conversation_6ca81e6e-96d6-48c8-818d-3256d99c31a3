StockTypeEnum.ON_HAND=ç°è´§
StockTypeEnum.TRANSPORTING=å¨é
StockTypeEnum.ON_ORDER=å¨è®¢
GenderEnum.MALE=ç·
GenderEnum.FEMALE=å¥³
PeriodLengthUnitEnum.MONTH=æ
PeriodLengthUnitEnum.WEEK=å¨
PeriodLengthUnitEnum.DAY=æ¥
UserTypeEnum.SYSTEM_ADMIN=è¶çº§ç®¡çå
UserTypeEnum.TENANT_ADMIN=ç§æ·ç®¡çå
UserTypeEnum.NORMAL=æ®éç¨æ·
CalculationTypeEnum.HOMEBREW=åºå®å®å¨åºå­
CalculationTypeEnum.OUTSOURCING=å¨æå®å¨åºå­
CapacityTypeEnum.INDEPENDENT=ç©çèµæºç¬ç«çäº§
CapacityTypeEnum.SHARE=ç©çèµæºå±åçäº§
DataTypeEnum.INTEGER=æ´æ°
DataTypeEnum.CARD=å¡ç
DataTypeEnum.DOUBLE=å°æ°
SupplyTypeEnum.STOCK_SUPPLY=åºå­ä¾åº
SupplyTypeEnum.PURCHASE_ORDER_SUPPLY=éè´­è®¢åä¾åº
SupplyTypeEnum.TRANSPORTATION_ORDER_SUPPLY=è¿è¾è®¢åä¾åº
SupplyTypeEnum.WORK_ORDER_SUPPLY=å¶é è®¢åä¾åº
RfuFunctionTypeEnum.POPUP=å¼¹åºæ¡
RfuFunctionTypeEnum.MENU=èå
RfuFunctionTypeEnum.INTERFACE=æ¥å£
MaterialTypeEnum.KEY_MATERIAL=å³é®æ
MaterialTypeEnum.SHORTAGE_MATERIAL=ç¼ºå£æ
WeekEnum.MON=ææä¸
WeekEnum.TUE=ææäº
WeekEnum.WEN=ææä¸
WeekEnum.THU=ææå
WeekEnum.FRI=ææäº
WeekEnum.SAT=ææå­
WeekEnum.SUN=æææ¥
RuleDateFormatEnum.Y=Y
RuleDateFormatEnum.YY=YY
RuleDateFormatEnum.YYYY=YYYY
RuleDateFormatEnum.YYMM=YYMM
RuleDateFormatEnum.YYMMDD=YYMMDD
RuleDateFormatEnum.M=M
RuleDateFormatEnum.MM=MM
RuleDateFormatEnum.D=D
RuleDateFormatEnum.DD=DD
ResourceTypeEnum.BOTTLENECK_RESOURCE=ç¶é¢èµæº
ResourceTypeEnum.SHORTAGE_MATERIAL=ç¼ºå£èµæº
FinancialKpiEnum.MARGINAL_INCOME=è¾¹éæ¶ç
FinancialKpiEnum.INCOME=æ¶å¥
FinancialKpiEnum.MANUFACTURING_COSTS=å¶é ææ¬
FinancialKpiEnum.TRANSPORTATION_COST=è¿è¾ææ¬
FinancialKpiEnum.HOLDING_COST=æè´§ææ¬
FinancialKpiEnum.OUT_OF_STOCK_COST=ç¼ºè´§ææ¬
FinancialKpiEnum.REPLACEMENT_COST=æ¢åææ¬
PlannedStatusEnum.UNPLAN=æªè®¡å
PlannedStatusEnum.PLANNED=å·²è®¡å
PlannedStatusEnum.ISSUED=å·²ä¸å
PlannedStatusEnum.SCHEDULED=å·²æç¨
PlannedStatusEnum.FINISHED=å·²å®å·¥
ExpressionColumnTypeEnum.TEXT=ææ¬
ExpressionColumnTypeEnum.ICON=å¾æ 
ExpressionColumnTypeEnum.CHART=å¾è¡¨
CountryTypeEnum.D=å½å
CountryTypeEnum.I=å½é
TimeUnitEnum.DECADE=åå¹´
TimeUnitEnum.YEAR=å¹´
TimeUnitEnum.SEASON=å­£
TimeUnitEnum.MONTH=æ
TimeUnitEnum.TEN_DAY=æ¬
TimeUnitEnum.WEEK=å¨
TimeUnitEnum.DAY=æ¥
ResourceCategoryEnum.MAIN=ä¸»èµæº
ResourceCategoryEnum.TOOL=å·¥å·èµæº
ChartTypeEnum.BAR=æ±ç¶å¾
ChartTypeEnum.LINE=æçº¿å¾
ChartTypeEnum.PIE=é¥¼å¾
ChartTypeEnum.HEAT_MAP=ç­åå¾
ChartTypeEnum.RADAR=é·è¾¾å¾
ChartTypeEnum.RECTANGLE_TREE=ç©å½¢æ å¾
ActionTypeEnum.QUERY=æ¥è¯¢
ActionTypeEnum.CLICK=ç¹å»
ActionTypeEnum.HOVER=æ¬æµ®
LanguageEnum.ZH_CN=ä¸­æ
LanguageEnum.EN_US=English
SourcingTypeEnum.HOMEBREW=èªå¶
SourcingTypeEnum.OUTSOURCING=å§å¤
SourcingTypeEnum.PURCHASE=éè´­
DemandTypeEnum.FORECAST_DEMAND=é¢æµéæ±
DemandTypeEnum.CUSTOMER_ORDER_DEMAND=å®¢æ·è®¢åéæ±
DemandTypeEnum.TRANSPORTATION_ORDER_DEMAND=è¿è¾è®¢åéæ±
DemandTypeEnum.WORK_ORDER_DEMAND=å¶é è®¢åéæ±
DemandTypeEnum.SAFETY_STOCK_DEMAND=å®å¨åºå­éæ±
DemandTypeEnum.REAL_DEMAND=å®ééæ±
ValueTypeEnum.QUANTITY=æ°é
ValueTypeEnum.AMOUNT=éé¢
PlanUnitBusinessStatusEnum.INITIALZATION=ä¸åºå®
PlanUnitBusinessStatusEnum.CONFIRMED=äººå·¥æ°éåºå®
PlanUnitBusinessStatusEnum.FEEDBACK=è®¡ååé¦åºå®
DemandForecastVersionStatusEnum.DEMAND_COLLECTING=éæ±æ¶éä¸­
DemandForecastVersionStatusEnum.DEMAND_COLLECTED=éæ±æ¶éå®æ
DemandForecastVersionStatusEnum.TO_BE_PLANED=å¾ç¼æ
DemandForecastVersionStatusEnum.PLANED=å·²ç¼æ
DemandForecastVersionStatusEnum.APPLIED=å·²åºç¨
ScenarioTypeEnum.PRIMARY=ä¸»åºæ¯
ScenarioTypeEnum.SECONDARY=å­åºæ¯
VisualizationTypeEnum.PRODUCTION_ORGANIZATION=çäº§ç»ç»
VisualizationTypeEnum.OUTSOURCING_FACTORY=å§å¤å
VisualizationTypeEnum.STOCK_POINT=åºå­ç¹
VisualizationTypeEnum.TRANSPORT_SECTION=è¿è¾è·¯æ®µ
ShiftTypeEnum.REGULAR=æ­£å¸¸ç­æ¬¡
ShiftTypeEnum.MAINTENANCE=æ£ä¿®ç­æ¬¡
RuleEncodingsEnum.REAL_DEMAND=å®ééæ±
RuleEncodingsEnum.PURCHASE_ORDER_NO=éè´­è®¢åå·
RuleEncodingsEnum.PURCHASE_ORDER_SUPPLY=éè´­è®¢åä¾åº
RuleEncodingsEnum.TRANSPORTATION_ORDER_NO=è¿è¾è®¢åå·
RuleEncodingsEnum.TRANSPORTATION_ORDER_SUPPLY=è¿è¾è®¢åä¾åº
RuleEncodingsEnum.TRANSPORTATION_ORDER_DEMAND=è¿è¾è®¢åéæ±
RuleEncodingsEnum.SAFETY_STOCK_DEMAND=å®å¨åºå­éæ±
RuleEncodingsEnum.HEAT_NO=çæ¬¡å·
RuleEncodingsEnum.OPERATION_CODE=å·¥åºä»£ç 
RuleEncodingsEnum.PLAN_NO=è®¡åå·
RuleEncodingsEnum.WORK_ORDER_NO=å¶é è®¢åå·
RuleEncodingsEnum.WORK_ORDER_DEMAND=å¶é è®¢åéæ±
RuleEncodingsEnum.WORK_ORDER_SUPPLY=å¶é è®¢åä¾åº
RuleEncodingsEnum.STOCK_SUPPLY=åºå­ä¾åº
RuleEncodingsEnum.PLANNED_BATCH_NUMBER=è®¡åæ¹æ¬¡å·
RuleEncodingsEnum.CUSTOMER_ORDER_DEMAND=å®¢æ·è®¢åéæ±
RuleEncodingsEnum.SUB_OPERATION_CODE=å­å·¥åºä»£ç 
OrderTypeEnum.WORK_ORDER=å¶é è®¢å
OrderTypeEnum.TRANSPORT_ORDER=è¿è¾è®¢å
OrderTypeEnum.PURCHASE_ORDER=éè´­è®¢å
ProductTypeEnum.MATERIAL=åææ
ProductTypeEnum.SEMI_FINISHED=åæå
ProductTypeEnum.FINISHED=æå
YesOrNoEnum.YES=æ¯
YesOrNoEnum.NO=å¦
ConnectionTypeEnum.ES=é¡ºåºçäº§
ConnectionTypeEnum.SSEE=å¹¶è¡çäº§
ExtendDirectionEnum.HISTORICAL=åå²æ¹å
ExtendDirectionEnum.FUTURE=æªæ¥æ¹å
OperationTypeEnum.CREATE=æ°å¢
OperationTypeEnum.UPDATE=ä¿®æ¹
OperationTypeEnum.DELETE=å é¤
FulfillmentStatusEnum.UNFULFILL=æªåé
FulfillmentStatusEnum.SOME_FULFILLED=é¨ååé
FulfillmentStatusEnum.ALL_FULFILLED=å®å¨åé
CalendarSourceEnum.AUTO_CREATED_BY_RULE=ç±æ¥åè§åçæ
CalendarSourceEnum.MANUAL_ADD=æå¨æ·»å 
CalendarSourceEnum.MANUAL_DEL=æå¨å é¤
ProcessStatusEnum.IN_PROGRESS=æµè½¬ä¸­
ProcessStatusEnum.NORMAL_FINISH=æ­£å¸¸ç»æ
ProcessStatusEnum.ABNORMAL_FINISH=å¼å¸¸ç»æ
ProcessStatusEnum.MANUAL_ABORT=äººå·¥ç»æ­¢
ResourceTypeEnum.SINGLE=åä¸è½åèµæº
ResourceTypeEnum.FURNACE=çèµæº
AlgorithmLogStatusEnum.WAITING=ç­å¾è¿è¡
AlgorithmLogStatusEnum.RUNNING=æ­£å¨è¿è¡
AlgorithmLogStatusEnum.PULLING=æ­£å¨æåç»æ
AlgorithmLogStatusEnum.SUCCESS=æå
AlgorithmLogStatusEnum.FAIL=å¤±è´¥
AltTypeEnum.POSITIVE_ORDER=é¡ºå
AltTypeEnum.REVERSE_ORDER=éå
PriorityTypeEnum.FORECAST_DEMAND=éæ±é¢æµ
PriorityTypeEnum.CUSTOMER_ORDER=å®¢æ·è®¢å
PriorityTypeEnum.SAFETY_STOCK=å®å¨åºå­
OutsourcingTypeEnum.BY_PIECES=è®¡ä»¶å¶
OutsourcingTypeEnum.BY_HOURS=å·¥æ¶å¶
CalTypeEnum.SUM=æ±åé¡¹
CalTypeEnum.COUNT=è®¡æ°é¡¹
CalTypeEnum.AVERAGE=å¹³åå¼
CalTypeEnum.MAXIMUM=æå¤§å¼
CalTypeEnum.MINIMUM=æå°å¼
ScenarioConfigEnum.REQUIREMENT_RANGE=éæ±èå´
ScenarioConfigEnum.SUPPLY_CHAIN_MODEL_SETTING=ä¾åºé¾æ¨¡åå¿«æ·è®¾ç½®
ScenarioConfigEnum.ALGORITHM_CONFIG=ç®æ³åæ°
ScenarioConfigEnum.TIME_PERIOD_RANGE=æ¶æ®µèå´
ScenarioConfigEnum.PRODUCT_RANGE=äº§åèå´
ScenarioConfigEnum.MARKETING_ORGANIZATION=æå±éå®ç»ç»
ScenarioConfigEnum.PRODUCTION_CAPACITY=çäº§è½å
ScenarioConfigEnum.STOCK_POINT_CAPACITY=åºå­ç¹è½å
ScenarioConfigEnum.TRANSPORT_CAPACITY=è¿è¾è½å
ScenarioConfigEnum.TIME_LIMIT=æ¶é´éå¶
ScenarioConfigEnum.PRODUCTION_SUPPLY_AND_DEMAND_COUPLE=äº§ä¾éæ±æé©
ScenarioConfigEnum.DISABLE_CONSTRAINTS=ç¦ç¨çº¦æ
ScenarioConfigEnum.ONLY_BOTTLENECK_MATERIAL=åªèèç¶é¢ç©æ
ScenarioConfigEnum.ONLY_BOTTLENECK_RESOURCE=åªèèç¶é¢èµæº
SystemModuleEnum.MDS=ä¸»æ°æ®
SystemModuleEnum.SOP=äº§éåå
SystemModuleEnum.OCP=ä¸»è®¡å
SystemModuleEnum.MRP=ç©æè®¡å
SystemModuleEnum.CTS=æ§å¶å¡
SystemModuleEnum.ODS=è®¢åäº¤ä»
SystemModuleEnum.AMS=çäº§æç¨
SystemModuleEnum.DPS=éæ±ç®¡ç
SystemModuleEnum.IPS=ç³»ç»ç®¡ç
SystemModuleEnum.SDS=ä¾åºç®¡ç
ImportTypeEnum.FULL_IMPORT=æä»¶å¨éå¯¼å¥
ImportTypeEnum.INCREMENTAL_IMPORT=æä»¶å¢éå¯¼å¥
ConnectionTaskEnum.PRE_SETTING=åè®¾ç½®
ConnectionTaskEnum.PRODUCTION=å¶é 
ConnectionTaskEnum.LOCK=éå®
ConnectionTaskEnum.POST_SETTING=åè®¾ç½®
TransportTypeEnum.AIR=ç©ºè¿
TransportTypeEnum.SHIPPING=æµ·è¿
TransportTypeEnum.RAILWAY=éè·¯
TransportTypeEnum.HIGHWAY=å¬è·¯
RuleInitializationConditionsEnum.NOT_INIT=ä¸åå§å
RuleInitializationConditionsEnum.CROSS_DAY=è·¨å¤©
RuleInitializationConditionsEnum.CROSS_YEAR=è·¨å¹´
RuleInitializationConditionsEnum.CROSS_MONTH=è·¨æ
RepeatFrequencyEnum.WEEKLY=æå¨
RepeatFrequencyEnum.MONTHLY=ææ
ImportDataTypeEnum.GATHER=æ±æ»
ImportDataTypeEnum.ERROR=å¤±è´¥
ImportDataTypeEnum.WARNING=è­¦å
ImportDataTypeEnum.INFO=æ­£å¸¸
DisableConstraintsEnum.FORBIDDEN_TRANSPORTATION_LIMIT=è¿è½ä¸é
DisableConstraintsEnum.FORBIDDEN_PRODUCTION_LIMIT=äº§è½ä¸é
DisableConstraintsEnum.FORBIDDEN_INVENTORY_LIMIT=åºå®¹ä¸é
DisableConstraintsEnum.FORBIDDEN_PURCHASE_LIMIT=ç©æä¾åºä¸é
KpiEnum.CUSTOMER_ORDER_COUNT=éå®è®¢åä¸ªæ°
KpiEnum.CUSTOMER_ORDER_COMPLETE_RATE=éå®è®¢åè®¡åäº¤ä»ç
KpiEnum.DELAY_CUSTOMER_ORDER_COUNT=å»¶æéå®è®¢åä¸ªæ°
KpiEnum.WORK_ORDER_COUNT=å¶é è®¢åä¸ªæ°
KpiEnum.WAITING_PLAN_WORK_ORDER_COUNT=å¾æå¶é è®¢åä¸ªæ°
KpiEnum.WORK_ORDER_COMPLETE_RATE=å¶é è®¢åè®¡åäº¤ä»ç
KpiEnum.DELAY_WORK_ORDER_COUNT=å»¶æå¶é è®¢åä¸ªæ°
KpiEnum.AVERAGE_CAPACITY_UTILIZATION=å¹³åäº§è½å©ç¨ç
KpiEnum.UTILIZATION_RATE=ç¨¼å¨ç
KpiEnum.TOTAL_CHANGEOVER_TIME=æ»æ¢åæ¶é´
KpiEnum.TOTAL_NUMBER_OF_CHANGES=æ»æ¢åæ¬¡æ°
KpiEnum.TOTAL_STANDBY_TIME=æ»å¾æºæ¶é´
KpiEnum.FINISHED_PRODUCT_DAY_AVERAGE_TOTAL_INVENTORY=æåæ¥å¹³ååºå­æ»é
KpiEnum.SEMI_FINISHED_PRODUCT_DAY_AVERAGE_TOTAL_INVENTORY=åæåæ¥å¹³ååºå­æ»é
KpiEnum.MATERIAL_PRODUCT_DAY_AVERAGE_TOTAL_INVENTORY=åæææ¥å¹³ååºå­æ»é
KpiEnum.FINISHED_NUM_COUNT=äº§åºæ°éç»è®¡
KpiEnum.PROCESS_TIME_COUNT=çäº§æ¶é´ç»è®¡
KpiEnum.FREE_TIME_COUNT=é²ç½®æ¶é´ç»è®¡
KpiEnum.PURCHASE_ORDER_DELAY=éè´­è®¢åæªæç§äº¤æå°è´§
KpiEnum.DEMAND_NOT_FULFILLMENT_COUNT=ç©æéæ±æªè¢«æ»¡è¶³
KpiEnum.ROUTING_INVALID=æ æå·¥èºè·¯å¾æ°é
KpiEnum.NOT_PLAN_OPERATION_COUNT=æªæç¨å·¥åºæ°é
KpiEnum.WORK_ORDER_FAIL_SYNC=å¶é è®¢ååæ­¥å¤±è´¥
KpiEnum.TIME_INTERVAL_OPERATION_COUNT=å·¥åºä¸åå·¥åºè¿åæ¶é´é´éçº¦æ
DemandForecastVersionTypeEnum.FORECAST=é¢æµçæ¬
DemandForecastVersionTypeEnum.SUBMISSION=ææ¥çæ¬
DemandForecastVersionTypeEnum.COMPARISON=å¯¹æ¯çæ¬
RuleTypeEnum.GENERATED=çæå
RuleTypeEnum.REFERENCED=å¼ç¨å
DataTypeEnum.INTEGER=æ´æ°
DataTypeEnum.DECIMAL=å°æ°
DataTypeEnum.DATE=æ¥æ
DataTypeEnum.TIME=æ¶é´
DataTypeEnum.DATE_TIME=æ¶é´æ³
DataTypeEnum.TIME_PERIOD=æ¶é´æ®µ
DataTypeEnum.CHAR=å­ç¬¦
DataTypeEnum.VARCHAR=å­ç¬¦ä¸²
DataTypeEnum.COORDINATE=åæ 
DataTypeEnum.BOOLEAN=å¸å°
ObjectTypeEnum.DEMAND_PRIORITY=éæ±ä¼åçº§
ObjectTypeEnum.CUSTOMER=å®¢æ·
ObjectTypeEnum.CUSTOMER_ORDER=å®¢æ·è®¢å
ObjectTypeEnum.SAFETY_STOCK=å®å¨åºå­
ObjectTypeEnum.SAFETY_STOCK_CONFIG=å®å¨åºå­éç½®
ObjectTypeEnum.DEMAND_FORECAST_VERSION=éæ±é¢æµçæ¬
ObjectTypeEnum.DEMAND_FORECAST=éæ±é¢æµ
ObjectTypeEnum.OPTIMIZE_TARGET=ä¼åç®æ 
SymbolEnum.EQUAL=ç­äº
SymbolEnum.GREATER_THAN=å¤§äº
SymbolEnum.LESS_THAN=å°äº
SymbolEnum.GREATER_OR_EQUAL=å¤§äºç­äº
SymbolEnum.LESS_OR_EQUAL=å°äºç­äº
SymbolEnum.NOT_EQUAL=ä¸ç­äº
SymbolEnum.CONTAIN=åå«
SymbolEnum.NOT_CONTAIN=ä¸åå«
SymbolEnum.BETWEEN=ä»äº
SymbolEnum.IN=å¤é
ObjectTypeEnum.CURRENCY=è´§å¸åä½
ObjectTypeEnum.CURRENCY_CONVERSION=æ±ç
ObjectTypeEnum.MEASUREMENT_UNIT=è®¡éåä½
ObjectTypeEnum.MEASUREMENT_UNIT_CONVERSION=è®¡éåä½æ¢ç®
ObjectTypeEnum.PRODUCTION_ORGANIZATION=çäº§ç»ç»
ObjectTypeEnum.PRODUCT=ç©å
ObjectTypeEnum.STOCK_POINT=åºå­ç¹
ObjectTypeEnum.PRODUCT_STOCK_POINT=åºå­ç¹ç©å
ObjectTypeEnum.STANDARD_RESOURCE=æ åèµæº
ObjectTypeEnum.PHYSICAL_RESOURCE=ç©çèµæº
ObjectTypeEnum.ROUTING=å·¥èºè·¯å¾
ObjectTypeEnum.ROUTING_STEP=å·¥èº
ObjectTypeEnum.ROUTING_STEP_RESOURCE=å·¥èºèµæº
ObjectTypeEnum.BOM=ç©ææ¸å
ObjectTypeEnum.ROUTING_STEP_INPUT=å·¥èºè¾å¥ç©å
ObjectTypeEnum.ROUTING_STEP_OUTPUT=å·¥èºè¾åºç©å
ObjectTypeEnum.SALES_LEVEL=éå®å±çº§
ObjectTypeEnum.SALES_SEGMENT=éå®é¨é¨
ObjectTypeEnum.SUPPLIER=ä¾åºå
ObjectTypeEnum.SUPPLIER_ROUTING=ä¾åºåå¯ä¾ç©æ
ObjectTypeEnum.SUPPLIER_RATIO=ä¾åºåéè´­æ¯ä¾
ObjectTypeEnum.SUPPLIER_CAPACITY=ä¾åºåä¾åºè½å
ObjectTypeEnum.OUTSOURCING_FACTORY=å§å¤å
ObjectTypeEnum.OUTSOURCING_FACTORY_PRODUCT=å§å¤åå¯äº§ç©å
ObjectTypeEnum.TIME_PERIOD_GROUP=æ¶æ®µåºå
ObjectTypeEnum.TIME_PERIOD_RULE=æ¶æ®µè§å
ObjectTypeEnum.TIME_PERIOD=æ¶æ®µ
ObjectTypeEnum.PLANNING_HORIZON=è®¡åå±ææ
ObjectTypeEnum.WORK_ORDER=å¶é è®¢å
ObjectTypeEnum.TRANSPORT_ORDER=è¿è¾è®¢å
ObjectTypeEnum.PURCHASE_ORDER=éè´­è®¢å
ObjectTypeEnum.PLAN_UNIT=è®¡åæ¹æ¬¡
ObjectTypeEnum.OPERATION=å·¥åº
ObjectTypeEnum.OPERATION_INPUT=å·¥åºè¾å¥
ObjectTypeEnum.OPERATION_OUTPUT=å·¥åºè¾åº
ObjectTypeEnum.OPERATION_RESOURCE=å·¥åºèµæº
ObjectTypeEnum.OPERATION_TASK=å·¥åºä»»å¡
ObjectTypeEnum.DEMAND=éæ±
ObjectTypeEnum.SUPPLY=ä¾åº
ObjectTypeEnum.FULFILLMENT=åéå³ç³»
ObjectTypeEnum.RULE_ENCODINGS=ç¼ç è§å
ObjectTypeEnum.SCENARIO_CONFIG=åºæ¯åæ°
ObjectTypeEnum.TRANSPORT_ROUTING=è¿è¾è·¯å¾
ObjectTypeEnum.TRANSPORT_SECTION=è¿è¾è·¯æ®µ
ObjectTypeEnum.TRANSPORT_PRODUCT=è¿è¾ç©å
ObjectTypeEnum.ALGORITHM_SERVER=è¿è¾ç©å
ObjectTypeEnum.CURRENCY=è´§å¸åä½
ObjectTypeEnum.CURRENCY_CONVERSION=æ±ç
ObjectTypeEnum.MEASUREMENT_UNIT=è®¡éåä½
ObjectTypeEnum.MEASUREMENT_UNIT_CONVERSION=è®¡éåä½æ¢ç®
