package com.yhl.scp.mrp.supplier.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseDTO;
import com.yhl.scp.common.excel.ExcelPropertyCheck;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialSupplierPurchaseBasicDTO</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-22 15:33:32
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialSupplierPurchaseBasicDTO extends BaseDTO implements Serializable {


    private static final long serialVersionUID = 440920083625092279L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    @ExcelProperty(value = "库存点代码*")
    @ExcelPropertyCheck(required = true)
    private String stockPointCode;
    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    private String stockPointName;
    /**
     * 材料代码
     */
    @ApiModelProperty(value = "材料代码")
    @ExcelProperty(value = "材料代码*")
    @ExcelPropertyCheck(required = true)
    private String materialCode;
    /**
     * 材料名称
     */
    @ApiModelProperty(value = "材料名称")
    private String materialName;
    /**
     * 材料类型
     */
    @ApiModelProperty(value = "材料类型")
    private String materialType;
    /**
     * 材料属性
     */
    @ApiModelProperty(value = "材料属性")
    private String materialProperty;

    /**
     * 计划员
     */
    @ApiModelProperty(value = "计划员")
    private String planner;
    /**
     * 最小起订量
     */
    @ApiModelProperty(value = "最小起订量")
    @ExcelProperty(value = "最小起订量")
    private BigDecimal minOrderQty;

    /**
     * 包装批量
     */
    @ApiModelProperty(value = "包装批量")
    @ExcelProperty(value = "包装批量")
    private BigDecimal packageLot;
    /**
     * 是否寄售
     */
    @ApiModelProperty(value = "是否寄售")
    private String consignment;
    /**
     * 是否专用
     */
    @ApiModelProperty(value = "是否专用")
    private String specific;


    /**
     * 要货计划锁定期
     */
    @ApiModelProperty(value = "要货计划锁定期")
    @ExcelProperty(value = "要货计划锁定期")
    private BigDecimal requestCargoPlanLockDay;


    /**
     * 订单下达提前期
     */
    @ApiModelProperty(value = "订单下达提前期")
    @ExcelProperty(value = "订单下达提前期")
    private BigDecimal orderPlacementLeadTimeDay;

    /**
     * 采购批量
     */
    @ApiModelProperty(value = "采购批量")
    @ExcelProperty(value = "采购批量")
    private BigDecimal purchaseLot;

    /**
     * 要货模式
     */
    @ApiModelProperty(value = "要货模式")
    @ExcelProperty(value = "要货模式")
    private String demandPattern;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 组织id
     */
    @ApiModelProperty(value = "组织id")
    private String organizationId;
    /**
     * srmId
     */
    @ApiModelProperty(value = "srmId")
    private String recId;
    /**
     * 客户零件号
     */
    @ApiModelProperty(value = "客户零件号")
    private String partNumber;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
    /**
     * 星期几收货
     */
    @ApiModelProperty(value = "星期几收货")
    private String receivingMonthDay;
    /**
     * 每月几号收货
     */
    @ApiModelProperty(value = "每月几号收货")
    private String receivingWeekDay;
}
