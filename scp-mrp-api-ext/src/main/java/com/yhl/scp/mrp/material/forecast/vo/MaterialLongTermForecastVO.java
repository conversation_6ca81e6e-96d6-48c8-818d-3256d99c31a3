package com.yhl.scp.mrp.material.forecast.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <code>MaterialLongTermForecastVO</code>
 * <p>
 * 材料长期预测VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-22 15:46:19
 */
@ApiModel(value = "材料长期预测VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialLongTermForecastVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -94088973436275416L;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @FieldInterpretation(value = "物料编码")
    private String productCode;
    /**
     * 需求日期
     */
    @ApiModelProperty(value = "需求日期")
    @FieldInterpretation(value = "需求日期")
    private Date demandDate;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @FieldInterpretation(value = "数量")
    private BigDecimal demandQuantity;
    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    @FieldInterpretation(value = "供应商ID")
    private String supplierId;
    /**
     * 要货模式
     */
    @ApiModelProperty(value = "要货模式")
    @FieldInterpretation(value = "要货模式")
    private String demandPattern;
    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @FieldInterpretation(value = "供应商编码")
    private String supplierCode;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @FieldInterpretation(value = "供应商名称")
    private String supplierName;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    @FieldInterpretation(value = "物料名称")
    private String productName;
    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    @ExcelProperty(value = "库存点代码")
    private String stockPointCode;
    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    @ExcelProperty(value = "库存点名称")
    private String stockPointName;
    /**
     * 组织id
     */
    @ApiModelProperty(value = "组织id")
    @ExcelProperty(value = "组织id")
    private String organizationId;
    /**
     * 日期列表
     */
    @ApiModelProperty(value = "日期列表")
    @FieldInterpretation(value = "日期列表")
    private List<String> dateList;
    /**
     * 日期数量详情
     */
    @ApiModelProperty(value = "日期数量详情")
    @FieldInterpretation(value = "日期数量详情")
    private List<MaterialLongTermForecastDetailVO> detailList;
    /**
     * idList
     */
    @ApiModelProperty(value = "id集合")
    @FieldInterpretation(value = "id集合")
    private List<String> idList;

    /**
     * 推移版本号
     */
    @ApiModelProperty(value = "推移版本号")
    @FieldInterpretation(value = "推移版本号")
    private String inventoryShiftVersionCode;

    @Override
    public void clean() {

    }

}
