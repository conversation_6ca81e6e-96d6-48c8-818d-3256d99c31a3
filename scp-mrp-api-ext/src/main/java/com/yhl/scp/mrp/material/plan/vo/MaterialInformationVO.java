package com.yhl.scp.mrp.material.plan.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName MaterialInformationVO
 * @Description TODO
 * @Date 2024-12-04 17:09:28
 * <AUTHOR>
 * @Copyright 悠桦林信息科技（上海）有限公司
 * @Version 1.0
 */
@Data
public class MaterialInformationVO implements Serializable {

    private static final long serialVersionUID = -10569010534689321L;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "物料属性")
    private String materialProperty;

    @ApiModelProperty("最小起订量")
    private BigDecimal minOrderQty;

    @ApiModelProperty("采购周期")
    private BigDecimal purchaseLot;

    @ApiModelProperty("包装批量")
    private BigDecimal packageLot;

    @ApiModelProperty("订单下达提前期")
    private BigDecimal orderPlacementLeadTimeDay;

    @ApiModelProperty("要货计划锁定期")
    private BigDecimal requestCargoPlanLockDay;

    @ApiModelProperty("最小安全库存天数")
    private BigDecimal safetyStockDaysMin;

    @ApiModelProperty("目标安全库存天数")
    private BigDecimal safetyStockDaysStandard;

    @ApiModelProperty("最大安全库存天数")
    private BigDecimal safetyStockDaysMax;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("要货模式")
    private String demandPattern;

    @ApiModelProperty("送货频次")
    private String frequencyDelivery;

    @ApiModelProperty("车型编码")
    private String vehicleModelCode;

    @ApiModelProperty(value = "材料风险等级")
    private String materialRiskLevel;

}
