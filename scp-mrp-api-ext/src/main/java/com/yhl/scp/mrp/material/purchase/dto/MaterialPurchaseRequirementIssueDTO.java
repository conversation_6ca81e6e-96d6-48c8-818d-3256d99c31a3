package com.yhl.scp.mrp.material.purchase.dto;

import java.io.Serializable;
import java.util.Date;

import com.yhl.platform.common.ddd.BaseDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <code>MaterialPurchaseRequirementIssueDTO</code>
 * <p>
 * 材料采购需求下发记录DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-11 19:01:29
 */
@ApiModel(value = "材料采购需求下发记录DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialPurchaseRequirementIssueDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -40449484669888116L;

        
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
        
    /**
     * 下发状态
     */
    @ApiModelProperty(value = "下发状态")
    private String issueStatus;
        
    /**
     * 下发时间
     */
    @ApiModelProperty(value = "下发时间")
    private Date issueTime;
        
    /**
     * 下发人
     */
    @ApiModelProperty(value = "下发人")
    private String issuer;
        
    /**
     * 下发批次号
     */
    @ApiModelProperty(value = "下发批次号")
    private String issueBatchCode;
        
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
        
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
        
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer versionValue;

}
