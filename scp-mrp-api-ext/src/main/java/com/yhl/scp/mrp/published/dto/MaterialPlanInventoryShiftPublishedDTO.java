package com.yhl.scp.mrp.published.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialPlanInventoryShiftPublishedDTO</code>
 * <p>
 * 物料库存推移发布表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-21 14:29:02
 */
@ApiModel(value = "物料库存推移发布表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialPlanInventoryShiftPublishedDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 799136828992226491L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 物料计划发布版本id
     */
    @ApiModelProperty(value = "物料计划发布版本id")
    private String materialPlanPublishedVersionId;
    /**
     * 物品编码
     */
    @ApiModelProperty(value = "物品编码")
    private String productCode;
    /**
     * 物料分类
     */
    @ApiModelProperty(value = "物料分类")
    private String productClassify;
    /**
     * 库存点编码
     */
    @ApiModelProperty(value = "库存点编码")
    private String stockPointCode;
    /**
     * 所有浮法厂库存
     */
    @ApiModelProperty(value = "所有浮法厂库存")
    private BigDecimal allFfInventory;
    /**
     * 最小安全库存天数
     */
    @ApiModelProperty(value = "最小安全库存天数")
    private Long safetyStockDaysMin;
    /**
     * 目标安全库存天数
     */
    @ApiModelProperty(value = "目标安全库存天数")
    private Long safetyStockDaysStandard;
    /**
     * 最大安全库存天数
     */
    @ApiModelProperty(value = "最大安全库存天数")
    private Long safetyStockDaysMax;
    /**
     * 最小安全库存水位
     */
    @ApiModelProperty(value = "最小安全库存水位")
    private BigDecimal safetyStockLevelMin;
    /**
     * 标准安全库存水位
     */
    @ApiModelProperty(value = "标准安全库存水位")
    private BigDecimal safetyStockLevelStandard;
    /**
     * 最大安全库存水位
     */
    @ApiModelProperty(value = "最大安全库存水位")
    private BigDecimal safetyStockLevelMax;
    /**
     * 期初库存
     */
    @ApiModelProperty(value = "期初库存")
    private BigDecimal openingInventory;
    /**
     * 本厂期初库存
     */
    @ApiModelProperty(value = "本厂期初库存")
    private BigDecimal bcOpeningInventory;
    /**
     * 毛需求
     */
    @ApiModelProperty(value = "毛需求")
    private BigDecimal demandQuantity;
    /**
     * 供应数量
     */
    @ApiModelProperty(value = "供应数量")
    private BigDecimal supplyQuantity;
    /**
     * 计划调拨-浮法送柜
     */
    @ApiModelProperty(value = "计划调拨-浮法送柜")
    private BigDecimal adjustQuantityFromFloat;
    /**
     * 已发布调拨计划到货
     */
    @ApiModelProperty(value = "已发布调拨计划到货")
    private BigDecimal inputQuantity;
    /**
     * 计划调拨-码头送柜
     */
    @ApiModelProperty(value = "计划调拨-码头送柜")
    private BigDecimal adjustQuantityFromPort;
    /**
     * 在途码头送柜
     */
    @ApiModelProperty(value = "在途码头送柜")
    private BigDecimal transitQuantityFromPort;
    /**
     * 在途浮法送柜
     */
    @ApiModelProperty(value = "在途浮法送柜")
    private BigDecimal transitQuantityFromFloat;
    /**
     * 已发布调拨运出至本厂
     */
    @ApiModelProperty(value = "已发布调拨运出至本厂")
    private BigDecimal outputQuantityToBc;
    /**
     * 已发布调拨运出至码头
     */
    @ApiModelProperty(value = "已发布调拨运出至码头")
    private BigDecimal outputQuantityToPort;
    /**
     * 计划调拨运出至本厂
     */
    @ApiModelProperty(value = "计划调拨运出至本厂")
    private BigDecimal decisionOutputQuantityToBc;
    /**
     * 计划调拨运出至码头
     */
    @ApiModelProperty(value = "计划调拨运出至码头")
    private BigDecimal decisionOutputQuantityToPort;
    /**
     * 消耗标准量
     */
    @ApiModelProperty(value = "消耗标准量")
    private BigDecimal useStandardQuantity;
    /**
     * 消耗替代料量
     */
    @ApiModelProperty(value = "消耗替代料量")
    private BigDecimal useReplaceQuantity;
    /**
     * 被作为替代料消耗量
     */
    @ApiModelProperty(value = "被作为替代料消耗量")
    private BigDecimal usedAsReplaceQuantity;
    /**
     * 调整前期末库存
     */
    @ApiModelProperty(value = "调整前期末库存")
    private BigDecimal beforeEndingInventory;
    /**
     * 补库缺口
     */
    @ApiModelProperty(value = "补库缺口")
    private BigDecimal safetyStockGap;
    /**
     * 下单前期末库存
     */
    @ApiModelProperty(value = "下单前期末库存")
    private BigDecimal endingInventoryBeforeAdjust;
    /**
     * 期末库存
     */
    @ApiModelProperty(value = "期末库存")
    private BigDecimal endingInventory;
    /**
     * 期末库存天数
     */
    @ApiModelProperty(value = "期末库存天数")
    private String endingInventoryDays;
    /**
     * 库存推移日期
     */
    @ApiModelProperty(value = "库存推移日期")
    private Date inventoryDate;
    /**
     * 警告提醒
     */
    @ApiModelProperty(value = "警告提醒")
    private String warningRemind;
    /**
     * 警告提醒-期初库存
     */
    @ApiModelProperty(value = "警告提醒-期初库存")
    private String warningRemindStart;
    /**
     * 警告提醒-期末库存
     */
    @ApiModelProperty(value = "警告提醒-期末库存")
    private String warningRemindEnd;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;

}
