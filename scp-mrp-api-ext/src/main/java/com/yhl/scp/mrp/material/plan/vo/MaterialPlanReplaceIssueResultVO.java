package com.yhl.scp.mrp.material.plan.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialPlanReplaceIssueResultVO</code>
 * <p>
 * 替代料计划发布结果VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-11 23:20:58
 */
@ApiModel(value = "替代料计划发布结果VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialPlanReplaceIssueResultVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 803013242721117583L;

    /**
     * 库存推移id
     */
    @ApiModelProperty(value = "库存推移id")
    @FieldInterpretation(value = "库存推移id")
    private String materialPlanInventoryShiftId;
    /**
     * 计划替代规则id
     */
    @ApiModelProperty(value = "计划替代规则id")
    @FieldInterpretation(value = "计划替代规则id")
    private String planReplaceRuleId;
    /**
     * 主料物料编码
     */
    @ApiModelProperty(value = "主料物料编码")
    @FieldInterpretation(value = "主料物料编码")
    private String masterProductCode;
    /**
     * 替代料物编码
     */
    @ApiModelProperty(value = "替代料物编码")
    @FieldInterpretation(value = "替代料物编码")
    private String replaceProductCode;
    /**
     * 替代数量
     */
    @ApiModelProperty(value = "替代数量")
    @FieldInterpretation(value = "替代数量")
    private BigDecimal replaceQuantity;
    /**
     * 替代日期
     */
    @ApiModelProperty(value = "替代日期")
    @FieldInterpretation(value = "替代日期")
    private Date replaceDate;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @FieldInterpretation(value = "优先级")
    private Integer priority;
    /**
     * 替代类型
     */
    @ApiModelProperty(value = "替代类型")
    @FieldInterpretation(value = "替代类型")
    private String replaceType;
    /**
     * 替代开始日期
     */
    @ApiModelProperty(value = "替代开始日期")
    @FieldInterpretation(value = "替代开始日期")
    private Date replaceStartDate;
    /**
     * 替代结束日期
     */
    @ApiModelProperty(value = "替代结束日期")
    @FieldInterpretation(value = "替代结束日期")
    private Date replaceEndDate;
    /**
     * 替代库存批次
     */
    @ApiModelProperty(value = "替代库存批次")
    @FieldInterpretation(value = "替代库存批次")
    private String replaceInventoryBatch;
    /**
     * 生产工单
     */
    @ApiModelProperty(value = "生产工单")
    @FieldInterpretation(value = "生产工单")
    private String workOrderCode;

    @Override
    public void clean() {

    }

}
