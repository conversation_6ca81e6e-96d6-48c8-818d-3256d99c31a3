package com.yhl.scp.mrp.enquiry.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.yhl.platform.common.ddd.BaseDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <code>MaterialEnquiryTrackDTO</code>
 * <p>
 * 材料到货跟踪DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 11:37:45
 */
@ApiModel(value = "材料到货跟踪DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialEnquiryTrackDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -56676729026483665L;

        
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;
        
    /**
     * 要货计划ID
     */
    @ApiModelProperty(value = "要货计划ID")
    private String enquiryPlanId;
        
    /**
     * 采购单号
     */
    @ApiModelProperty(value = "采购单号")
    private String purchaseOrderCode;
        
    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    private String stockPointCode;
        
    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    private String stockPointName;
        
    /**
     * 本厂编号
     */
    @ApiModelProperty(value = "本厂编号")
    private String productCode;
    
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String productName;
        
    /**
     * 要货日期
     */
    @ApiModelProperty(value = "要货日期")
    private Date enquiryTime;
        
    /**
     * 要货数量
     */
        @ApiModelProperty(value = "要货数量")
    private BigDecimal enquiryQuantity;
    
    /**
     * 物料单位
     */
    @ApiModelProperty(value = "物料单位")
    private String productUnit;
        
    /**
     * 预计到货日期
     */
    @ApiModelProperty(value = "预计到货日期")
    private Date expectedArrivalTime;
        
    /**
     * 预计到货数量
     */
    @ApiModelProperty(value = "预计到货数量")
    private BigDecimal expectedArrivalQuantity;
        
    /**
     * 到货状态
     */
    @ApiModelProperty(value = "到货状态")
    private String arrivalStatus;
        
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
        
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
        
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer versionValue;

    /**
     * 预计到货数量
     */
    @ApiModelProperty(value = "预计到货数量")
    private BigDecimal predictArrivalQuantity;

}
