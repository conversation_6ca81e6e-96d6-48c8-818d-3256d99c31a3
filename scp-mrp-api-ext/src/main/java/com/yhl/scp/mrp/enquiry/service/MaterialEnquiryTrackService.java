package com.yhl.scp.mrp.enquiry.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mrp.enquiry.dto.MaterialEnquiryTrackDTO;
import com.yhl.scp.mrp.enquiry.vo.MaterialEnquiryTrackVO;

import java.util.List;

/**
 * <code>MaterialEnquiryTrackService</code>
 * <p>
 * 材料到货跟踪应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 11:37:53
 */
public interface MaterialEnquiryTrackService extends BaseService<MaterialEnquiryTrackDTO, MaterialEnquiryTrackVO> {

    /**
     * 查询所有
     *
     * @return list {@link MaterialEnquiryTrackVO}
     */
    List<MaterialEnquiryTrackVO> selectAll();

}
