package com.yhl.scp.mrp.material.purchase.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesReturnedPurchase;
import com.yhl.scp.mrp.material.purchase.dto.MaterialReturnedPurchaseDTO;
import com.yhl.scp.mrp.material.purchase.vo.MaterialReturnedPurchaseVO;

import java.util.List;
import java.util.Map;

/**
 * <code>MaterialReturnedPurchaseService</code>
 * <p>
 * 采购退货应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-08 15:17:53
 */
public interface MaterialReturnedPurchaseService extends BaseService<MaterialReturnedPurchaseDTO, MaterialReturnedPurchaseVO> {

    void doUpdateBatchSelective(List<MaterialReturnedPurchaseDTO> list);

    List<MaterialReturnedPurchaseVO> selectVOByParams(Map<String, Object> params);

    /**
     * 查询所有
     *
     * @return list {@link MaterialReturnedPurchaseVO}
     */
    List<MaterialReturnedPurchaseVO> selectAll();

    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> handleReturnedPurchase(List<MesReturnedPurchase> erpPrQuery);

    /**
     * 同步
     *
     * @return
     */
    BaseResponse<Void> syncReturnedPurchase(String tenantId);


    void doReturnedJob(Integer moveMinute);

}
