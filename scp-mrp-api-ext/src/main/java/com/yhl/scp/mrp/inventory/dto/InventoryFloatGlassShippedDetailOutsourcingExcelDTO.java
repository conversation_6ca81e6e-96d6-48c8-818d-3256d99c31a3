package com.yhl.scp.mrp.inventory.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.scp.common.excel.ExcelPropertyCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>InventoryFloatGlassShippedDetailOutsourcingExcelDTO</code>
 * <p>
 * 原片浮法已发运（外购）库存批次明细excel导入DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 20:31:57
 */
@ApiModel(value = "原片浮法已发运（外购）库存批次明细excel导入DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InventoryFloatGlassShippedDetailOutsourcingExcelDTO implements Serializable {

    private static final long serialVersionUID = 5571285145092801215L;

    /**
     * 物品规格
     */
    @ApiModelProperty(value = "物品规格")
    @FieldInterpretation(value = "物品规格")
    @ExcelProperty(value = "物品规格")
    private String productSpec;
    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    @FieldInterpretation(value = "等级")
    @ExcelProperty(value = "等级")
    private String level;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    @FieldInterpretation(value = "批次号")
    @ExcelProperty(value = "批次号")
    private String lotNumber;
    /**
     * 片/箱
     */
    @ApiModelProperty(value = "片/箱")
    @FieldInterpretation(value = "片")
    @ExcelProperty(value = "片/箱*")
    private BigDecimal perBox;
    /**
     * 箱数
     */
    @ApiModelProperty(value = "箱数")
    @FieldInterpretation(value = "箱数")
    @ExcelProperty(value = "箱数*")
    private BigDecimal box;
    /**
     * 实发片数
     */
    @ApiModelProperty(value = "实发片数")
    @FieldInterpretation(value = "实发片数")
    @ExcelProperty(value = "实发片数")
    private BigDecimal actualSentQuantity;
    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
    @FieldInterpretation(value = "面积")
    @ExcelProperty(value = "面积")
    private BigDecimal area;
    /**
     * 吨数
     */
    @ApiModelProperty(value = "吨数")
    @FieldInterpretation(value = "吨数")
    @ExcelProperty(value = "吨数")
    private BigDecimal weight;
    /**
     * 包装方式
     */
    @ApiModelProperty(value = "包装方式")
    @FieldInterpretation(value = "包装方式")
    @ExcelProperty(value = "包装方式")
    private String packageType;
    /**
     * 发运方式
     */
    @ApiModelProperty(value = "发运方式")
    @FieldInterpretation(value = "发运方式")
    @ExcelProperty(value = "发运方式*")
    private String shipmentMethod;
    /**
     * 柜号
     */
    @ApiModelProperty(value = "柜号")
    @FieldInterpretation(value = "柜号")
    @ExcelProperty(value = "柜号*")
    private String containerNumber;
    /**
     * 预计到港时间
     */
    @ApiModelProperty(value = "预计到港时间")
    @FieldInterpretation(value = "预计到港时间")
    @ExcelProperty(value = "预计到港时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date estimatedArrivalTime;
    /**
     * 实际到港时间
     */
    @ApiModelProperty(value = "实际到港时间")
    @FieldInterpretation(value = "实际到港时间")
    @ExcelProperty(value = "实际到港时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date actualArrivalTime;
    /**
     * 港口
     */
    @ApiModelProperty(value = "港口")
    @FieldInterpretation(value = "港口")
    @ExcelProperty(value = "港口")
    private String portName;
    /**
     * 承运商
     */
    @ApiModelProperty(value = "承运商")
    @FieldInterpretation(value = "承运商")
    @ExcelProperty(value = "承运商*")
    private String carrier;
    /**
     * 发货时间
     */
    @ApiModelProperty(value = "发货时间")
    @FieldInterpretation(value = "发货时间")
    @ExcelProperty(value = "发货时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date deliveryTime;
    /**
     * 物品编码
     */
    @ApiModelProperty(value = "物品编码")
    @FieldInterpretation(value = "物品编码")
    @ExcelProperty(value = "物品编码*")
    private String productCode;
    /**
     * 组织（库存点)
     */
    @ApiModelProperty(value = "组织")
    @FieldInterpretation(value = "组织")
    @ExcelProperty(value = "厂家*")
    private String stockPointCode;
    /**
     * 提单号
     */
    @ApiModelProperty(value = "提单号")
    @FieldInterpretation(value = "提单号")
    @ExcelProperty(value = "提单号")
    private String billNo;
    /**
     * 调拨单号
     */
    @ApiModelProperty(value = "调拨单号")
    @FieldInterpretation(value = "调拨单号")
    @ExcelProperty(value = "调拨单号")
    private String allocateNo;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @FieldInterpretation(value = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 批次等级代码
     */
    @ApiModelProperty(value = "批次等级代码")
    @FieldInterpretation(value = "批次等级代码")
//    @ExcelProperty(value = "批次等级代码")
    private String lotLevelCode;
    /**
     * 发货方式
     */
    @ApiModelProperty(value = "发货方式")
    @FieldInterpretation(value = "发货方式")
//    @ExcelProperty(value = "发货方式*")
    private String deliveryMethod;
    /**
     * PO
     */
    @ApiModelProperty(value = "PO")
    @FieldInterpretation(value = "PO")
//    @ExcelProperty(value = "PO")
    private String po;
    /**
     * 送柜时间
     */
    @ApiModelProperty(value = "送柜时间")
    @FieldInterpretation(value = "送柜时间")
//    @ExcelProperty(value = "送柜时间")
    private Date containerDeliveryTime;
    /**
     * 要求到港时间
     */
    @ApiModelProperty(value = "要求到港时间")
    @FieldInterpretation(value = "要求到港时间")
//    @ExcelProperty(value = "要求到港时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date requiredArrivalTime;
    /**
     * 厂家
     */
    @ApiModelProperty(value = "厂家")
    @FieldInterpretation(value = "厂家")
//    @ExcelProperty(value = "厂家")
    private String manufacturer;
}
