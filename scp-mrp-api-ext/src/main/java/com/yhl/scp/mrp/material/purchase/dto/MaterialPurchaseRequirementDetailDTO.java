package com.yhl.scp.mrp.material.purchase.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <code>MaterialPurchaseRequirementDetailDTO</code>
 * <p>
 * 材料采购需求明细DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-11 19:00:21
 */
@ApiModel(value = "材料采购需求明细DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialPurchaseRequirementDetailDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -29650291654301240L;

        
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 材料推移ID
     */
    @ApiModelProperty(value = "材料推移ID")
    private String noGlassInventoryShiftId;
        
    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private String productId;
        
    /**
     * 材料需求发布日期​
     */
    @ApiModelProperty(value = "​材料需求发布日期")
    private Date requirementReleaseDate;
        
    /**
     * 要货日期
     */
    @ApiModelProperty(value = "要货日期")
    private Date requireDate;
        
    /**
     * 要货数量
     */
    @ApiModelProperty(value = "要货数量")
    private BigDecimal requireQuantity;
        
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String productUnit;

    /**
     * 父级id（拆单）
     */
    @ApiModelProperty(value = "父级id（拆单）")
    private String parentId;
        
    /**
     * 采购单号
     */
    @ApiModelProperty(value = "采购单号")
    private String purchaseOrderCode;
    
    /**
     * 采购单行号
     */
    @ApiModelProperty(value = "采购单行号")
    private String purchaseOrderLineCode;
        
    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;
        
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
        
    /**
     * 材料采购需求下发ID
     */
    @ApiModelProperty(value = "材料采购需求下发ID")
    private String materialPurchaseRequirementIssueId;
    
    /**
     * 采购申请单号
     */
    @ApiModelProperty(value = "采购申请单号")
    private String purchaseRequestCode;
    
    /**
     * 采购申请行ID
     */
    @ApiModelProperty(value = "采购申请行ID")
    private String purchaseRequestLineId;
    
    /**
     * 采购申请行号
     */
    @ApiModelProperty(value = "采购申请行号")
    private String purchaseRequestLineCode;
    
    /**
     * 检验标识
     */
    @ApiModelProperty(value = "检验标识")
    private String validataType;
    
    /**
     * 检验信息
     */
    @ApiModelProperty(value = "检验信息")
    private String validataMsg;
        
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
        
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
        
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer versionValue;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String productCode;

    private BigDecimal packageLot;

    private BigDecimal minOrderQty;

    /**
     * 数据来源
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String dataSource;

    /**
     * 推移版本代码
     */
    @ApiModelProperty(value = "推移版本代码")
    private String inventoryShiftVersionCode;

}
