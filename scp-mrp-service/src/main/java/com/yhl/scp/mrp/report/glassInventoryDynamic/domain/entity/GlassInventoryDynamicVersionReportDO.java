package com.yhl.scp.mrp.report.glassInventoryDynamic.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>GlassInventoryDynamicVersionReportDO</code>
 * <p>
 * 原片库存动态版本表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-12 22:04:04
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class GlassInventoryDynamicVersionReportDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 220277978498072914L;

    /**
     * 主键
     */
    private String id;
    /**
     * 原片增幅-后一个月
     */
    private String glassNextOneMonthGrowthRate;
    /**
     * 原片增幅-后二个月
     */
    private String glassNextTwoMonthGrowthRate;
    /**
     * 原片增幅-后三个月
     */
    private String glassNextThreeMonthGrowthRate;
    /**
     * 主工序产量-当前月
     */
    private BigDecimal mainOperationOutputCurrentMonth;
    /**
     * 主工序产量-后一个月
     */
    private BigDecimal mainOperationOutputNextOneMonth;
    /**
     * 主工序产量-后二个月
     */
    private BigDecimal mainOperationOutputNextTwoMonth;
    /**
     * 主工序产量-后三个月
     */
    private BigDecimal mainOperationOutputNextThreeMonth;
    /**
     * 计划增幅-后一个月
     */
    private String planNextOneMonthGrowthRate;
    /**
     * 计划增幅-后二个月
     */
    private String planNextTwoMonthGrowthRate;
    /**
     * 计划增幅-后三个月
     */
    private String planNextThreeMonthGrowthRate;

}
