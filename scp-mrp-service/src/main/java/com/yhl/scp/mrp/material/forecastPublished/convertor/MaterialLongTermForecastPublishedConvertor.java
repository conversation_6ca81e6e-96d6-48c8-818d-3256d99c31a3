package com.yhl.scp.mrp.material.forecastPublished.convertor;

import com.yhl.scp.mrp.material.forecastPublished.domain.entity.MaterialLongTermForecastPublishedDO;
import com.yhl.scp.mrp.material.forecastPublished.dto.MaterialLongTermForecastPublishedDTO;
import com.yhl.scp.mrp.material.forecastPublished.infrastructure.po.MaterialLongTermForecastPublishedPO;
import com.yhl.scp.mrp.material.forecastPublished.vo.MaterialLongTermForecastPublishedVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>MaterialLongTermForecastPublishedConvertor</code>
 * <p>
 * 材料长期预测发布表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-07 15:02:58
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MaterialLongTermForecastPublishedConvertor {

    MaterialLongTermForecastPublishedConvertor INSTANCE = Mappers.getMapper(MaterialLongTermForecastPublishedConvertor.class);

    MaterialLongTermForecastPublishedDO dto2Do(MaterialLongTermForecastPublishedDTO obj);

    MaterialLongTermForecastPublishedDTO do2Dto(MaterialLongTermForecastPublishedDO obj);

    List<MaterialLongTermForecastPublishedDO> dto2Dos(List<MaterialLongTermForecastPublishedDTO> list);

    List<MaterialLongTermForecastPublishedDTO> do2Dtos(List<MaterialLongTermForecastPublishedDO> list);

    MaterialLongTermForecastPublishedVO do2Vo(MaterialLongTermForecastPublishedDO obj);

    MaterialLongTermForecastPublishedVO po2Vo(MaterialLongTermForecastPublishedPO obj);

    List<MaterialLongTermForecastPublishedVO> po2Vos(List<MaterialLongTermForecastPublishedPO> list);

    MaterialLongTermForecastPublishedPO do2Po(MaterialLongTermForecastPublishedDO obj);

    MaterialLongTermForecastPublishedDO po2Do(MaterialLongTermForecastPublishedPO obj);

    MaterialLongTermForecastPublishedPO dto2Po(MaterialLongTermForecastPublishedDTO obj);

    List<MaterialLongTermForecastPublishedPO> dto2Pos(List<MaterialLongTermForecastPublishedDTO> obj);

}
