package com.yhl.scp.mrp.material.plan.domain.factory;

import com.yhl.scp.mrp.material.plan.domain.entity.NoGlassInventoryShiftDataDO;
import com.yhl.scp.mrp.material.plan.dto.NoGlassInventoryShiftDataDTO;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.NoGlassInventoryShiftDataDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>NoGlassInventoryShiftDataFactory</code>
 * <p>
 * 非原片库存推移主表领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-26 15:25:06
 */
@Component
public class NoGlassInventoryShiftDataFactory {

    @Resource
    private NoGlassInventoryShiftDataDao noGlassInventoryShiftDataDao;

    NoGlassInventoryShiftDataDO create(NoGlassInventoryShiftDataDTO dto) {
        // TODO
        return null;
    }

}
