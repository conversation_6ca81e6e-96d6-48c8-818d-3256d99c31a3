package com.yhl.scp.mrp.material.plan.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.mrp.material.plan.dto.NoGlassInventoryShiftDTO;
import com.yhl.scp.mrp.material.plan.dto.NoGlassInventoryShiftDetailDTO;
import com.yhl.scp.mrp.material.plan.service.NoGlassInventoryShiftDetailService;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>NoGlassInventoryShiftDetailController</code>
 * <p>
 * 非原片库存推移详情表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-26 15:25:57
 */
@Slf4j
@Api(tags = "非原片库存推移详情表控制器")
@RestController
@RequestMapping("noGlassInventoryShiftDetail")
public class NoGlassInventoryShiftDetailController extends BaseController {

    @Resource
    private NoGlassInventoryShiftDetailService noGlassInventoryShiftDetailService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<NoGlassInventoryShiftDetailVO>> page() {
        List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftDetailList = noGlassInventoryShiftDetailService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<NoGlassInventoryShiftDetailVO> pageInfo = new PageInfo<>(noGlassInventoryShiftDetailList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody NoGlassInventoryShiftDetailDTO noGlassInventoryShiftDetailDTO) {
        return noGlassInventoryShiftDetailService.doCreate(noGlassInventoryShiftDetailDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody NoGlassInventoryShiftDetailDTO noGlassInventoryShiftDetailDTO) {
        return noGlassInventoryShiftDetailService.doUpdate(noGlassInventoryShiftDetailDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        noGlassInventoryShiftDetailService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<NoGlassInventoryShiftDetailVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, noGlassInventoryShiftDetailService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "校验是否可以调整")
    @PostMapping(value = "checkAdjust")
    public BaseResponse<String> checkAdjust(@RequestBody NoGlassInventoryShiftDTO noGlassInventoryShiftDTO) {
        try {
            return noGlassInventoryShiftDetailService.checkAdjust(noGlassInventoryShiftDTO);
        } catch (Exception e) {
            log.error("校验失败", e);
            throw new BusinessException("校验失败,{0}", e.getLocalizedMessage());
        }
    }

    @ApiOperation(value = "调整数据")
    @PostMapping(value = "adjustData")
    public BaseResponse<String> adjustData(@RequestBody NoGlassInventoryShiftDTO noGlassInventoryShiftDTO) {
        try {
            return noGlassInventoryShiftDetailService.doAdjustData(noGlassInventoryShiftDTO);
        } catch (Exception e) {
            log.error("调整失败", e);
            throw new BusinessException("调整失败,{0}", e.getLocalizedMessage());
        }
    }

}
