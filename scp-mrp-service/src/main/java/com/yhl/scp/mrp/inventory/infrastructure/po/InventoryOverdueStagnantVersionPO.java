package com.yhl.scp.mrp.inventory.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>InventoryOverdueStagnantVersionPO</code>
 * <p>
 * 超期呆滞库存版本PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-15 16:55:05
 */
public class InventoryOverdueStagnantVersionPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 106512186672290017L;

    /**
     * 版本号
     */
    private String versionCode;
    /**
     * 版本名称
     */
    private String versionName;
    /**
     * 版本
     */
    private Integer versionValue;

    public String getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
