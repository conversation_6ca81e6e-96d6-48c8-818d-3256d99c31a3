package com.yhl.scp.mrp.material.plan.domain.entity;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MaterialPlanVersionDO</code>
 * <p>
 * 物料计划版本DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-31 21:14:47
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialPlanVersionDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 784327771646115546L;

    /**
     * 主键
     */
    private String id;
    /**
     * 版本号
     */
    private String versionCode;
    /**
     * 主生产计划版本号
     */
    private String masterPlanVersionCode;
    /**
     * 发布状态
     */
    private String publishStatus;
    /**
     * 版本类型
     */
    private String versionType;
    /**
     * 材料类型
     */
    private String materialType;
    /**
     * 发布人
     */
    private String publisher;
    /**
     * 发布时间
     */
    private Date publishTime;

}
