package com.yhl.scp.mrp.inventory.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.mrp.inventory.domain.entity.FulfillmentManualDO;
import com.yhl.scp.mrp.inventory.infrastructure.dao.FulfillmentManualDao;
import com.yhl.scp.mrp.inventory.infrastructure.po.FulfillmentManualPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>FulfillmentManualDomainService</code>
 * <p>
 * 手动分配关系领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-04 10:25:16
 */
@Service
public class FulfillmentManualDomainService {

    @Resource
    private FulfillmentManualDao fulfillmentManualDao;

    /**
     * 数据校验
     *
     * @param fulfillmentManualDO 领域对象
     */
    public void validation(FulfillmentManualDO fulfillmentManualDO) {
        checkNotNull(fulfillmentManualDO);
        checkUniqueCode(fulfillmentManualDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param fulfillmentManualDO 领域对象
     */
    private void checkNotNull(FulfillmentManualDO fulfillmentManualDO) {}

    /**
     * 唯一性校验
     *
     * @param fulfillmentManualDO 领域对象
     */
    private void checkUniqueCode(FulfillmentManualDO fulfillmentManualDO) {}

}
