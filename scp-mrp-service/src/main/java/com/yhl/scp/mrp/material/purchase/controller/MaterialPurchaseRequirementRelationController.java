package com.yhl.scp.mrp.material.purchase.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseRequirementRelationDTO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseRequirementRelationService;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseRequirementRelationVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MaterialPurchaseRequirementRelationController</code>
 * <p>
 * 材料采购需求关系表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-21 13:53:55
 */
@Slf4j
@Api(tags = "材料采购需求关系表控制器")
@RestController
@RequestMapping("materialPurchaseRequirementRelation")
public class MaterialPurchaseRequirementRelationController extends BaseController {

    @Resource
    private MaterialPurchaseRequirementRelationService materialPurchaseRequirementRelationService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<MaterialPurchaseRequirementRelationVO>> page() {
        List<MaterialPurchaseRequirementRelationVO> materialPurchaseRequirementRelationList = materialPurchaseRequirementRelationService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialPurchaseRequirementRelationVO> pageInfo = new PageInfo<>(materialPurchaseRequirementRelationList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MaterialPurchaseRequirementRelationDTO materialPurchaseRequirementRelationDTO) {
        return materialPurchaseRequirementRelationService.doCreate(materialPurchaseRequirementRelationDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MaterialPurchaseRequirementRelationDTO materialPurchaseRequirementRelationDTO) {
        return materialPurchaseRequirementRelationService.doUpdate(materialPurchaseRequirementRelationDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        materialPurchaseRequirementRelationService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<MaterialPurchaseRequirementRelationVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialPurchaseRequirementRelationService.selectByPrimaryKey(id));
    }

}
