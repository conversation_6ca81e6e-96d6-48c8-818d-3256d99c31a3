package com.yhl.scp.mrp.material.purchase.controller;

import java.util.List;

import javax.annotation.Resource;

import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseRequirementDetailDTO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseRequirementDetailService;
import com.yhl.scp.mrp.extension.material.vo.MaterialPurchaseRequirementDetailVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>MaterialPurchaseRequirementDetailController</code>
 * <p>
 * 材料采购需求明细控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-11 19:00:58
 */
@Slf4j
@Api(tags = "材料采购需求明细控制器")
@RestController
@RequestMapping("materialPurchaseRequirementDetail")
public class MaterialPurchaseRequirementDetailController extends BaseController {

    @Resource
    private MaterialPurchaseRequirementDetailService materialPurchaseRequirementDetailService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<MaterialPurchaseRequirementDetailVO>> page() {
        List<MaterialPurchaseRequirementDetailVO> materialPurchaseRequirementDetailList = materialPurchaseRequirementDetailService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialPurchaseRequirementDetailVO> pageInfo = new PageInfo<>(materialPurchaseRequirementDetailList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MaterialPurchaseRequirementDetailDTO materialPurchaseRequirementDetailDTO) {
        return materialPurchaseRequirementDetailService.doCreate(materialPurchaseRequirementDetailDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MaterialPurchaseRequirementDetailDTO materialPurchaseRequirementDetailDTO) {
        return materialPurchaseRequirementDetailService.doUpdate(materialPurchaseRequirementDetailDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        materialPurchaseRequirementDetailService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<MaterialPurchaseRequirementDetailVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialPurchaseRequirementDetailService.selectDetailByPrimaryKey(id));
    }
    
    @ApiOperation(value = "批量下发")
    @PostMapping(value = "batchIssue")
    @BusinessMonitorLog(businessCode = "采购需求发布", moduleCode = "MRP", businessFrequency = "DAY")
    public BaseResponse<String> doBatchIssue(@RequestBody List<String> ids) {
        String result = materialPurchaseRequirementDetailService.doBatchIssue(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, result);
    }

}