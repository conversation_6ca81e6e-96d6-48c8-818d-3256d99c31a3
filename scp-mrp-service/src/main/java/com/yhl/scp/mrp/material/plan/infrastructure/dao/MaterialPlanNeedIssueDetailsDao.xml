<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanNeedIssueDetailsDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanNeedIssueDetailsPO">
        <!--@Table mrp_material_plan_need_issue_details-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="plan_need_id" jdbcType="VARCHAR" property="planNeedId"/>
        <result column="material_plan_need_no" jdbcType="VARCHAR" property="materialPlanNeedNo"/>
        <result column="need_quantity" jdbcType="VARCHAR" property="needQuantity"/>
        <result column="need_date" jdbcType="VARCHAR" property="needDate"/>
        <result column="purchase_order_code" jdbcType="VARCHAR" property="purchaseOrderCode"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="issue_version" jdbcType="INTEGER" property="issueVersion"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="dimension" jdbcType="VARCHAR" property="dimension"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.material.plan.vo.MaterialPlanNeedIssueDetailsVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,plan_need_id,material_plan_need_no,need_quantity,need_date,purchase_order_code,supplier_code,supplier_name
        ,product_code,issue_version,status,dimension,enabled,creator,create_time,modifier,modify_time

    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.planNeedId != null and params.planNeedId != ''">
                and plan_need_id = #{params.planNeedId,jdbcType=VARCHAR}
            </if>
            <if test="params.planNeedIds != null and params.planNeedIds.size() > 0">
                and plan_need_id in
                <foreach collection="params.planNeedIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.materialPlanNeedNo != null and params.materialPlanNeedNo != ''">
                and material_plan_need_no = #{params.materialPlanNeedNo,jdbcType=VARCHAR}
            </if>
            <if test="params.materialPlanNeedNos != null and params.materialPlanNeedNos.size() > 0">
                and material_plan_need_no in
                <foreach collection="params.materialPlanNeedNos" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.needQuantity != null">
                and need_quantity = #{params.needQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.needDate != null and params.needDate != ''">
                and need_date = #{params.needDate,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseOrderCode != null and params.purchaseOrderCode != ''">
                and purchase_order_code = #{params.purchaseOrderCode,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierCode != null and params.supplierCode != ''">
                and supplier_code = #{params.supplierCode,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierName != null and params.supplierName != ''">
                and supplier_name = #{params.supplierName,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.issueVersion != null">
                and issue_version = #{params.issueVersion,jdbcType=INTEGER}
            </if>
            <if test="params.status != null and params.status != ''">
                and status = #{params.status,jdbcType=VARCHAR}
            </if>
            <if test="params.dimension != null and params.dimension != ''">
                and dimension = #{params.dimension,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_need_issue_details
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_need_issue_details
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_material_plan_need_issue_details
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_plan_need_issue_details
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanNeedIssueDetailsPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_plan_need_issue_details(
        id,
        plan_need_id,
        material_plan_need_no,
        need_quantity,
        need_date,
        purchase_order_code,
        supplier_code,
        supplier_name,
        product_code,
        issue_version,
        status,
        dimension,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{planNeedId,jdbcType=VARCHAR},
        #{materialPlanNeedNo,jdbcType=VARCHAR},
        #{needQuantity,jdbcType=VARCHAR},
        #{needDate,jdbcType=VARCHAR},
        #{purchaseOrderCode,jdbcType=VARCHAR},
        #{supplierCode,jdbcType=VARCHAR},
        #{supplierName,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{issueVersion,jdbcType=INTEGER},
        #{status,jdbcType=VARCHAR},
        #{dimension,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanNeedIssueDetailsPO">
        insert into mrp_material_plan_need_issue_details(id,
                                                         plan_need_id,
                                                         material_plan_need_no,
                                                         need_quantity,
                                                         need_date,
                                                         purchase_order_code,
                                                         supplier_code,
                                                         supplier_name,
                                                         product_code,
                                                         issue_version,
                                                         status,
                                                         dimension,
                                                         enabled,
                                                         creator,
                                                         create_time,
                                                         modifier,
                                                         modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{planNeedId,jdbcType=VARCHAR},
                #{materialPlanNeedNo,jdbcType=VARCHAR},
                #{needQuantity,jdbcType=VARCHAR},
                #{needDate,jdbcType=VARCHAR},
                #{purchaseOrderCode,jdbcType=VARCHAR},
                #{supplierCode,jdbcType=VARCHAR},
                #{supplierName,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{issueVersion,jdbcType=INTEGER},
                #{status,jdbcType=VARCHAR},
                #{dimension,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_plan_need_issue_details(
        id,
        plan_need_id,
        material_plan_need_no,
        need_quantity,
        need_date,
        purchase_order_code,
        supplier_code,
        supplier_name,
        product_code,
        issue_version,
        status,
        dimension,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.planNeedId,jdbcType=VARCHAR},
            #{entity.materialPlanNeedNo,jdbcType=VARCHAR},
            #{entity.needQuantity,jdbcType=VARCHAR},
            #{entity.needDate,jdbcType=VARCHAR},
            #{entity.purchaseOrderCode,jdbcType=VARCHAR},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.supplierName,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.issueVersion,jdbcType=INTEGER},
            #{entity.status,jdbcType=VARCHAR},
            #{entity.dimension,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_plan_need_issue_details(
        id,
        plan_need_id,
        material_plan_need_no,
        need_quantity,
        need_date,
        purchase_order_code,
        supplier_code,
        supplier_name,
        product_code,
        issue_version,
        status,
        dimension,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.planNeedId,jdbcType=VARCHAR},
            #{entity.materialPlanNeedNo,jdbcType=VARCHAR},
            #{entity.needQuantity,jdbcType=VARCHAR},
            #{entity.needDate,jdbcType=VARCHAR},
            #{entity.purchaseOrderCode,jdbcType=VARCHAR},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.supplierName,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.issueVersion,jdbcType=INTEGER},
            #{entity.status,jdbcType=VARCHAR},
            #{entity.dimension,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanNeedIssueDetailsPO">
        update mrp_material_plan_need_issue_details
        set plan_need_id          = #{planNeedId,jdbcType=VARCHAR},
            material_plan_need_no = #{materialPlanNeedNo,jdbcType=VARCHAR},
            need_quantity         = #{needQuantity,jdbcType=VARCHAR},
            need_date             = #{needDate,jdbcType=VARCHAR},
            purchase_order_code   = #{purchaseOrderCode,jdbcType=VARCHAR},
            supplier_code         = #{supplierCode,jdbcType=VARCHAR},
            supplier_name         = #{supplierName,jdbcType=VARCHAR},
            product_code          = #{productCode,jdbcType=VARCHAR},
            issue_version         = #{issueVersion,jdbcType=INTEGER},
            status                = #{status,jdbcType=VARCHAR},
            dimension             = #{dimension,jdbcType=VARCHAR},
            enabled               = #{enabled,jdbcType=VARCHAR},
            modifier              = #{modifier,jdbcType=VARCHAR},
            modify_time           = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanNeedIssueDetailsPO">
        update mrp_material_plan_need_issue_details
        <set>
            <if test="item.planNeedId != null and item.planNeedId != ''">
                plan_need_id = #{item.planNeedId,jdbcType=VARCHAR},
            </if>
            <if test="item.materialPlanNeedNo != null and item.materialPlanNeedNo != ''">
                material_plan_need_no = #{item.materialPlanNeedNo,jdbcType=VARCHAR},
            </if>
            <if test="item.needQuantity != null">
                need_quantity = #{item.needQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.needDate != null and item.needDate != ''">
                need_date = #{item.needDate,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseOrderCode != null and item.purchaseOrderCode != ''">
                purchase_order_code = #{item.purchaseOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierCode != null and item.supplierCode != ''">
                supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierName != null and item.supplierName != ''">
                supplier_name = #{item.supplierName,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.issueVersion != null">
                issue_version = #{item.issueVersion,jdbcType=INTEGER},
            </if>
            <if test="item.status != null and item.status != ''">
                status = #{item.status,jdbcType=VARCHAR},
            </if>
            <if test="item.dimension != null and item.dimension != ''">
                dimension = #{item.dimension,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_plan_need_issue_details
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="plan_need_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planNeedId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_plan_need_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialPlanNeedNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="need_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.needQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="need_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.needDate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_order_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseOrderCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="issue_version = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.issueVersion,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.status,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="dimension = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dimension,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_plan_need_issue_details
            <set>
                <if test="item.planNeedId != null and item.planNeedId != ''">
                    plan_need_id = #{item.planNeedId,jdbcType=VARCHAR},
                </if>
                <if test="item.materialPlanNeedNo != null and item.materialPlanNeedNo != ''">
                    material_plan_need_no = #{item.materialPlanNeedNo,jdbcType=VARCHAR},
                </if>
                <if test="item.needQuantity != null">
                    need_quantity = #{item.needQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.needDate != null and item.needDate != ''">
                    need_date = #{item.needDate,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseOrderCode != null and item.purchaseOrderCode != ''">
                    purchase_order_code = #{item.purchaseOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierCode != null and item.supplierCode != ''">
                    supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierName != null and item.supplierName != ''">
                    supplier_name = #{item.supplierName,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.issueVersion != null">
                    issue_version = #{item.issueVersion,jdbcType=INTEGER},
                </if>
                <if test="item.status != null and item.status != ''">
                    status = #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.dimension != null and item.dimension != ''">
                    dimension = #{item.dimension,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_plan_need_issue_details
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_plan_need_issue_details where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
