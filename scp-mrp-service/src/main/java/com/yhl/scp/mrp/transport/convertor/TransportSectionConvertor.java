package com.yhl.scp.mrp.transport.convertor;

import com.yhl.scp.mrp.transport.domain.entity.TransportSectionDO;
import com.yhl.scp.mrp.transport.dto.TransportSectionDTO;
import com.yhl.scp.mrp.transport.infrastructure.po.TransportSectionPO;
import com.yhl.scp.mrp.transport.vo.TransportSectionVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>TransportSectionConvertor</code>
 * <p>
 * 运输路段转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-29 10:32:48
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TransportSectionConvertor {

    TransportSectionConvertor INSTANCE = Mappers.getMapper(TransportSectionConvertor.class);

    TransportSectionDO dto2Do(TransportSectionDTO obj);

    TransportSectionDTO do2Dto(TransportSectionDO obj);

    List<TransportSectionDO> dto2Dos(List<TransportSectionDTO> list);

    List<TransportSectionDTO> do2Dtos(List<TransportSectionDO> list);

    TransportSectionVO do2Vo(TransportSectionDO obj);

    TransportSectionVO po2Vo(TransportSectionPO obj);

    List<TransportSectionVO> po2Vos(List<TransportSectionPO> list);

    TransportSectionPO do2Po(TransportSectionDO obj);

    TransportSectionDO po2Do(TransportSectionPO obj);

    TransportSectionPO dto2Po(TransportSectionDTO obj);

    List<TransportSectionPO> dto2Pos(List<TransportSectionDTO> obj);

}
