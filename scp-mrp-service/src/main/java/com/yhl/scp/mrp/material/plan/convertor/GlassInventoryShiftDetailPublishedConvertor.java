package com.yhl.scp.mrp.material.plan.convertor;

import com.yhl.scp.mrp.material.plan.domain.entity.GlassInventoryShiftDetailPublishedDO;
import com.yhl.scp.mrp.material.plan.dto.GlassInventoryShiftDetailPublishedDTO;
import com.yhl.scp.mrp.material.plan.infrastructure.po.GlassInventoryShiftDetailPublishedPO;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftDetailPublishedVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>GlassInventoryShiftDetailPublishedConvertor</code>
 * <p>
 * 物料库存推移转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 23:10:40
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GlassInventoryShiftDetailPublishedConvertor {

    GlassInventoryShiftDetailPublishedConvertor INSTANCE = Mappers.getMapper(GlassInventoryShiftDetailPublishedConvertor.class);

    GlassInventoryShiftDetailPublishedDO dto2Do(GlassInventoryShiftDetailPublishedDTO obj);

    List<GlassInventoryShiftDetailPublishedDO> dto2Dos(List<GlassInventoryShiftDetailPublishedDTO> list);

    GlassInventoryShiftDetailPublishedDTO do2Dto(GlassInventoryShiftDetailPublishedDO obj);

    List<GlassInventoryShiftDetailPublishedDTO> do2Dtos(List<GlassInventoryShiftDetailPublishedDO> list);

    GlassInventoryShiftDetailPublishedDTO vo2Dto(GlassInventoryShiftDetailPublishedVO obj);

    List<GlassInventoryShiftDetailPublishedDTO> vo2Dtos(List<GlassInventoryShiftDetailPublishedVO> list);

    GlassInventoryShiftDetailPublishedVO po2Vo(GlassInventoryShiftDetailPublishedPO obj);

    List<GlassInventoryShiftDetailPublishedVO> po2Vos(List<GlassInventoryShiftDetailPublishedPO> list);

    GlassInventoryShiftDetailPublishedPO dto2Po(GlassInventoryShiftDetailPublishedDTO obj);

    List<GlassInventoryShiftDetailPublishedPO> dto2Pos(List<GlassInventoryShiftDetailPublishedDTO> obj);

    GlassInventoryShiftDetailPublishedVO do2Vo(GlassInventoryShiftDetailPublishedDO obj);

    GlassInventoryShiftDetailPublishedPO do2Po(GlassInventoryShiftDetailPublishedDO obj);

    GlassInventoryShiftDetailPublishedDO po2Do(GlassInventoryShiftDetailPublishedPO obj);

}
