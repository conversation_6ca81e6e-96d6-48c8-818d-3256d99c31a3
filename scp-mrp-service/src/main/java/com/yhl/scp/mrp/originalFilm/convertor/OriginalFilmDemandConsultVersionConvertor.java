package com.yhl.scp.mrp.originalFilm.convertor;

import com.yhl.scp.mrp.originalFilm.domain.entity.OriginalFilmDemandConsultVersionDO;
import com.yhl.scp.mrp.originalFilm.dto.OriginalFilmDemandConsultVersionDTO;
import com.yhl.scp.mrp.originalFilm.infrastructure.po.OriginalFilmDemandConsultVersionPO;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultVersionVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>OriginalFilmDemandConsultVersionConvertor</code>
 * <p>
 * 原片需求征询版本转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-24 14:13:55
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OriginalFilmDemandConsultVersionConvertor {

    OriginalFilmDemandConsultVersionConvertor INSTANCE = Mappers.getMapper(OriginalFilmDemandConsultVersionConvertor.class);

    OriginalFilmDemandConsultVersionDO dto2Do(OriginalFilmDemandConsultVersionDTO obj);

    OriginalFilmDemandConsultVersionDTO do2Dto(OriginalFilmDemandConsultVersionDO obj);

    List<OriginalFilmDemandConsultVersionDO> dto2Dos(List<OriginalFilmDemandConsultVersionDTO> list);

    List<OriginalFilmDemandConsultVersionDTO> do2Dtos(List<OriginalFilmDemandConsultVersionDO> list);

    OriginalFilmDemandConsultVersionVO do2Vo(OriginalFilmDemandConsultVersionDO obj);

    OriginalFilmDemandConsultVersionVO po2Vo(OriginalFilmDemandConsultVersionPO obj);

    List<OriginalFilmDemandConsultVersionVO> po2Vos(List<OriginalFilmDemandConsultVersionPO> list);

    OriginalFilmDemandConsultVersionPO do2Po(OriginalFilmDemandConsultVersionDO obj);

    OriginalFilmDemandConsultVersionDO po2Do(OriginalFilmDemandConsultVersionPO obj);

    OriginalFilmDemandConsultVersionPO dto2Po(OriginalFilmDemandConsultVersionDTO obj);

    List<OriginalFilmDemandConsultVersionPO> dto2Pos(List<OriginalFilmDemandConsultVersionDTO> obj);

}
