package com.yhl.scp.mrp.material.plan.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mrp.material.plan.infrastructure.po.NoGlassInventoryShiftDataPO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>NoGlassInventoryShiftDataDao</code>
 * <p>
 * 非原片库存推移主表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-26 15:25:04
 */
public interface NoGlassInventoryShiftDataDao extends BaseDao<NoGlassInventoryShiftDataPO, NoGlassInventoryShiftDataVO> {

    List<NoGlassInventoryShiftDataVO> selectBySpecialParams(@Param("params") Map<String, Object> params);

    Date getLastCreateTime(@Param("list") List<String> productCodes);

    List<NoGlassInventoryShiftDataVO> selectBySpecialNotDetailParams(@Param("params") Map<String, Object> params);

}
