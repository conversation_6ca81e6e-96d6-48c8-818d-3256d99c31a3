package com.yhl.scp.mrp.halfsubinventory.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.halfsubinventory.convertor.WarehouseHalfSubinventoryConvertor;
import com.yhl.scp.mrp.halfsubinventory.domain.entity.WarehouseHalfSubinventoryDO;
import com.yhl.scp.mrp.halfsubinventory.domain.service.WarehouseHalfSubinventoryDomainService;
import com.yhl.scp.mrp.halfsubinventory.dto.WarehouseHalfSubinventoryDTO;
import com.yhl.scp.mrp.halfsubinventory.infrastructure.dao.WarehouseHalfSubinventoryDao;
import com.yhl.scp.mrp.halfsubinventory.infrastructure.po.WarehouseHalfSubinventoryPO;
import com.yhl.scp.mrp.halfsubinventory.service.WarehouseHalfSubinventoryService;
import com.yhl.scp.mrp.halfsubinventory.vo.WarehouseHalfSubinventoryVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>WarehouseHalfSubinventoryServiceImpl</code>
 * <p>
 * 中转库半品辅料表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-24 17:38:21
 */
@Slf4j
@Service
public class WarehouseHalfSubinventoryServiceImpl extends AbstractService implements WarehouseHalfSubinventoryService {

    @Resource
    private WarehouseHalfSubinventoryDao warehouseHalfSubinventoryDao;

    @Resource
    private WarehouseHalfSubinventoryDomainService warehouseHalfSubinventoryDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(WarehouseHalfSubinventoryDTO warehouseHalfSubinventoryDTO) {
        // 0.数据转换
        WarehouseHalfSubinventoryDO warehouseHalfSubinventoryDO = WarehouseHalfSubinventoryConvertor.INSTANCE.dto2Do(warehouseHalfSubinventoryDTO);
        WarehouseHalfSubinventoryPO warehouseHalfSubinventoryPO = WarehouseHalfSubinventoryConvertor.INSTANCE.dto2Po(warehouseHalfSubinventoryDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        warehouseHalfSubinventoryDomainService.validation(warehouseHalfSubinventoryDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(warehouseHalfSubinventoryPO);
        warehouseHalfSubinventoryDao.insert(warehouseHalfSubinventoryPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(WarehouseHalfSubinventoryDTO warehouseHalfSubinventoryDTO) {
        // 0.数据转换
        WarehouseHalfSubinventoryDO warehouseHalfSubinventoryDO = WarehouseHalfSubinventoryConvertor.INSTANCE.dto2Do(warehouseHalfSubinventoryDTO);
        WarehouseHalfSubinventoryPO warehouseHalfSubinventoryPO = WarehouseHalfSubinventoryConvertor.INSTANCE.dto2Po(warehouseHalfSubinventoryDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        warehouseHalfSubinventoryDomainService.validation(warehouseHalfSubinventoryDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(warehouseHalfSubinventoryPO);
        warehouseHalfSubinventoryDao.update(warehouseHalfSubinventoryPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<WarehouseHalfSubinventoryDTO> list) {
        List<WarehouseHalfSubinventoryPO> newList = WarehouseHalfSubinventoryConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        warehouseHalfSubinventoryDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<WarehouseHalfSubinventoryDTO> list) {
        List<WarehouseHalfSubinventoryPO> newList = WarehouseHalfSubinventoryConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        warehouseHalfSubinventoryDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return warehouseHalfSubinventoryDao.deleteBatch(idList);
        }
        return warehouseHalfSubinventoryDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public WarehouseHalfSubinventoryVO selectByPrimaryKey(String id) {
        WarehouseHalfSubinventoryPO po = warehouseHalfSubinventoryDao.selectByPrimaryKey(id);
        return WarehouseHalfSubinventoryConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "WAREHOUSE_HALF_SUBINVENTORY")
    public List<WarehouseHalfSubinventoryVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "WAREHOUSE_HALF_SUBINVENTORY")
    public List<WarehouseHalfSubinventoryVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<WarehouseHalfSubinventoryVO> dataList = warehouseHalfSubinventoryDao.selectByCondition(sortParam, queryCriteriaParam);
        WarehouseHalfSubinventoryServiceImpl target = SpringBeanUtils.getBean(WarehouseHalfSubinventoryServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<WarehouseHalfSubinventoryVO> selectByParams(Map<String, Object> params) {
        List<WarehouseHalfSubinventoryPO> list = warehouseHalfSubinventoryDao.selectByParams(params);
        return WarehouseHalfSubinventoryConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<WarehouseHalfSubinventoryVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.WAREHOUSE_HALF_SUBINVENTORY.getCode();
    }

    @Override
    public List<WarehouseHalfSubinventoryVO> invocation(List<WarehouseHalfSubinventoryVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
