package com.yhl.scp.mrp.excel.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.common.excel.DataImportInfo;
import com.yhl.scp.common.excel.ImportDataTypeEnum;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.excel.service.AbstractExcelService;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mrp.inventory.convertor.SafetyInventoryConvertor;
import com.yhl.scp.mrp.inventory.dto.SafetyInventoryDTO;
import com.yhl.scp.mrp.inventory.infrastructure.dao.SafetyInventoryDao;
import com.yhl.scp.mrp.inventory.infrastructure.po.SafetyInventoryPO;
import com.yhl.scp.mrp.inventory.service.SafetyInventoryService;
import com.yhl.scp.mrp.inventory.vo.SafetyInventoryVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>SafetyInventoryExcelService</code>
 * <p>
 * 材料安全库存导入服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-17 10:41:41
 */
@Service
public class SafetyInventoryExcelService extends AbstractExcelService<SafetyInventoryDTO, SafetyInventoryPO, SafetyInventoryVO> {

    @Resource
    private SafetyInventoryService safetyInventoryService;

    @Resource
    private SafetyInventoryDao safetyInventoryDao;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Override
    public BaseDao<SafetyInventoryPO, SafetyInventoryVO> getBaseDao() {
        return safetyInventoryDao;
    }

    @Override
    public Function<SafetyInventoryDTO, SafetyInventoryPO> getDTO2POConvertor() {
        return SafetyInventoryConvertor.INSTANCE::dto2Po;
    }

    @Override
    public Class<SafetyInventoryDTO> getDTOClass() {
        return SafetyInventoryDTO.class;
    }

    @Override
    public BaseService<SafetyInventoryDTO, SafetyInventoryVO> getBaseService() {
        return safetyInventoryService;
    }

    @Override
    protected void fillIdForUpdateData(List<SafetyInventoryDTO> list, Map<String, SafetyInventoryPO> map) {

        for (SafetyInventoryDTO safetyInventoryDTO : list) {
            SafetyInventoryPO safetyInventoryPO = map.get(safetyInventoryDTO.getStockPointCode() + "&" + safetyInventoryDTO.getMaterialCode());
            if (safetyInventoryPO != null) {
                safetyInventoryDTO.setId(safetyInventoryPO.getId());
            }
        }
    }

    @Override
    protected void specialVerification(ImportAnalysisResultHolder<SafetyInventoryDTO, SafetyInventoryPO> resultHolder, ImportContext importContext) {
        List<SafetyInventoryDTO> insertList = resultHolder.getInsertList();
        List<SafetyInventoryDTO> updateList = resultHolder.getUpdateList();
        if(CollectionUtils.isNotEmpty(insertList)) {
        	this.verifyPaternity(insertList, resultHolder.getImportLogList());
        }
        if(CollectionUtils.isNotEmpty(updateList)) {
        	this.verifyPaternity(updateList, resultHolder.getImportLogList());
        }
        resultHolder.setInsertList(insertList);
        resultHolder.setUpdateList(updateList);
    }

    private void verifyPaternity(List<SafetyInventoryDTO> checkList, List<DataImportInfo> importLogList) {
        BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), 
        		TenantCodeEnum.FYQB.getCode());
        List<String> stockPointCodes = checkList.stream().map(SafetyInventoryDTO::getStockPointCode).distinct().collect(Collectors.toList());
        List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectStockPointByParams(scenario.getData(), 
        		ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), 
        				"stockPointCodes", stockPointCodes));
        List<String> materialCodes = checkList.stream().map(SafetyInventoryDTO::getMaterialCode).distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(scenario.getData(), 
        		ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), 
				"stockPointCodes", stockPointCodes,
        		"productCodes", materialCodes));
        Map<String, NewProductStockPointVO> productStockPointCodeMap = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(data -> String.join("&", data.getProductCode(), data.getStockPointCode()),
                        Function.identity(), (v1, v2) -> v1));
        Map<String, List<NewProductStockPointVO>> productCodeMap = newProductStockPointVOS.stream()
                .collect(Collectors.groupingBy(NewProductStockPointVO::getProductCode));
        if (newStockPointVOS == null) {
            throw new BusinessException("基础数据未维护，请维护后再尝试");
        }
        // 查询库存点数据
        Map<String, NewStockPointVO> stockPointVOMap = CollectionUtils.isEmpty(newStockPointVOS) ? MapUtil.newHashMap() :
                newStockPointVOS.stream().collect(Collectors.toMap(NewStockPointVO::getStockPointCode, Function.identity(), (v1, v2) -> v1));
        Iterator<SafetyInventoryDTO> iterator = checkList.iterator();
        while (iterator.hasNext()) {
            SafetyInventoryDTO safetyInventoryDTO = iterator.next();
            if (!stockPointVOMap.containsKey(safetyInventoryDTO.getStockPointCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + safetyInventoryDTO.getRowIndex() + ";[库存点代码]不存在");
                dataImportInfo.setDisplayIndex(safetyInventoryDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            if (!productCodeMap.containsKey(safetyInventoryDTO.getMaterialCode())) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + safetyInventoryDTO.getRowIndex() + ";[物料代码]不存在");
                dataImportInfo.setDisplayIndex(safetyInventoryDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
                continue;
            }
            NewProductStockPointVO productStockPointVO = productStockPointCodeMap.get(String.join("&",
                    safetyInventoryDTO.getMaterialCode(), safetyInventoryDTO.getStockPointCode()));
            if (Objects.isNull(productStockPointVO)) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark("行数：" + safetyInventoryDTO.getRowIndex() + ";[物料代码]与库存点不匹配");
                dataImportInfo.setDisplayIndex(safetyInventoryDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
            }
        }
    }

    @Override
    protected ImportRelatedDataHolder<SafetyInventoryPO> prepareData(List<SafetyInventoryDTO> safetyInventoryDTOS) {
        // 找到数据库现在所有的数据
        List<SafetyInventoryPO> existingData = safetyInventoryDao.selectByParams(new HashMap<>(2));
        Map<String, SafetyInventoryPO> codeToPOMap = existingData.stream().collect(Collectors.toMap(x -> x.getStockPointCode() + "&" + x.getMaterialCode(), Function.identity(), (v1, v2) -> v1));
        // 组成唯一键的字段
        List<String> uniqueKeys = ListUtil.of("stockPointCode", "materialCode");
        // 外键字段
        List<String> foreignKeys = ListUtil.of();
        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();

        return ImportRelatedDataHolder.<SafetyInventoryPO>builder()
                .existingData(existingData)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .existingDataMap(codeToPOMap)
                .build();
    }

}
