package com.yhl.scp.mrp.material.plan.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.material.plan.convertor.MaterialPlanVersionConvertor;
import com.yhl.scp.mrp.material.plan.domain.entity.MaterialPlanVersionDO;
import com.yhl.scp.mrp.material.plan.domain.service.MaterialPlanVersionDomainService;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanVersionDTO;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanVersionDao;
import com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanVersionPO;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanVersionService;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanVersionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MaterialPlanVersionServiceImpl</code>
 * <p>
 * 物料计划版本应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-31 21:14:46
 */
@Slf4j
@Service
public class MaterialPlanVersionServiceImpl extends AbstractService implements MaterialPlanVersionService {

    @Resource
    private MaterialPlanVersionDao materialPlanVersionDao;

    @Resource
    private MaterialPlanVersionDomainService materialPlanVersionDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(MaterialPlanVersionDTO materialPlanVersionDTO) {
        // 0.数据转换
        MaterialPlanVersionDO materialPlanVersionDO = MaterialPlanVersionConvertor.INSTANCE.dto2Do(materialPlanVersionDTO);
        MaterialPlanVersionPO materialPlanVersionPO = MaterialPlanVersionConvertor.INSTANCE.dto2Po(materialPlanVersionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialPlanVersionDomainService.validation(materialPlanVersionDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialPlanVersionPO);
        materialPlanVersionDao.insertWithPrimaryKey(materialPlanVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MaterialPlanVersionDTO materialPlanVersionDTO) {
        // 0.数据转换
        MaterialPlanVersionDO materialPlanVersionDO = MaterialPlanVersionConvertor.INSTANCE.dto2Do(materialPlanVersionDTO);
        MaterialPlanVersionPO materialPlanVersionPO = MaterialPlanVersionConvertor.INSTANCE.dto2Po(materialPlanVersionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialPlanVersionDomainService.validation(materialPlanVersionDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialPlanVersionPO);
        materialPlanVersionDao.update(materialPlanVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialPlanVersionDTO> list) {
        List<MaterialPlanVersionPO> newList = MaterialPlanVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialPlanVersionDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialPlanVersionDTO> list) {
        List<MaterialPlanVersionPO> newList = MaterialPlanVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialPlanVersionDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialPlanVersionDao.deleteBatch(idList);
        }
        return materialPlanVersionDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialPlanVersionVO selectByPrimaryKey(String id) {
        MaterialPlanVersionPO po = materialPlanVersionDao.selectByPrimaryKey(id);
        return MaterialPlanVersionConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mrp_material_plan_version")
    public List<MaterialPlanVersionVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mrp_material_plan_version")
    public List<MaterialPlanVersionVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialPlanVersionVO> dataList = materialPlanVersionDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialPlanVersionServiceImpl target = SpringBeanUtils.getBean(MaterialPlanVersionServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialPlanVersionVO> selectByParams(Map<String, Object> params) {
        List<MaterialPlanVersionPO> list = materialPlanVersionDao.selectByParams(params);
        return MaterialPlanVersionConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialPlanVersionVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
//        return ObjectTypeEnum.MATERIAL_PLAN_VERSION.getCode();
        return "";
    }

    @Override
    public List<MaterialPlanVersionVO> invocation(List<MaterialPlanVersionVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public MaterialPlanVersionVO selectLatestVersion(String materialType, String userId) {
        MaterialPlanVersionPO materialPlanVersionPO = materialPlanVersionDao.selectLatestVersion(materialType, userId);
        return MaterialPlanVersionConvertor.INSTANCE.po2Vo(materialPlanVersionPO);
    }

    @Override
    public String selectLatestVersionId() {
        return materialPlanVersionDao.selectLatestVersionId();
    }

}
