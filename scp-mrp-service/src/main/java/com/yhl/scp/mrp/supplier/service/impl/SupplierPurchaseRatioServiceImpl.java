package com.yhl.scp.mrp.supplier.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.CustomThreadPoolFactory;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.enums.ObjectTypeEnum;
import com.yhl.scp.mds.basic.supplier.dto.SupplierPurchaseRatioBasicDTO;
import com.yhl.scp.mds.extension.supplier.domain.entity.SupplierPurchaseRatioDO;
import com.yhl.scp.mds.extension.supplier.dto.SupplierProductDTO;
import com.yhl.scp.mds.extension.supplier.dto.SupplierPurchaseRatioDTO;

import com.yhl.scp.mds.extension.supplier.infrastructure.po.SupplierPurchaseRatioPO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierPurchaseRatioVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;

import com.yhl.scp.mds.supplier.vo.SupplierPurchaseRationMap;
import com.yhl.scp.mrp.supplier.convertor.SupplierPurchaseRatioConvertor;
import com.yhl.scp.mrp.supplier.domain.service.SupplierPurchaseRatioDomainService;

import com.yhl.scp.mrp.supplier.infrastructure.dao.SupplierPurchaseRatioDao;

import com.yhl.scp.mrp.supplier.service.SupplierPurchaseRatioService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <code>SupplierPurchaseRatioServiceImpl</code>
 * <p>
 * 供应商采购比例应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-26 00:14:49
 */
@Slf4j
@Service
public class SupplierPurchaseRatioServiceImpl extends AbstractService implements SupplierPurchaseRatioService {

    @Resource
    private SupplierPurchaseRatioDao supplierPurchaseRatioDao;

    @Resource
    private SupplierPurchaseRatioDomainService supplierPurchaseRatioDomainService;

    // @Resource
    // private SupplierProductService supplierProductService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;
    // @Resource
    // private ProductService productService;


    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(SupplierPurchaseRatioDTO supplierPurchaseRatioDTO) {
        // 0.数据转换
        SupplierPurchaseRatioDO supplierPurchaseRatioDO = SupplierPurchaseRatioConvertor.INSTANCE.dto2Do(supplierPurchaseRatioDTO);
        SupplierPurchaseRatioPO supplierPurchaseRatioPO = SupplierPurchaseRatioConvertor.INSTANCE.dto2Po(supplierPurchaseRatioDTO);
        // 1.数据校验
        supplierPurchaseRatioDomainService.validation(supplierPurchaseRatioDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(supplierPurchaseRatioPO);
        supplierPurchaseRatioDao.insert(supplierPurchaseRatioPO);
        // 更新供应商采购物品是否启用
        //this.doUpdateEnabled(supplierPurchaseRatioDTO.getSupplierProductId());
        //计算分配比例
        this.doCalculate(supplierPurchaseRatioDTO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(SupplierPurchaseRatioDTO supplierPurchaseRatioDTO) {
        // 0.数据转换
        SupplierPurchaseRatioDO supplierPurchaseRatioDO = SupplierPurchaseRatioConvertor.INSTANCE.dto2Do(supplierPurchaseRatioDTO);
        SupplierPurchaseRatioPO supplierPurchaseRatioPO = SupplierPurchaseRatioConvertor.INSTANCE.dto2Po(supplierPurchaseRatioDTO);
        // 1.数据校验
        supplierPurchaseRatioDomainService.validation(supplierPurchaseRatioDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(supplierPurchaseRatioPO);
        supplierPurchaseRatioDao.update(supplierPurchaseRatioPO);
        // 更新供应商采购物品是否启用
        //this.doUpdateEnabled(supplierPurchaseRatioDTO.getSupplierProductId());
        //计算分配比例
        this.doCalculate(supplierPurchaseRatioDTO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<SupplierPurchaseRatioDTO> list) {
        List<SupplierPurchaseRatioPO> newList = SupplierPurchaseRatioConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        supplierPurchaseRatioDao.insertBatch(newList);
    }

    @Override
    public void doCreateBatchWithPrimaryKey(List<SupplierPurchaseRatioDTO> list) {
        List<SupplierPurchaseRatioPO> newList = SupplierPurchaseRatioConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        supplierPurchaseRatioDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<SupplierPurchaseRatioDTO> list) {
        List<SupplierPurchaseRatioPO> newList = SupplierPurchaseRatioConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        supplierPurchaseRatioDao.updateBatch(newList);
    }

    @Override
    public void doUpdateBatchSelective(List<SupplierPurchaseRatioDTO> list) {
        List<SupplierPurchaseRatioPO> newList = SupplierPurchaseRatioConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        supplierPurchaseRatioDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        List<String> supplierProductIds = supplierPurchaseRatioDao.selectByPrimaryKeys(idList).stream()
                .map(SupplierPurchaseRatioPO::getSupplierProductId).distinct().collect(Collectors.toList());
        int num;
        if (idList.size() > 1) {
            num = supplierPurchaseRatioDao.deleteBatch(idList);
        } else {
            num = supplierPurchaseRatioDao.deleteByPrimaryKey(idList.get(0));
        }
        // 更新供应商采购物品是否启用
        /*for (String supplierProductId : supplierProductIds) {
            this.doUpdateEnabled(supplierProductId);
        }*/
        return num;
    }

    @Override
    public SupplierPurchaseRatioVO selectByPrimaryKey(String id) {
        SupplierPurchaseRatioPO po = supplierPurchaseRatioDao.selectByPrimaryKey(id);
        return SupplierPurchaseRatioConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mds_sup_supplier_purchase_ratio")
    public List<SupplierPurchaseRatioVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mds_sup_supplier_purchase_ratio")
    public List<SupplierPurchaseRatioVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<SupplierPurchaseRatioVO> dataList = supplierPurchaseRatioDao.selectByCondition(sortParam, queryCriteriaParam);
        SupplierPurchaseRatioServiceImpl target = springBeanUtils.getBean(SupplierPurchaseRatioServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<SupplierPurchaseRatioVO> selectByParams(Map<String, Object> params) {
        List<SupplierPurchaseRatioPO> list = supplierPurchaseRatioDao.selectByParams(params);
        return SupplierPurchaseRatioConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<SupplierPurchaseRatioVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.SUPPLIER_PURCHASE_RATIO.getCode();
    }

    @Override
    public List<SupplierPurchaseRatioVO> invocation(List<SupplierPurchaseRatioVO> dataList, Map<String, Object> params, String invocation) {
        if (CollectionUtils.isEmpty(dataList) && params != null && !params.isEmpty()) {
            dataList = this.selectByParams(params);
        }
        int degree = invocation.split("#").length;
        ThreadPoolExecutor THREAD_POOL_EXECUTOR = CustomThreadPoolFactory.getDegreeThreadPool(degree);
        String dataSource = DynamicDataSourceContextHolder.getDataSource();
        // 异步任务集合
        List<CompletableFuture<Void>> completableFutures = new ArrayList<>();
        List<SupplierPurchaseRatioVO> finalDataList = dataList;
        SupplierPurchaseRationMap supplierPurchaseRationMap = new SupplierPurchaseRationMap();
        supplierPurchaseRationMap.setSupplierPurchaseRatioVOList(finalDataList);
        HashMap<String, Object> map = MapUtil.newHashMap(3);
        map.put("finalDataList",finalDataList);
        map.put("invocation",invocation);
        map.put("setSupplier", "setSupplier");
        map.put("ids","ids");
        map.put("supplierId","supplierId");
        map.put("id","id");
        supplierPurchaseRationMap.setMap(map);
        completableFutures.add(CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(dataSource);
            // 冗余供应商字段
            BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
            newMdsFeign.supplierAddSupplierColumn(scenario.getData(),supplierPurchaseRationMap);
        }, THREAD_POOL_EXECUTOR));
/*        completableFutures.add(CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(dataSource);
            // 冗余物品字段
            productService.addProductColumn(finalDataList, invocation, "setSupplierProduct", "ids", "supplierProductId", "id");
        }, THREAD_POOL_EXECUTOR));*/
        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();
        return dataList;
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public void doUpdateEnabled(String supplierProductId) {
        if (StringUtils.isBlank(supplierProductId)) {
            return;
        }
        HashMap<String, Object> params = new HashMap<>();
        params.put("supplierProductId", supplierProductId);
        params.put("enabled", YesOrNoEnum.YES.getCode());
        List<SupplierPurchaseRatioPO> supplierPurchaseRatios = supplierPurchaseRatioDao.selectByParams(params);
        BigDecimal sum = supplierPurchaseRatios.stream().map(SupplierPurchaseRatioPO::getPurchaseRatio).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        String enabled = sum.compareTo(new BigDecimal("1")) == 0 ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode();
        // 当前行下所有供应商采购比例之和是否为100%，是则启用，否则不启用
        SupplierProductDTO supplierProductDTO = new SupplierProductDTO();
        supplierProductDTO.setId(supplierProductId);
        supplierProductDTO.setEnabled(enabled);
        //supplierProductService.doUpdateSelective(supplierProductDTO);
    }

    /**
     * 新增或更新时重新计算分配比例
     * @param supplierPurchaseRatioDTO
     */
    private void doCalculate(SupplierPurchaseRatioDTO supplierPurchaseRatioDTO) {
        Map<String, Object> params = new HashMap<>(4);
        params.put("supplierProductId", supplierPurchaseRatioDTO.getSupplierProductId());
        params.put("enabled", YesOrNoEnum.YES.getCode());
        List<SupplierPurchaseRatioPO> list = supplierPurchaseRatioDao.selectByParams(params);
        Map<String, Pair<String, Date>> createMap = list.stream().collect(Collectors.toMap(SupplierPurchaseRatioPO::getId, t -> Pair.of(t.getCreator(), t.getCreateTime())));
        List<SupplierPurchaseRatioDTO> supplierPurchaseRatioDTOS = SupplierPurchaseRatioConvertor.INSTANCE.pos2Dtos(list);
        calculatedDistributionRatio(supplierPurchaseRatioDTOS, supplierPurchaseRatioDTO);
        List<SupplierPurchaseRatioPO> updateList = SupplierPurchaseRatioConvertor.INSTANCE.dto2Pos(supplierPurchaseRatioDTOS);
        updateList.forEach(
                t->{
                    t.setCreator(createMap.get(t.getId()).getLeft());
                    t.setCreateTime(createMap.get(t.getId()).getRight());
                }
        );
        if(CollectionUtils.isNotEmpty(updateList)){
            BasePOUtils.updateBatchFiller(updateList);
            supplierPurchaseRatioDao.updateBatch(updateList);
        }

    }

//    @Override
//    public void calculatedDistributionRatio(List<SupplierPurchaseRatioDTO> dtoList) {
//        if (CollectionUtils.isEmpty(dtoList)){
//            return;
//        }
//        Map<String, List<SupplierPurchaseRatioDTO>> listMap = dtoList.stream().collect(Collectors.groupingBy(SupplierPurchaseRatioBasicDTO::getSupplierProductId));
//        for (Map.Entry<String, List<SupplierPurchaseRatioDTO>> entry : listMap.entrySet()) {
//            List<SupplierPurchaseRatioDTO> value = entry.getValue();
//            BigDecimal total = value.stream().map(SupplierPurchaseRatioDTO::getPurchaseRatio).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
//            for (SupplierPurchaseRatioDTO supplierPurchaseRatioDTO : value) {
//                if (Objects.isNull(supplierPurchaseRatioDTO.getPurchaseRatio())){
//                    throw new BusinessException("该供应商的采购比例字段为空，请进行数据维护。");
//                }
//                BigDecimal divide = supplierPurchaseRatioDTO.getPurchaseRatio().divide(total, 4, RoundingMode.HALF_UP);
//                supplierPurchaseRatioDTO.setPurchaseRatio(divide);
//            }
//        }
//    }

    @Override
    public void calculatedDistributionRatio(List<SupplierPurchaseRatioDTO> dtoList, SupplierPurchaseRatioDTO modifiedSupplierPurchaseRatioDTO) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }

        // 找到被修改的比例
        BigDecimal modifiedRatio = BigDecimal.ZERO;
        if (null != modifiedSupplierPurchaseRatioDTO){
            modifiedRatio = modifiedSupplierPurchaseRatioDTO.getPurchaseRatio();
        }

        // 计算所有比例的总和，排除被修改的比例
        BigDecimal total = dtoList.stream()
                .map(SupplierPurchaseRatioDTO::getPurchaseRatio)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        total = total.subtract(modifiedRatio);

        // 如果总比例为0，直接返回
        if (total.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        // 计算被修改的比例占总比例的比例
        BigDecimal remainingProportion = BigDecimal.ONE.subtract(modifiedRatio);

        // 计算剩余的总比例
        BigDecimal remainingTotal = total;

        // 调整其他比例
        for (SupplierPurchaseRatioDTO supplierPurchaseRatioDTO : dtoList) {
            if (null != modifiedSupplierPurchaseRatioDTO && modifiedSupplierPurchaseRatioDTO.getId().equals(supplierPurchaseRatioDTO.getId())){
                continue;
            }
            BigDecimal newRatio = supplierPurchaseRatioDTO.getPurchaseRatio()
                    .multiply(remainingProportion)
                    .divide(remainingTotal, 4, RoundingMode.HALF_UP);
            supplierPurchaseRatioDTO.setPurchaseRatio(newRatio);
        }

        // 确保所有比例的总和为1
        BigDecimal recalculatedTotal = dtoList.stream()
                .map(SupplierPurchaseRatioDTO::getPurchaseRatio)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 如果总和不为1，进行微调
        if (recalculatedTotal.compareTo(BigDecimal.ONE) != 0) {
            BigDecimal difference = BigDecimal.ONE.subtract(recalculatedTotal);
            if (null != modifiedSupplierPurchaseRatioDTO){
                modifiedSupplierPurchaseRatioDTO.setPurchaseRatio(modifiedSupplierPurchaseRatioDTO.getPurchaseRatio().add(difference));
            }else {
                dtoList.get(0).setPurchaseRatio(dtoList.get(0).getPurchaseRatio().add(difference));
            }
        }
    }

    @Override
    public List<SupplierPurchaseRatioVO> selectVOByParams(Map<String, Object> params) {
        return supplierPurchaseRatioDao.selectVOByParams(params);
    }
}
