package com.yhl.scp.mrp.material.arrival.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.material.arrival.convertor.MaterialArrivalFeedbackRecordConvertor;
import com.yhl.scp.mrp.material.arrival.domain.entity.MaterialArrivalFeedbackRecordDO;
import com.yhl.scp.mrp.material.arrival.domain.service.MaterialArrivalFeedbackRecordDomainService;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalFeedbackRecordDTO;
import com.yhl.scp.mrp.material.arrival.infrastructure.dao.MaterialArrivalFeedbackRecordDao;
import com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalFeedbackRecordPO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalFeedbackRecordService;
import com.yhl.scp.mrp.material.arrival.vo.MaterialArrivalFeedbackRecordVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MaterialArrivalFeedbackRecordServiceImpl</code>
 * <p>
 * 材料到货反馈记录应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-30 11:30:38
 */
@Slf4j
@Service
public class MaterialArrivalFeedbackRecordServiceImpl extends AbstractService implements MaterialArrivalFeedbackRecordService {

    @Resource
    private MaterialArrivalFeedbackRecordDao materialArrivalFeedbackRecordDao;

    @Resource
    private MaterialArrivalFeedbackRecordDomainService materialArrivalFeedbackRecordDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(MaterialArrivalFeedbackRecordDTO materialArrivalFeedbackRecordDTO) {
        // 0.数据转换
        MaterialArrivalFeedbackRecordDO materialArrivalFeedbackRecordDO = MaterialArrivalFeedbackRecordConvertor.INSTANCE.dto2Do(materialArrivalFeedbackRecordDTO);
        MaterialArrivalFeedbackRecordPO materialArrivalFeedbackRecordPO = MaterialArrivalFeedbackRecordConvertor.INSTANCE.dto2Po(materialArrivalFeedbackRecordDTO);
        // 1.数据校验
        materialArrivalFeedbackRecordDomainService.validation(materialArrivalFeedbackRecordDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialArrivalFeedbackRecordPO);
        materialArrivalFeedbackRecordDao.insert(materialArrivalFeedbackRecordPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MaterialArrivalFeedbackRecordDTO materialArrivalFeedbackRecordDTO) {
        // 0.数据转换
        MaterialArrivalFeedbackRecordDO materialArrivalFeedbackRecordDO = MaterialArrivalFeedbackRecordConvertor.INSTANCE.dto2Do(materialArrivalFeedbackRecordDTO);
        MaterialArrivalFeedbackRecordPO materialArrivalFeedbackRecordPO = MaterialArrivalFeedbackRecordConvertor.INSTANCE.dto2Po(materialArrivalFeedbackRecordDTO);
        // 1.数据校验
        materialArrivalFeedbackRecordDomainService.validation(materialArrivalFeedbackRecordDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialArrivalFeedbackRecordPO);
        materialArrivalFeedbackRecordDao.update(materialArrivalFeedbackRecordPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialArrivalFeedbackRecordDTO> list) {
        List<MaterialArrivalFeedbackRecordPO> newList = MaterialArrivalFeedbackRecordConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialArrivalFeedbackRecordDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialArrivalFeedbackRecordDTO> list) {
        List<MaterialArrivalFeedbackRecordPO> newList = MaterialArrivalFeedbackRecordConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialArrivalFeedbackRecordDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialArrivalFeedbackRecordDao.deleteBatch(idList);
        }
        return materialArrivalFeedbackRecordDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialArrivalFeedbackRecordVO selectByPrimaryKey(String id) {
        MaterialArrivalFeedbackRecordPO po = materialArrivalFeedbackRecordDao.selectByPrimaryKey(id);
        return MaterialArrivalFeedbackRecordConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MATERIAL_ARRIVAL_FEEDBACK_RECORD")
    public List<MaterialArrivalFeedbackRecordVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MATERIAL_ARRIVAL_FEEDBACK_RECORD")
    public List<MaterialArrivalFeedbackRecordVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialArrivalFeedbackRecordVO> dataList = materialArrivalFeedbackRecordDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialArrivalFeedbackRecordServiceImpl target = springBeanUtils.getBean(MaterialArrivalFeedbackRecordServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialArrivalFeedbackRecordVO> selectByParams(Map<String, Object> params) {
        List<MaterialArrivalFeedbackRecordPO> list = materialArrivalFeedbackRecordDao.selectByParams(params);
        return MaterialArrivalFeedbackRecordConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialArrivalFeedbackRecordVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public void receiveArrivalData(List<MaterialArrivalFeedbackRecordVO> dataList) {

    }

    @Override
    public String getObjectType() {
      //return ObjectTypeEnum.MATERIAL_ARRIVAL_FEEDBACK_RECORD.getCode();
        return null;
    }

    @Override
    public List<MaterialArrivalFeedbackRecordVO> invocation(List<MaterialArrivalFeedbackRecordVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

}
