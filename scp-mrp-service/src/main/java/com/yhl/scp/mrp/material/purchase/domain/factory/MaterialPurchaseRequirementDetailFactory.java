package com.yhl.scp.mrp.material.purchase.domain.factory;

import com.yhl.scp.mrp.material.purchase.domain.entity.MaterialPurchaseRequirementDetailDO;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseRequirementDetailDTO;
import com.yhl.scp.mrp.material.purchase.infrastructure.dao.MaterialPurchaseRequirementDetailDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>MaterialPurchaseRequirementDetailFactory</code>
 * <p>
 * 材料采购需求明细领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-11 19:01:02
 */
@Component
public class MaterialPurchaseRequirementDetailFactory {

    @Resource
    private MaterialPurchaseRequirementDetailDao materialPurchaseRequirementDetailDao;

    MaterialPurchaseRequirementDetailDO create(MaterialPurchaseRequirementDetailDTO dto) {
        // TODO
        return null;
    }

}
