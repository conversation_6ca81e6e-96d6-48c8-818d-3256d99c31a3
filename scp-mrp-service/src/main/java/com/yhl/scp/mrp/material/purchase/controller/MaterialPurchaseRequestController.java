package com.yhl.scp.mrp.material.purchase.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseRequestDTO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseRequestService;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseRequestVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MaterialPurchaseRequestController</code>
 * <p>
 * ERP材料采购申请单控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-02 10:19:36
 */
@Slf4j
@Api(tags = "ERP材料采购申请单控制器")
@RestController
@RequestMapping("materialPurchaseRequest")
public class MaterialPurchaseRequestController extends BaseController {

    @Resource
    private MaterialPurchaseRequestService materialPurchaseRequestService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<MaterialPurchaseRequestVO>> page() {
        List<MaterialPurchaseRequestVO> materialPurchaseRequestList = materialPurchaseRequestService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialPurchaseRequestVO> pageInfo = new PageInfo<>(materialPurchaseRequestList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MaterialPurchaseRequestDTO materialPurchaseRequestDTO) {
        return materialPurchaseRequestService.doCreate(materialPurchaseRequestDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MaterialPurchaseRequestDTO materialPurchaseRequestDTO) {
        return materialPurchaseRequestService.doUpdate(materialPurchaseRequestDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        materialPurchaseRequestService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<MaterialPurchaseRequestVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialPurchaseRequestService.selectByPrimaryKey(id));
    }
    @ApiOperation(value = "同步")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncMaterialPurchaseRequest() {
        return materialPurchaseRequestService.syncMaterialPurchaseRequest(SystemHolder.getTenantCode());
    }

}
