package com.yhl.scp.mrp.report.glassInventoryDynamic.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.report.glassInventoryDynamic.convertor.GlassInventoryDynamicDataReportConvertor;
import com.yhl.scp.mrp.report.glassInventoryDynamic.domain.entity.GlassInventoryDynamicDataReportDO;
import com.yhl.scp.mrp.report.glassInventoryDynamic.domain.service.GlassInventoryDynamicDataReportDomainService;
import com.yhl.scp.mrp.report.glassInventoryDynamic.dto.GlassInventoryDynamicDataReportDTO;
import com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.dao.GlassInventoryDynamicDataReportDao;
import com.yhl.scp.mrp.report.glassInventoryDynamic.infrastructure.po.GlassInventoryDynamicDataReportPO;
import com.yhl.scp.mrp.report.glassInventoryDynamic.service.GlassInventoryDynamicDataReportService;
import com.yhl.scp.mrp.report.glassInventoryDynamic.service.GlassInventoryDynamicVersionReportService;
import com.yhl.scp.mrp.report.glassInventoryDynamic.vo.GlassInventoryDynamicDataReportVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.report.glassInventoryDynamic.vo.GlassInventoryDynamicSummaryReportVO;
import com.yhl.scp.mrp.report.glassInventoryDynamic.vo.GlassInventoryDynamicVersionReportVO;
import com.yhl.scp.mrp.utils.EasyExcelUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>GlassInventoryDynamicDataReportServiceImpl</code>
 * <p>
 * 原片库存动态表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-12 22:05:00
 */
@Slf4j
@Service
public class GlassInventoryDynamicDataReportServiceImpl extends AbstractService implements GlassInventoryDynamicDataReportService {

    @Resource
    private GlassInventoryDynamicDataReportDao glassInventoryDynamicDataReportDao;

    @Resource
    private GlassInventoryDynamicDataReportDomainService glassInventoryDynamicDataReportDomainService;

    @Resource
    private GlassInventoryDynamicVersionReportService glassInventoryDynamicVersionReportService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(GlassInventoryDynamicDataReportDTO glassInventoryDynamicDataReportDTO) {
        // 0.数据转换
        GlassInventoryDynamicDataReportDO glassInventoryDynamicDataReportDO = GlassInventoryDynamicDataReportConvertor.INSTANCE.dto2Do(glassInventoryDynamicDataReportDTO);
        GlassInventoryDynamicDataReportPO glassInventoryDynamicDataReportPO = GlassInventoryDynamicDataReportConvertor.INSTANCE.dto2Po(glassInventoryDynamicDataReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        glassInventoryDynamicDataReportDomainService.validation(glassInventoryDynamicDataReportDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(glassInventoryDynamicDataReportPO);
        glassInventoryDynamicDataReportDao.insert(glassInventoryDynamicDataReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(GlassInventoryDynamicDataReportDTO glassInventoryDynamicDataReportDTO) {
        // 0.数据转换
        GlassInventoryDynamicDataReportDO glassInventoryDynamicDataReportDO = GlassInventoryDynamicDataReportConvertor.INSTANCE.dto2Do(glassInventoryDynamicDataReportDTO);
        GlassInventoryDynamicDataReportPO glassInventoryDynamicDataReportPO = GlassInventoryDynamicDataReportConvertor.INSTANCE.dto2Po(glassInventoryDynamicDataReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        glassInventoryDynamicDataReportDomainService.validation(glassInventoryDynamicDataReportDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(glassInventoryDynamicDataReportPO);
        glassInventoryDynamicDataReportDao.update(glassInventoryDynamicDataReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<GlassInventoryDynamicDataReportDTO> list) {
        List<GlassInventoryDynamicDataReportPO> newList = GlassInventoryDynamicDataReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        glassInventoryDynamicDataReportDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<GlassInventoryDynamicDataReportDTO> list) {
        List<GlassInventoryDynamicDataReportPO> newList = GlassInventoryDynamicDataReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        glassInventoryDynamicDataReportDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return glassInventoryDynamicDataReportDao.deleteBatch(idList);
        }
        return glassInventoryDynamicDataReportDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public GlassInventoryDynamicDataReportVO selectByPrimaryKey(String id) {
        GlassInventoryDynamicDataReportPO po = glassInventoryDynamicDataReportDao.selectByPrimaryKey(id);
        return GlassInventoryDynamicDataReportConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "GLASS_INVENTORY_DYNAMIC_DATA_REPORT")
    public List<GlassInventoryDynamicDataReportVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "GLASS_INVENTORY_DYNAMIC_DATA_REPORT")
    public List<GlassInventoryDynamicDataReportVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<GlassInventoryDynamicDataReportVO> dataList = glassInventoryDynamicDataReportDao.selectByCondition(sortParam, queryCriteriaParam);
        GlassInventoryDynamicDataReportServiceImpl target = SpringBeanUtils.getBean(GlassInventoryDynamicDataReportServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<GlassInventoryDynamicDataReportVO> selectByParams(Map<String, Object> params) {
        List<GlassInventoryDynamicDataReportPO> list = glassInventoryDynamicDataReportDao.selectByParams(params);
        return GlassInventoryDynamicDataReportConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<GlassInventoryDynamicDataReportVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<GlassInventoryDynamicDataReportVO> invocation(List<GlassInventoryDynamicDataReportVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public GlassInventoryDynamicSummaryReportVO queryReportData(String versionId) {
        GlassInventoryDynamicVersionReportVO glassInventoryDynamicVersionReportVO;
        if (StringUtils.isNotBlank(versionId)){
            glassInventoryDynamicVersionReportVO = glassInventoryDynamicVersionReportService.selectByPrimaryKey(versionId);
            if (null == glassInventoryDynamicVersionReportVO){
                return null;
            }
        }else {
            // 查询最新版本
            List<GlassInventoryDynamicVersionReportVO> glassInventoryDynamicVersionReportVOS = glassInventoryDynamicVersionReportService.selectAll();
            if (CollectionUtils.isEmpty(glassInventoryDynamicVersionReportVOS)){
                return null;
            }
            glassInventoryDynamicVersionReportVOS = glassInventoryDynamicVersionReportVOS.stream()
                    .sorted(Comparator.comparing(GlassInventoryDynamicVersionReportVO::getCreateTime).reversed())
                    .collect(Collectors.toList());
            glassInventoryDynamicVersionReportVO = glassInventoryDynamicVersionReportVOS.get(0);
        }

        // 查询报表data数据
        List<GlassInventoryDynamicDataReportVO> reportVoDataList = this.selectVOByParams(ImmutableMap.of("versionId", glassInventoryDynamicVersionReportVO.getId()));

        GlassInventoryDynamicSummaryReportVO glassInventoryDynamicSummaryReportVO = new GlassInventoryDynamicSummaryReportVO();
        glassInventoryDynamicSummaryReportVO.setGlassNextOneMonthGrowthRate(glassInventoryDynamicVersionReportVO.getGlassNextOneMonthGrowthRate());
        glassInventoryDynamicSummaryReportVO.setGlassNextTwoMonthGrowthRate(glassInventoryDynamicVersionReportVO.getGlassNextTwoMonthGrowthRate());
        glassInventoryDynamicSummaryReportVO.setGlassNextThreeMonthGrowthRate(glassInventoryDynamicVersionReportVO.getGlassNextThreeMonthGrowthRate());
        glassInventoryDynamicSummaryReportVO.setMainOperationOutputCurrentMonth(glassInventoryDynamicVersionReportVO.getMainOperationOutputCurrentMonth());
        glassInventoryDynamicSummaryReportVO.setMainOperationOutputNextOneMonth(glassInventoryDynamicVersionReportVO.getMainOperationOutputNextOneMonth());
        glassInventoryDynamicSummaryReportVO.setMainOperationOutputNextTwoMonth(glassInventoryDynamicVersionReportVO.getMainOperationOutputNextTwoMonth());
        glassInventoryDynamicSummaryReportVO.setMainOperationOutputNextThreeMonth(glassInventoryDynamicVersionReportVO.getMainOperationOutputNextThreeMonth());
        glassInventoryDynamicSummaryReportVO.setPlanNextOneMonthGrowthRate(glassInventoryDynamicVersionReportVO.getPlanNextOneMonthGrowthRate());
        glassInventoryDynamicSummaryReportVO.setPlanNextTwoMonthGrowthRate(glassInventoryDynamicVersionReportVO.getPlanNextTwoMonthGrowthRate());
        glassInventoryDynamicSummaryReportVO.setPlanNextThreeMonthGrowthRate(glassInventoryDynamicVersionReportVO.getPlanNextThreeMonthGrowthRate());

        // reportVoDataList 按照分组标识小计
        Map<String, List<GlassInventoryDynamicDataReportVO>> dataReportVOMap = reportVoDataList.stream()
                .collect(Collectors.groupingBy(GlassInventoryDynamicDataReportVO::getGroupFlag));

        List<GlassInventoryDynamicDataReportVO> dataList = new ArrayList<>();
        List<GlassInventoryDynamicDataReportVO> minTotalDataList = new ArrayList<>();
        for (Map.Entry<String, List<GlassInventoryDynamicDataReportVO>> entry : dataReportVOMap.entrySet()) {
            List<GlassInventoryDynamicDataReportVO> valueList = entry.getValue();
            valueList = valueList.stream().sorted(Comparator.comparing(GlassInventoryDynamicDataReportVO::getShowSequence)).collect(Collectors.toList());
            dataList.addAll(valueList);
            // 求和数据项
            GlassInventoryDynamicDataReportVO totalDataReportVO = getTotalDataReportVO(valueList);
            totalDataReportVO.setProductColor("小计");
            dataList.add(totalDataReportVO);
            minTotalDataList.add(totalDataReportVO);
        }
        GlassInventoryDynamicDataReportVO mainTotalDataReportVO = getTotalDataReportVO(minTotalDataList);
        mainTotalDataReportVO.setProductColor("合计");
        dataList.add(mainTotalDataReportVO);
        glassInventoryDynamicSummaryReportVO.setList(dataList);
        return glassInventoryDynamicSummaryReportVO;
    }

    private GlassInventoryDynamicDataReportVO getTotalDataReportVO(List<GlassInventoryDynamicDataReportVO> dataList) {
        GlassInventoryDynamicDataReportVO glassInventoryDynamicDataReportVO = new GlassInventoryDynamicDataReportVO();

        // 使用方法引用和 Stream 的 reduce 方法进行求和
        glassInventoryDynamicDataReportVO.setLocalInventoryArea(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getLocalInventoryArea));
        glassInventoryDynamicDataReportVO.setLocalInventoryWeight(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getLocalInventoryWeight));
        glassInventoryDynamicDataReportVO.setTransitInventoryArea(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getTransitInventoryArea));
        glassInventoryDynamicDataReportVO.setTransitInventoryWeight(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getTransitInventoryWeight));
        glassInventoryDynamicDataReportVO.setInternalFloatGlassInventoryArea(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getInternalFloatGlassInventoryArea));
        glassInventoryDynamicDataReportVO.setInternalFloatGlassInventoryWeight(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getInternalFloatGlassInventoryWeight));
        glassInventoryDynamicDataReportVO.setExternalFloatGlassInventoryArea(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getExternalFloatGlassInventoryArea));
        glassInventoryDynamicDataReportVO.setExternalFloatGlassInventoryWeight(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getExternalFloatGlassInventoryWeight));
        glassInventoryDynamicDataReportVO.setFloatGlassInventoryArea(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getFloatGlassInventoryArea));
        glassInventoryDynamicDataReportVO.setFloatGlassInventoryWeight(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getFloatGlassInventoryWeight));
        glassInventoryDynamicDataReportVO.setBacklogInventoryArea(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getBacklogInventoryArea));
        glassInventoryDynamicDataReportVO.setBacklogInventoryWeight(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getBacklogInventoryWeight));
        glassInventoryDynamicDataReportVO.setTotalInventoryArea(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getTotalInventoryArea));
        glassInventoryDynamicDataReportVO.setTotalInventoryWeight(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getTotalInventoryWeight));
        glassInventoryDynamicDataReportVO.setEffectiveTotalInventoryArea(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getEffectiveTotalInventoryArea));
        glassInventoryDynamicDataReportVO.setEffectiveTotalInventoryWeight(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getEffectiveTotalInventoryWeight));
        glassInventoryDynamicDataReportVO.setCurrentMonthConsumeInventoryArea(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getCurrentMonthConsumeInventoryArea));
        glassInventoryDynamicDataReportVO.setCurrentMonthConsumeInventoryWeight(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getCurrentMonthConsumeInventoryWeight));
        glassInventoryDynamicDataReportVO.setNextOneMonthConsumeInventoryArea(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getNextOneMonthConsumeInventoryArea));
        glassInventoryDynamicDataReportVO.setNextOneMonthConsumeInventoryWeight(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getNextOneMonthConsumeInventoryWeight));
        glassInventoryDynamicDataReportVO.setNextTwoMonthConsumeInventoryArea(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getNextTwoMonthConsumeInventoryArea));
        glassInventoryDynamicDataReportVO.setNextTwoMonthConsumeInventoryWeight(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getNextTwoMonthConsumeInventoryWeight));
        glassInventoryDynamicDataReportVO.setNextThreeMonthConsumeInventoryArea(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getNextThreeMonthConsumeInventoryArea));
        glassInventoryDynamicDataReportVO.setNextThreeMonthConsumeInventoryWeight(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getNextThreeMonthConsumeInventoryWeight));
        glassInventoryDynamicDataReportVO.setInventoryDays(sumBigDecimals(dataList, GlassInventoryDynamicDataReportVO::getInventoryDays));
        return glassInventoryDynamicDataReportVO;
    }

    private BigDecimal sumBigDecimals(List<GlassInventoryDynamicDataReportVO> dataList, java.util.function.Function<GlassInventoryDynamicDataReportVO, BigDecimal> mapper) {
        return dataList.stream()
                .map(mapper)
                .filter(Objects::nonNull) // 过滤掉null值
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public List<GlassInventoryDynamicDataReportVO> selectVOByParams(Map<String, Object> params) {
        return glassInventoryDynamicDataReportDao.selectVOByParams(params);
    }

    @Override
    @SneakyThrows
    public void exportData(HttpServletResponse response) {
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "原片库存动态报表导出");
        // 创建工作簿和工作表
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");

        // 创建标题行
        Row headerRow1 = sheet.createRow(0);
        String[] headers1 = {
                "颜色", "厚度", "本地库存", "", "在途库存", "", "供应商/内部浮法", "",
                "供应商/外部浮法", "", "在供应商合计", "", "积压库存", "", "总库存", "",
                "有效总库存量", "", "当月消耗", "", "后三个月滚动预测", "", "", "", "", "", "库存天数"
        };
        createHeaderRow(workbook, headerRow1, headers1);

        // 合并标题单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));  // 颜色
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1));  // 厚度
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 2, 3));  // 本地库存
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 4, 5));  // 在途库存
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 6, 7));  // 供应商/内部浮法
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 8, 9));  // 供应商/外部浮法
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 10, 11)); // 在供应商合计
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 12, 13)); // 积压库存
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 14, 15)); // 总库存
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 16, 17)); // 有效总库存量
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 18, 19)); // 当月消耗
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 20, 25)); // 后三个月滚动预测
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 26, 26)); // 库存天数

        // 创建子标题行
        Row headerRow2 = sheet.createRow(1);
        String[] headers2 = {
                "", "", "面积", "吨位", "面积", "吨位", "面积", "吨",
                "面积", "吨", "面积", "吨位", "面积", "吨位", "面积", "吨位",
                "面积", "吨位", "5月", "吨位", "6月", "吨位", "7月", "吨位", "8月", "吨位", ""
        };
        createHeaderRow(workbook, headerRow2, headers2);
        // 创建黄色行样式
        CellStyle yellowStyle = createYellowRowStyle(workbook);


        // 查询最新版本
        List<GlassInventoryDynamicVersionReportVO> glassInventoryDynamicVersionReportVOS = glassInventoryDynamicVersionReportService.selectAll();
        if (CollectionUtils.isEmpty(glassInventoryDynamicVersionReportVOS)){
            return;
        }
        glassInventoryDynamicVersionReportVOS = glassInventoryDynamicVersionReportVOS.stream()
                .sorted(Comparator.comparing(GlassInventoryDynamicVersionReportVO::getCreateTime).reversed())
                .collect(Collectors.toList());
        GlassInventoryDynamicVersionReportVO glassInventoryDynamicVersionReportVO = glassInventoryDynamicVersionReportVOS.get(0);

        // 查询数据
        GlassInventoryDynamicSummaryReportVO glassInventoryDynamicSummaryReportVO = this.queryReportData(glassInventoryDynamicVersionReportVO.getId());
        List<GlassInventoryDynamicDataReportVO> list = glassInventoryDynamicSummaryReportVO.getList();

        List<Object[]> dataRows = new ArrayList<>();
        for (GlassInventoryDynamicDataReportVO dataReportVO : list) {
            Object[] objects = {
                    dataReportVO.getProductColor(),
                    dataReportVO.getProductThickness(),
                    dataReportVO.getLocalInventoryArea(),
                    dataReportVO.getLocalInventoryWeight(),
                    dataReportVO.getTransitInventoryArea(),
                    dataReportVO.getTransitInventoryWeight(),
                    dataReportVO.getInternalFloatGlassInventoryArea(),
                    dataReportVO.getInternalFloatGlassInventoryWeight(),
                    dataReportVO.getExternalFloatGlassInventoryArea(),
                    dataReportVO.getExternalFloatGlassInventoryWeight(),
                    dataReportVO.getFloatGlassInventoryArea(),
                    dataReportVO.getFloatGlassInventoryWeight(),
                    dataReportVO.getBacklogInventoryArea(),
                    dataReportVO.getBacklogInventoryWeight(),
                    dataReportVO.getTotalInventoryArea(),
                    dataReportVO.getTotalInventoryWeight(),
                    dataReportVO.getEffectiveTotalInventoryArea(),
                    dataReportVO.getExternalFloatGlassInventoryWeight(),
                    dataReportVO.getCurrentMonthConsumeInventoryArea(),
                    dataReportVO.getCurrentMonthConsumeInventoryWeight(),
                    dataReportVO.getNextOneMonthConsumeInventoryArea(),
                    dataReportVO.getNextOneMonthConsumeInventoryWeight(),
                    dataReportVO.getNextTwoMonthConsumeInventoryArea(),
                    dataReportVO.getNextTwoMonthConsumeInventoryWeight(),
                    dataReportVO.getNextThreeMonthConsumeInventoryArea(),
                    dataReportVO.getNextThreeMonthConsumeInventoryWeight(),
                    dataReportVO.getInventoryDays()};
            dataRows.add(objects);
        }

        // 填充数据行
        int rowNum = 2;
        // 在填充数据行时应用样式
        for (Object[] rowData : dataRows) {
            Row row = sheet.createRow(rowNum++);
            for (int i = 0; i < rowData.length; i++) {
                Cell cell = row.createCell(i);
                setCellValueWithBigDecimal(cell, rowData[i]);

                // 检查是否为小计或合计行
                if (i == 0 && (rowData[0].equals("小计") || rowData[0].equals("合计"))) {
                    // 应用黄色样式
                    cell.setCellStyle(yellowStyle);
                } else if (i > 0 && (rowData[0].equals("小计") || rowData[0].equals("合计"))) {
                    // 为同一行的其他单元格也应用黄色样式
                    cell.setCellStyle(yellowStyle);
                }
            }
        }

        // 设置列宽自适应
        for (int i = 0; i < headers1.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // 5. 创建KPI报表（114-118行）
        createKpiReport(sheet, glassInventoryDynamicVersionReportVO);

        // 6. 写入文件
        workbook.write(out);
        workbook.close();
    }

    private static CellStyle createYellowRowStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置数字格式（保留小数位）
        DataFormat format = workbook.createDataFormat();
        style.setDataFormat(format.getFormat("#,##0.0000"));

        return style;
    }

    private static void setCellValueWithBigDecimal(Cell cell, Object value) {
        if (value == null) {
            return;
        }
        if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof BigDecimal) {
            // 将BigDecimal转换为double
            cell.setCellValue(((BigDecimal) value).doubleValue());

            // 可选：设置数字格式（保留小数位）
            CellStyle style = cell.getSheet().getWorkbook().createCellStyle();
            style.setDataFormat(cell.getSheet().getWorkbook().createDataFormat().getFormat("#,##0.0000"));
            cell.setCellStyle(style);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Float) {
            cell.setCellValue((Float) value);
        }
    }

    private static void createHeaderRow(Workbook workbook, Row row, String[] headers) {
        CellStyle headerStyle = createHeaderStyle(workbook);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }


    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置字体
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置居中对齐
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置背景色
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        return style;
    }

    private static void setCellValue(Cell cell, Object value) {
        if (value == null) {
            return;
        }
        if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        }
    }



    private static Map<String, CellStyle> createStyles(Workbook workbook) {
        Map<String, CellStyle> styles = new HashMap<>();

        // 标题样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        styles.put("header", headerStyle);

        // 数据样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.RIGHT);
        dataStyle.setDataFormat(workbook.createDataFormat().getFormat("#,##0.000"));
        styles.put("data", dataStyle);

        // 小计样式
        CellStyle totalStyle = workbook.createCellStyle();
        totalStyle.setAlignment(HorizontalAlignment.RIGHT);
        totalStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        totalStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        totalStyle.setFont(headerFont);
        styles.put("total", totalStyle);

        return styles;
    }

    private static void createHeaders(Sheet sheet, Map<String, CellStyle> styles) {
        // 第一行标题
        Row row1 = sheet.createRow(0);
        String[] headers1 = {"颜色", "厚度", "本地库存", "", "在途库存", "", "供应商/内部浮法", "",
                "供应商/外部浮法", "", "在供应商合计", "", "积压库存", "", "总库存", "",
                "有效总库存量", "", "当月消耗", "", "后三个月滚动预测", "", "", "", "", "", "库存天数"};
        for (int i = 0; i < headers1.length; i++) {
            Cell cell = row1.createCell(i);
            cell.setCellValue(headers1[i]);
            cell.setCellStyle(styles.get("header"));
        }

        // 第二行标题
        Row row2 = sheet.createRow(1);
        String[] headers2 = {"", "", "面积", "吨位", "面积", "吨位", "面积", "吨",
                "面积", "吨", "面积", "吨位", "面积", "吨位", "面积", "吨位",
                "面积", "吨位", "5月", "吨位", "6月", "吨位", "7月", "吨位", "8月", "吨位", ""};
        for (int i = 0; i < headers2.length; i++) {
            Cell cell = row2.createCell(i);
            cell.setCellValue(headers2[i]);
            cell.setCellStyle(styles.get("header"));
        }
    }

    private static void fillDataRows(Sheet sheet, Map<String, CellStyle> styles) {
        // 示例数据填充（实际项目中应从数据库读取）
        Object[][] data = {
                // 颜色, 厚度, 本地库存面积, 吨位, 在途库存面积, 吨位, ... 库存天数公式
                {"C/白玻", 1.6, 220.5736, 0.8822944, 0, 0, null, 0, null, 0, 0, 0, null, 0, 220.5736, 0.8822944, 220.5736, 0.8822944, 0, 0, 34.255319, 0.137021, 34.255319, 0.137021, 2222},
                // 其他数据行...
                {"小计", "", 231423.3899, 1304.9106, 133065.13, 651.2428, 603908.067, 3055.6724, 61355.1097, 550.5848, 665263.177, 3606.2571, 0, 0, 1029751.697, 5562.4106, 1029751.697, 5562.4106, 476894.888, 2659.6249, 467596.466, 2621.0214, 490844.814, 2740.0363, 2222},
                // 其他颜色数据...
                {"合计", "", 231423.3899, 1304.9106, 133065.13, 651.2428, 603908.067, 3055.6724, 61355.1097, 550.5848, 665263.177, 3606.2571, 0, 0, 1029751.697, 5562.4106, 1029751.697, 5562.4106, 476894.888, 2659.6249, 467596.466, 2621.0214, 490844.814, 2740.0363, 2222}
        };

        for (int i = 0; i < data.length; i++) {
            Row row = sheet.createRow(i + 2); // 从第3行开始
            for (int j = 0; j < data[i].length; j++) {
                Cell cell = row.createCell(j);
                Object value = data[i][j];

                if (value instanceof Number) {
                    cell.setCellValue(((Number) value).doubleValue());
                    cell.setCellStyle(styles.get("data"));
                } else if (value instanceof String) {
                    String strVal = (String) value;
                    if (strVal.startsWith("=")) {
                        cell.setCellFormula(strVal.substring(1));
                    } else {
                        cell.setCellValue(strVal);
                    }
                    // 设置小计/合计的特殊样式
                    if ("小计".equals(strVal) || "合计".equals(strVal)) {
                        cell.setCellStyle(styles.get("total"));
                    }
                }
            }
        }
    }

    private static void createKpiReport(Sheet sheet, GlassInventoryDynamicVersionReportVO glassInventoryDynamicVersionReportVO) {
        // 第114行（实际行号113）
        Row row114 = sheet.createRow(113);
        row114.createCell(16).setCellValue("原片增幅");
        row114.createCell(20).setCellFormula(glassInventoryDynamicVersionReportVO.getGlassNextOneMonthGrowthRate());
        row114.createCell(22).setCellFormula(glassInventoryDynamicVersionReportVO.getGlassNextTwoMonthGrowthRate());
        row114.createCell(24).setCellFormula(glassInventoryDynamicVersionReportVO.getGlassNextThreeMonthGrowthRate());

        // 第116行
        Row row116 = sheet.createRow(115);
        row116.createCell(16).setCellValue("主工序产量");
        row116.createCell(18).setCellValue(null != glassInventoryDynamicVersionReportVO.getMainOperationOutputCurrentMonth() ?
                glassInventoryDynamicVersionReportVO.getMainOperationOutputCurrentMonth().toString() : null);
        row116.createCell(20).setCellValue(null != glassInventoryDynamicVersionReportVO.getMainOperationOutputNextOneMonth() ?
                glassInventoryDynamicVersionReportVO.getMainOperationOutputNextOneMonth().toString() : null);
        row116.createCell(22).setCellValue(null != glassInventoryDynamicVersionReportVO.getMainOperationOutputNextTwoMonth() ?
                glassInventoryDynamicVersionReportVO.getMainOperationOutputNextTwoMonth().toString() : null);
        row116.createCell(24).setCellValue(null != glassInventoryDynamicVersionReportVO.getMainOperationOutputNextThreeMonth() ?
                glassInventoryDynamicVersionReportVO.getMainOperationOutputNextThreeMonth().toString() : null);

        // 第118行
        Row row118 = sheet.createRow(117);
        row118.createCell(16).setCellValue("计划增幅");
        row118.createCell(20).setCellFormula(glassInventoryDynamicVersionReportVO.getPlanNextOneMonthGrowthRate());
        row118.createCell(22).setCellFormula(glassInventoryDynamicVersionReportVO.getPlanNextTwoMonthGrowthRate());
        row118.createCell(24).setCellFormula(glassInventoryDynamicVersionReportVO.getPlanNextThreeMonthGrowthRate());
    }


}