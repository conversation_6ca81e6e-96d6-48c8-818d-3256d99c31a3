package com.yhl.scp.mrp.inventory.infrastructure.po;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BasePO;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>InventoryOurFactoryDetailPO</code>
 * <p>
 * 原片本厂库存批次明细PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 20:18:56
 */
public class InventoryOurFactoryDetailPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 553706658757855523L;

    /**
     * 主键id
     */
    private String id;
    /**
     * 物品id
     */
    private String productId;
    /**
     * 组织
     */
    private String organization;
    /**
     * 子库存
     */
    private String subInventory;
    /**
     * 货位
     */
    private String location;
    /**
     * 原片编码
     */
    private String productCode;
    /**
     * 原片规格
     */
    private String productSpec;
    /**
     * 库存点编码
     */
    private String stockPointCode;
    /**
     * 等级
     */
    private String level;
    /**
     * 批次等级代码
     */
    private String lotLevelCode;
    /**
     * 批次号
     */
    private String lotNumber;
    /**
     * 片/箱
     */
    private BigDecimal perBox;
    /**
     * 吨/箱
     */
    private BigDecimal weightBox;
    /**
     * 客户
     */
    private String customer;
    /**
     * 现存件数
     */
    private BigDecimal currentQuantity;
    /**
     * 吨数
     */
    private BigDecimal tonnage;
    /**
     * 入库时间
     */
    private Date storageTime;
    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;
    /**
     * 库龄天数
     */
    private Integer ageDays;
    /**
     * 距离失效天数
     */
    private Integer daysToExpiration;
    /**
     * 切裁率
     */
    private BigDecimal cuttingRate;
    /**
     * 超期天数
     */
    private Integer overdueDays;
    /**
     * 计划使用量
     */
    private BigDecimal planQuantity;
    /**
     * 替代使用量
     */
    private BigDecimal substituteQuantity;
    /**
     * 版本
     */
    private Integer versionValue;
    /**
     * 分配状态
     */
    private String allocationStatus;
    /**
     * 供应id
     */
    private String supplyId;
    /**
     * 超期时间
     */
    private Date overdueTime;
    /**
     * 是否生效（生效/失效）
     */
    private String enabled;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 修改时间
     */
    private Date modifyTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public String getSubInventory() {
        return subInventory;
    }

    public void setSubInventory(String subInventory) {
        this.subInventory = subInventory;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductSpec() {
        return productSpec;
    }

    public void setProductSpec(String productSpec) {
        this.productSpec = productSpec;
    }

    public String getStockPointCode() {
        return stockPointCode;
    }

    public void setStockPointCode(String stockPointCode) {
        this.stockPointCode = stockPointCode;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getLotLevelCode() {
        return lotLevelCode;
    }

    public void setLotLevelCode(String lotLevelCode) {
        this.lotLevelCode = lotLevelCode;
    }

    public String getLotNumber() {
        return lotNumber;
    }

    public void setLotNumber(String lotNumber) {
        this.lotNumber = lotNumber;
    }

    public BigDecimal getPerBox() {
        return perBox;
    }

    public void setPerBox(BigDecimal perBox) {
        this.perBox = perBox;
    }

    public BigDecimal getWeightBox() {
        return weightBox;
    }

    public void setWeightBox(BigDecimal weightBox) {
        this.weightBox = weightBox;
    }

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public BigDecimal getCurrentQuantity() {
        return currentQuantity;
    }

    public void setCurrentQuantity(BigDecimal currentQuantity) {
        this.currentQuantity = currentQuantity;
    }

    public BigDecimal getTonnage() {
        return tonnage;
    }

    public void setTonnage(BigDecimal tonnage) {
        this.tonnage = tonnage;
    }

    public Date getStorageTime() {
        return storageTime;
    }

    public void setStorageTime(Date storageTime) {
        this.storageTime = storageTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getAgeDays() {
        return ageDays;
    }

    public void setAgeDays(Integer ageDays) {
        this.ageDays = ageDays;
    }

    public Integer getDaysToExpiration() {
        return daysToExpiration;
    }

    public void setDaysToExpiration(Integer daysToExpiration) {
        this.daysToExpiration = daysToExpiration;
    }

    public BigDecimal getCuttingRate() {
        return cuttingRate;
    }

    public void setCuttingRate(BigDecimal cuttingRate) {
        this.cuttingRate = cuttingRate;
    }

    public Integer getOverdueDays() {
        return overdueDays;
    }

    public void setOverdueDays(Integer overdueDays) {
        this.overdueDays = overdueDays;
    }

    public BigDecimal getPlanQuantity() {
        return planQuantity;
    }

    public void setPlanQuantity(BigDecimal planQuantity) {
        this.planQuantity = planQuantity;
    }

    public BigDecimal getSubstituteQuantity() {
        return substituteQuantity;
    }

    public void setSubstituteQuantity(BigDecimal substituteQuantity) {
        this.substituteQuantity = substituteQuantity;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

    public String getAllocationStatus() {
        return allocationStatus;
    }

    public void setAllocationStatus(String allocationStatus) {
        this.allocationStatus = allocationStatus;
    }

    public String getSupplyId() {
        return supplyId;
    }

    public void setSupplyId(String supplyId) {
        this.supplyId = supplyId;
    }

    public Date getOverdueTime() {
        return overdueTime;
    }

    public void setOverdueTime(Date overdueTime) {
        this.overdueTime = overdueTime;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

}
