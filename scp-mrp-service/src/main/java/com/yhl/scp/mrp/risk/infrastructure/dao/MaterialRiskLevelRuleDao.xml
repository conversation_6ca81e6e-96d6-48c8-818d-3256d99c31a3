<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.risk.infrastructure.dao.MaterialRiskLevelRuleDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.risk.infrastructure.po.MaterialRiskLevelRulePO">
        <!--@Table mrp_risk_level_rule_params-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="company" jdbcType="VARCHAR" property="company"/>
        <result column="material_type" jdbcType="VARCHAR" property="materialType"/>
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName"/>
        <result column="rule_description" jdbcType="VARCHAR" property="ruleDescription"/>
        <result column="rule_params" jdbcType="VARCHAR" property="ruleParams"/>
        <result column="thickness" jdbcType="VARCHAR" property="thickness"/>
        <result column="color" jdbcType="VARCHAR" property="color"/>
        <result column="risk_level" jdbcType="VARCHAR" property="riskLevel"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.risk.vo.MaterialRiskLevelRuleVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
id,company,material_type,rule_name,rule_description,rule_params,thickness,color,risk_level,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.ids != null and params.ids.size > 0">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="params.company != null and params.company != ''">
                and company = #{params.company,jdbcType=VARCHAR}
            </if>
            <if test="params.materialType != null and params.materialType != ''">
                and material_type = #{params.materialType,jdbcType=VARCHAR}
            </if>
            <if test="params.ruleName != null and params.ruleName != ''">
                and rule_name = #{params.ruleName,jdbcType=VARCHAR}
            </if>
            <if test="params.ruleDescription != null and params.ruleDescription != ''">
                and rule_description = #{params.ruleDescription,jdbcType=VARCHAR}
            </if>
            <if test="params.ruleParams != null and params.ruleParams != ''">
                and rule_params = #{params.ruleParams,jdbcType=VARCHAR}
            </if>
            <if test="params.thickness != null">
                and thickness = #{params.thickness,jdbcType=VARCHAR}
            </if>
            <if test="params.color != null and params.color != ''">
                and color = #{params.color,jdbcType=VARCHAR}
            </if>
            <if test="params.riskLevel != null and params.riskLevel != ''">
                and risk_level = #{params.riskLevel,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_material_risk_level_rule
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_material_risk_level_rule
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from mrp_material_risk_level_rule
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_material_risk_level_rule
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.risk.infrastructure.po.MaterialRiskLevelRulePO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_risk_level_rule(
        id,
        company,
        material_type,
        rule_name,
        rule_description,
        rule_params,
        thickness,
        color,
        risk_level,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{company,jdbcType=VARCHAR},
        #{materialType,jdbcType=VARCHAR},
        #{ruleName,jdbcType=VARCHAR},
        #{ruleDescription,jdbcType=VARCHAR},
        #{ruleParams,jdbcType=VARCHAR},
        #{thickness,jdbcType=VARCHAR},
        #{color,jdbcType=VARCHAR},
        #{riskLevel,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mrp.risk.infrastructure.po.MaterialRiskLevelRulePO">
        insert into mrp_material_risk_level_rule(
        id,
        company,
        material_type,
        rule_name,
        rule_description,
        rule_params,
        thickness,
        color,
        risk_level,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{company,jdbcType=VARCHAR},
        #{materialType,jdbcType=VARCHAR},
        #{ruleName,jdbcType=VARCHAR},
        #{ruleDescription,jdbcType=VARCHAR},
        #{ruleParams,jdbcType=VARCHAR},
        #{thickness,jdbcType=VARCHAR},
        #{color,jdbcType=VARCHAR},
        #{riskLevel,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_risk_level_rule(
        id,
        company,
        material_type,
        rule_name,
        rule_description,
        rule_params,
        thickness,
        color,
        risk_level,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.company,jdbcType=VARCHAR},
        #{entity.materialType,jdbcType=VARCHAR},
        #{entity.ruleName,jdbcType=VARCHAR},
        #{entity.ruleDescription,jdbcType=VARCHAR},
        #{entity.ruleParams,jdbcType=VARCHAR},
        #{entity.thickness,jdbcType=VARCHAR},
        #{entity.color,jdbcType=VARCHAR},
        #{entity.riskLevel,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_risk_level_rule(
        id,
        company,
        material_type,
        rule_name,
        rule_description,
        rule_params,
        thickness,
        color,
        risk_level,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.company,jdbcType=VARCHAR},
        #{entity.materialType,jdbcType=VARCHAR},
        #{entity.ruleName,jdbcType=VARCHAR},
        #{entity.ruleDescription,jdbcType=VARCHAR},
        #{entity.ruleParams,jdbcType=VARCHAR},
        #{entity.thickness,jdbcType=VARCHAR},
        #{entity.color,jdbcType=VARCHAR},
        #{entity.riskLevel,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.risk.infrastructure.po.MaterialRiskLevelRulePO">
        update mrp_material_risk_level_rule set
        company = #{company,jdbcType=VARCHAR},
        material_type = #{materialType,jdbcType=VARCHAR},
        rule_name = #{ruleName,jdbcType=VARCHAR},
        rule_description = #{ruleDescription,jdbcType=VARCHAR},
        rule_params = #{ruleParams,jdbcType=VARCHAR},
        thickness = #{thickness,jdbcType=VARCHAR},
        color = #{color,jdbcType=VARCHAR},
        risk_level = #{riskLevel,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR};
        update mrp_material_risk_level_rule set
            version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.risk.infrastructure.po.MaterialRiskLevelRulePO">
        update mrp_material_risk_level_rule
        <set>
            <if test="item.company != null and item.company != ''">
                company = #{item.company,jdbcType=VARCHAR},
            </if>
            <if test="item.materialType != null and item.materialType != ''">
                material_type = #{item.materialType,jdbcType=VARCHAR},
            </if>
            <if test="item.ruleName != null and item.ruleName != ''">
                rule_name = #{item.ruleName,jdbcType=VARCHAR},
            </if>
            <if test="item.ruleDescription != null and item.ruleDescription != ''">
                rule_description = #{item.ruleDescription,jdbcType=VARCHAR},
            </if>
            <if test="item.ruleParams != null and item.ruleParams != ''">
                rule_params = #{item.ruleParams,jdbcType=VARCHAR},
            </if>
            <if test="item.thickness != null">
                thickness = #{item.thickness,jdbcType=VARCHAR},
            </if>
            <if test="item.color != null and item.color != ''">
                color = #{item.color,jdbcType=VARCHAR},
            </if>
            <if test="item.riskLevel != null and item.riskLevel != ''">
                risk_level = #{item.riskLevel,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR};
        update mrp_material_risk_level_rule set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_risk_level_rule
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="company = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.company,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="rule_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ruleName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="rule_description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ruleDescription,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="rule_params = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ruleParams,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="thickness = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.thickness,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="color = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.color,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="risk_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.riskLevel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>;
        update mrp_material_risk_level_rule set
        version_value = version_value + 1
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mrp_material_risk_level_rule
        <set>
            <if test="item.company != null and item.company != ''">
                company = #{item.company,jdbcType=VARCHAR},
            </if>
            <if test="item.materialType != null and item.materialType != ''">
                material_type = #{item.materialType,jdbcType=VARCHAR},
            </if>
            <if test="item.ruleName != null and item.ruleName != ''">
                rule_name = #{item.ruleName,jdbcType=VARCHAR},
            </if>
            <if test="item.ruleDescription != null and item.ruleDescription != ''">
                rule_description = #{item.ruleDescription,jdbcType=VARCHAR},
            </if>
            <if test="item.ruleParams != null and item.ruleParams != ''">
                rule_params = #{item.ruleParams,jdbcType=VARCHAR},
            </if>
            <if test="item.thickness != null">
                thickness = #{item.thickness,jdbcType=VARCHAR},
            </if>
            <if test="item.color != null and item.color != ''">
                color = #{item.color,jdbcType=VARCHAR},
            </if>
            <if test="item.riskLevel != null and item.riskLevel != ''">
                risk_level = #{item.riskLevel,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER};
        update mrp_material_risk_level_rule set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mrp_material_risk_level_rule where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_risk_level_rule where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
