package com.yhl.scp.ips.rbac.service.impl;

import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.rbac.dao.TenantUserDao;
import com.yhl.scp.ips.rbac.entity.TenantUser;
import com.yhl.scp.ips.rbac.service.TenantUserService;
import com.yhl.scp.ips.system.DefaultTenant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <code>TenantUserServiceImpl</code>
 * <p>
 * TenantUserServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-17 20:21:06
 */
@Service
public class TenantUserServiceImpl implements TenantUserService {

    @Autowired
    private TenantUserDao tenantUserDao;

    @Override
    public void doCreate(String userId, String tenantId, String userType) {
        TenantUser tenantUser = new TenantUser();
        tenantUser.setUserId(userId);
        tenantUser.setTenantId(tenantId);
        tenantUser.setUserType(userType);
        tenantUser.setId(UUIDUtil.getUUID());
        tenantUserDao.insert(tenantUser);
    }

    @Override
    public void doDeleteByTenantId(String tenantId) {
        tenantUserDao.deleteByTenantId(tenantId);
    }

    @Override
    public List<TenantUser> selectAll() {
        return tenantUserDao.selectAll();
    }

    @Override
    public List<TenantUser> selectTenantAdminByTenantId(String id) {
        return tenantUserDao.selectTenantAdminByTenantId(id);
    }

    @Override
    public List<TenantUser> selectByUserId(String userId) {
        return tenantUserDao.selectByUserId(userId);
    }

    @Override
    public void doUpdate(TenantUser tenantUser) {
        tenantUserDao.updateByPrimaryKey(tenantUser);
    }

    @Override
    public void doSetDefaultTenant(DefaultTenant defaultTenant) {
        if (null != defaultTenant) {
            List<TenantUser> tenantUsers = selectByUserId(SystemHolder.getUserId());
            for (TenantUser tenantUser : tenantUsers) {
                tenantUser.setDefaultModule(null);
                tenantUser.setMaster(YesOrNoEnum.NO.getCode());
                doUpdate(tenantUser);
            }
            Optional<TenantUser> optional = tenantUsers.stream().
                    filter(item -> item.getTenantId().equals(defaultTenant.getTenantId())).findAny();
            if (optional.isPresent()) {
                TenantUser tenantUser = optional.get();
                tenantUser.setMaster(YesOrNoEnum.YES.getCode());
                tenantUser.setDefaultModule(defaultTenant.getModuleCode());
                doUpdate(tenantUser);
            }

        }

    }

    @Override
    public DefaultTenant getDefaultTenant() {
        DefaultTenant defaultTenant = new DefaultTenant();
        List<TenantUser> tenantUsers = selectByUserId(SystemHolder.getUserId());
        Optional<TenantUser> optional = tenantUsers.stream().
                filter(item -> YesOrNoEnum.YES.getCode().equals(item.getMaster())).findAny();

        if (optional.isPresent()) {
            defaultTenant.setTenantId(optional.get().getTenantId());
            defaultTenant.setModuleCode(optional.get().getDefaultModule());
        }

        return defaultTenant;
    }

    @Override
    public List<TenantUser> selectByTenantIds(List<String> tenantIds) {
        if (CollectionUtils.isEmpty(tenantIds)) {
            return new ArrayList<>();
        }
        return tenantUserDao.selectByTenantIds(tenantIds);
    }

    @Override
    public void doDeleteTenantAdminByUserId(String userId) {
        tenantUserDao.deleteTenantAdminByUserId(userId);
    }

    @Override
    public void doDeleteByUserIdAndTenantId(String userId, String tenantId) {
        tenantUserDao.deleteByUserIdAndTenantId(userId, tenantId);
    }

    @Override
    public void doCreateBatchById(List<TenantUser> tenantUserList) {
        tenantUserDao.insertBatch(tenantUserList);
    }
}
