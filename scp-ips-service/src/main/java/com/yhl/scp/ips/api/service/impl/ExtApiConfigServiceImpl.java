package com.yhl.scp.ips.api.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableMap;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.enums.ObjectTypeEnum;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.ips.api.convertor.ExtApiConfigConvertor;
import com.yhl.scp.ips.api.domain.entity.ExtApiConfigDO;
import com.yhl.scp.ips.api.domain.service.ExtApiConfigDomainService;
import com.yhl.scp.ips.api.dto.ExtApiConfigDTO;
import com.yhl.scp.ips.api.infrastructure.dao.ExtApiConfigDao;
import com.yhl.scp.ips.api.infrastructure.po.ExtApiConfigPO;
import com.yhl.scp.ips.api.service.ExtApiConfigService;
import com.yhl.scp.ips.api.vo.ExtApiConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class ExtApiConfigServiceImpl extends AbstractService implements ExtApiConfigService {

    @Resource
    private ExtApiConfigDao extApiConfigDao;

    @Resource
    private ExtApiConfigDomainService extApiConfigDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(ExtApiConfigDTO extApiConfigDTO) {
        // 0.数据转换
        ExtApiConfigDO extApiConfigDO = ExtApiConfigConvertor.INSTANCE.dto2Do(extApiConfigDTO);
        ExtApiConfigPO extApiConfigPO = ExtApiConfigConvertor.INSTANCE.dto2Po(extApiConfigDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        extApiConfigDomainService.validation(extApiConfigDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(extApiConfigPO);
        extApiConfigDao.insert(extApiConfigPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(ExtApiConfigDTO extApiConfigDTO) {
        // 0.数据转换
        ExtApiConfigDO extApiConfigDO = ExtApiConfigConvertor.INSTANCE.dto2Do(extApiConfigDTO);
        ExtApiConfigPO extApiConfigPO = ExtApiConfigConvertor.INSTANCE.dto2Po(extApiConfigDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        extApiConfigDomainService.validation(extApiConfigDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(extApiConfigPO);
        extApiConfigDao.update(extApiConfigPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ExtApiConfigDTO> list) {
        List<ExtApiConfigPO> newList = ExtApiConfigConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        extApiConfigDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<ExtApiConfigDTO> list) {
        List<ExtApiConfigPO> newList = ExtApiConfigConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        extApiConfigDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return extApiConfigDao.deleteBatch(idList);
        }
        return extApiConfigDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ExtApiConfigVO selectByPrimaryKey(String id) {
        ExtApiConfigPO po = extApiConfigDao.selectByPrimaryKey(id);
        return ExtApiConfigConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "EXT_API_CONFIG")
    public List<ExtApiConfigVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "EXT_API_CONFIG")
    public List<ExtApiConfigVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ExtApiConfigVO> dataList = extApiConfigDao.selectByCondition(sortParam, queryCriteriaParam);
        ExtApiConfigServiceImpl target = springBeanUtils.getBean(ExtApiConfigServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ExtApiConfigVO> selectByParams(Map<String, Object> params) {
        List<ExtApiConfigPO> list = extApiConfigDao.selectByParams(params);
        return ExtApiConfigConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ExtApiConfigVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<String> dropEnum() {
        List<ExtApiConfigVO> list = extApiConfigDao.selectApiSource();
        return list.stream().map(ExtApiConfigVO::getApiSource).collect(Collectors.toList());
    }

    @Override
    public BaseResponse<List<ExtApiConfigVO>> queryData(ExtApiConfigVO extApiConfigVO) {
        try {
            Map<String, Object> queryMap = ImmutableMap.of(
                    "apiSource", extApiConfigVO.getApiSource(),
                    "apiNameLike", extApiConfigVO.getApiName());
            List<ExtApiConfigVO> extApiConfigVOS = selectByParams(queryMap);
            return BaseResponse.success(extApiConfigVOS);
        } catch (Exception e) {
            log.error("查询接口表报错,{},{}", e.getMessage(), e.getCause().toString());
            throw new BusinessException("查询接口表报错:" + e.getMessage());
        }
    }

    @Override
    public List<LabelValue<String>> dropEnumById() {
        List<ExtApiConfigPO> list = extApiConfigDao.selectByParams(ImmutableMap.of());
        return list.stream()
                .map(x -> new LabelValue<>(x.getApiName() + "(" + x.getApiCategory() + ")", x.getId()))
                .collect(Collectors.toList());
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.EXT_API_CONFIG.getCode();
    }

    @Override
    public List<ExtApiConfigVO> invocation(List<ExtApiConfigVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
