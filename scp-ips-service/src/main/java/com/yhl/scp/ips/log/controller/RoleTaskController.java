package com.yhl.scp.ips.log.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.log.dto.RoleTaskDTO;
import com.yhl.scp.ips.log.service.RoleTaskService;
import com.yhl.scp.ips.log.vo.RoleTaskVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>RoleTaskController</code>
 * <p>
 * 角色任务表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-11 11:42:13
 */
@Slf4j
@Api(tags = "角色任务表控制器")
@RestController
@RequestMapping("roleTask")
public class RoleTaskController extends BaseController {

    @Resource
    private RoleTaskService roleTaskService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<RoleTaskVO>> page() {
        List<RoleTaskVO> roleTaskList = roleTaskService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<RoleTaskVO> pageInfo = new PageInfo<>(roleTaskList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody RoleTaskDTO roleTaskDTO) {
        return roleTaskService.doCreate(roleTaskDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody RoleTaskDTO roleTaskDTO) {
        return roleTaskService.doUpdate(roleTaskDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        roleTaskService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<RoleTaskVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, roleTaskService.selectByPrimaryKey(id));
    }

}
