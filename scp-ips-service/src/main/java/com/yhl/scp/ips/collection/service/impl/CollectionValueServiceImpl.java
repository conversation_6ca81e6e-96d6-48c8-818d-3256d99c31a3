package com.yhl.scp.ips.collection.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.collection.convertor.CollectionValueConvertor;
import com.yhl.scp.ips.collection.domain.entity.CollectionValueDO;
import com.yhl.scp.ips.collection.domain.service.CollectionValueDomainService;
import com.yhl.scp.ips.collection.dto.CollectionValueDTO;
import com.yhl.scp.ips.collection.infrastructure.dao.CollectionValueDao;
import com.yhl.scp.ips.collection.infrastructure.po.CollectionValuePO;
import com.yhl.scp.ips.collection.service.CollectionValueService;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.config.CollectionCacheInit;
import com.yhl.scp.ips.utils.BasePOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>CollectionValueServiceImpl</code>
 * <p>
 * 值集值应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-12-07 17:38:20
 */
@Slf4j
@Service
public class CollectionValueServiceImpl extends AbstractService implements CollectionValueService {

    @Resource
    private CollectionValueDao collectionValueDao;

    @Resource
    private CollectionValueDomainService collectionValueDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private CollectionCacheInit collectionCacheInit;

    @Override
    public BaseResponse<Void> doCreate(CollectionValueDTO collectionValueDTO) {
        // 0.数据转换
        CollectionValueDO collectionValueDO = CollectionValueConvertor.INSTANCE.dto2Do(collectionValueDTO);
        CollectionValuePO collectionValuePO = CollectionValueConvertor.INSTANCE.dto2Po(collectionValueDTO);
        // 1.数据校验
        collectionValueDomainService.validation(collectionValueDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(collectionValuePO);
        collectionValueDao.insert(collectionValuePO);
        collectionCacheInit.deleteByCollectionIds(Lists.newArrayList(collectionValueDO.getCollectionId()));
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(CollectionValueDTO collectionValueDTO) {
        // 0.数据转换
        CollectionValueDO collectionValueDO = CollectionValueConvertor.INSTANCE.dto2Do(collectionValueDTO);
        CollectionValuePO collectionValuePO = CollectionValueConvertor.INSTANCE.dto2Po(collectionValueDTO);
        // 1.数据校验
        collectionValueDomainService.validation(collectionValueDO);
        // 2.数据持久化
        if (Objects.isNull(collectionValuePO.getEnabled())) {
            collectionValuePO.setEnabled(YesOrNoEnum.YES.getCode());
        }
        BasePOUtils.updateFiller(collectionValuePO);
        collectionValueDao.update(collectionValuePO);
        collectionCacheInit.deleteByCollectionIds(Lists.newArrayList(collectionValueDO.getCollectionId()));
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<CollectionValueDTO> list) {
        List<CollectionValuePO> newList = CollectionValueConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        collectionValueDao.insertBatch(newList);
        if (CollectionUtils.isNotEmpty(list)) {
            collectionCacheInit.deleteByCollectionIds(list.stream().map(CollectionValueDTO::getCollectionId).collect(Collectors.toList()));
        }
    }

    @Override
    public void doUpdateBatch(List<CollectionValueDTO> list) {
        List<CollectionValuePO> newList = CollectionValueConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        collectionValueDao.updateBatch(newList);
        if (CollectionUtils.isNotEmpty(list)) {
            collectionCacheInit.deleteByCollectionIds(list.stream().map(CollectionValueDTO::getCollectionId).collect(Collectors.toList()));
        }
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            collectionCacheInit.deleteByCollectionValueIds(idList);
            return collectionValueDao.deleteBatch(idList);
        }
        collectionCacheInit.deleteByCollectionValueIds(Lists.newArrayList(idList.get(0)));
        return collectionValueDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public CollectionValueVO selectByPrimaryKey(String id) {
        CollectionValuePO po = collectionValueDao.selectByPrimaryKey(id);
        return CollectionValueConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "COLLECTION_VALUE")
    public List<CollectionValueVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "COLLECTION_VALUE")
    public List<CollectionValueVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<CollectionValueVO> dataList = collectionValueDao.selectByCondition(sortParam, queryCriteriaParam);
        CollectionValueServiceImpl target = springBeanUtils.getBean(CollectionValueServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<CollectionValueVO> selectByParams(Map<String, Object> params) {
        List<CollectionValuePO> list = collectionValueDao.selectByParams(params);
        return CollectionValueConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<CollectionValueVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<CollectionValueVO> selectByCollectionIds(List<String> collectionIds) {
        if (CollectionUtils.isEmpty(collectionIds)) {
            return Collections.emptyList();
        }
        return collectionValueDao.selectByCollectionIds(collectionIds);
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<CollectionValueVO> invocation(List<CollectionValueVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public List<CollectionValueVO> selectByCollectionCode(String collectionCode) {
        return CollectionValueConvertor.INSTANCE.po2Vos(collectionValueDao.selectByCollectionCode(collectionCode));
    }
}
