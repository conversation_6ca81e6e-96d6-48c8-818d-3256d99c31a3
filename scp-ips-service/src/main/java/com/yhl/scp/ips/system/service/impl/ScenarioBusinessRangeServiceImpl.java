package com.yhl.scp.ips.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.ips.system.convertor.ScenarioBusinessRangeConvertor;
import com.yhl.scp.ips.system.domain.entity.ScenarioBusinessRangeDO;
import com.yhl.scp.ips.system.domain.service.ScenarioBusinessRangeDomainService;
import com.yhl.scp.ips.system.dto.ScenarioBusinessRangeDTO;
import com.yhl.scp.ips.system.infrastructure.dao.ScenarioBusinessRangeDao;
import com.yhl.scp.ips.system.infrastructure.po.ScenarioBusinessRangePO;
import com.yhl.scp.ips.system.service.ScenarioBusinessRangeService;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>ScenarioBusinessRangeServiceImpl</code>
 * <p>
 * 场景业务范围应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-01 10:13:59
 */
@Slf4j
@Service
public class ScenarioBusinessRangeServiceImpl extends AbstractService implements ScenarioBusinessRangeService {

    @Resource
    private ScenarioBusinessRangeDao scenarioBusinessRangeDao;

    @Resource
    private ScenarioBusinessRangeDomainService scenarioBusinessRangeDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(ScenarioBusinessRangeDTO scenarioBusinessRangeDTO) {
        // 0.数据转换
        ScenarioBusinessRangeDO scenarioBusinessRangeDO = ScenarioBusinessRangeConvertor.INSTANCE.dto2Do(scenarioBusinessRangeDTO);
        ScenarioBusinessRangePO scenarioBusinessRangePO = ScenarioBusinessRangeConvertor.INSTANCE.dto2Po(scenarioBusinessRangeDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        scenarioBusinessRangeDomainService.validation(scenarioBusinessRangeDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(scenarioBusinessRangePO);
        scenarioBusinessRangeDao.insert(scenarioBusinessRangePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(ScenarioBusinessRangeDTO scenarioBusinessRangeDTO) {
        // 0.数据转换
        ScenarioBusinessRangeDO scenarioBusinessRangeDO = ScenarioBusinessRangeConvertor.INSTANCE.dto2Do(scenarioBusinessRangeDTO);
        ScenarioBusinessRangePO scenarioBusinessRangePO = ScenarioBusinessRangeConvertor.INSTANCE.dto2Po(scenarioBusinessRangeDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        scenarioBusinessRangeDomainService.validation(scenarioBusinessRangeDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(scenarioBusinessRangePO);
        scenarioBusinessRangeDao.update(scenarioBusinessRangePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ScenarioBusinessRangeDTO> list) {
        List<ScenarioBusinessRangePO> newList = ScenarioBusinessRangeConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        scenarioBusinessRangeDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<ScenarioBusinessRangeDTO> list) {
        List<ScenarioBusinessRangePO> newList = ScenarioBusinessRangeConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        scenarioBusinessRangeDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return scenarioBusinessRangeDao.deleteBatch(idList);
        }
        return scenarioBusinessRangeDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ScenarioBusinessRangeVO selectByPrimaryKey(String id) {
        ScenarioBusinessRangePO po = scenarioBusinessRangeDao.selectByPrimaryKey(id);
        return ScenarioBusinessRangeConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "SCENARIO_BUSINESS_RANGE")
    public List<ScenarioBusinessRangeVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "SCENARIO_BUSINESS_RANGE")
    public List<ScenarioBusinessRangeVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ScenarioBusinessRangeVO> dataList = scenarioBusinessRangeDao.selectByCondition(sortParam, queryCriteriaParam);
        ScenarioBusinessRangeServiceImpl target = springBeanUtils.getBean(ScenarioBusinessRangeServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ScenarioBusinessRangeVO> selectByParams(Map<String, Object> params) {
        List<ScenarioBusinessRangePO> list = scenarioBusinessRangeDao.selectByParams(params);
        return ScenarioBusinessRangeConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ScenarioBusinessRangeVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
//        return ObjectTypeEnum.SCENARIO_BUSINESS_RANGE.getCode();
        return null;
    }

    @Override
    public List<ScenarioBusinessRangeVO> invocation(List<ScenarioBusinessRangeVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
