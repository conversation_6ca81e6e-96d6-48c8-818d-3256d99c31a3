package com.yhl.scp.ips.warning.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class WarningSqlSettingDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -29401797904073103L;

    /**
     * 主键id
     */
    private String id;
    /**
     * 预警编码
     */
    private String warningCode;
    /**
     * 预警描述
     */
    private String warningDescription;
    /**
     * 接收者
     */
    private String receiver;
    /**
     * 抄送者
     */
    private String carbonCopy;
    /**
     * 密送者
     */
    private String blindCarbonCopy;
    /**
     * 预警sql内容
     */
    private String sqlContext;
    /**
     * 对应租户
     */
    private String databaseTenant;
    /**
     * cron
     */
    private String pollingTime;
    /**
     * 版本号
     */
    private Integer versionValue;
    /**
     * 生效日期
     */
    private Date activationDate;
    /**
     * 失效日期
     */
    private Date invalidDate;
    /**
     * 正文内容
     */
    private String textContent;
    /**
     * 发送方式（1邮箱，2企微，3短信）
     */
    private String sendWay;
    /**
     * 企微工号
     */
    private String staffCode;
    /**
     * 短信手机号码
     */
    private String phone;
    /**
     * 数据来源（1sql，2接口）
     */
    private String dataSource;
    /**
     * 接口配置
     */
    private String interfaceSetting;

}
