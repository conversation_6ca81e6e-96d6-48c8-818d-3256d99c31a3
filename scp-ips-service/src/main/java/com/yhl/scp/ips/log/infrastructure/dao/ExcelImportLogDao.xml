<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.log.infrastructure.dao.ExcelImportLogDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.log.infrastructure.po.ExcelImportLogPO">
        <!--@Table log_excel_import_log-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="object_type" jdbcType="VARCHAR" property="objectType"/>
        <result column="import_type" jdbcType="VARCHAR" property="importType"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="info_type" jdbcType="VARCHAR" property="infoType"/>
        <result column="display_index" jdbcType="INTEGER" property="displayIndex"/>
        <result column="error_detail" jdbcType="VARCHAR" property="errorDetail"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="module_code" jdbcType="VARCHAR" property="moduleCode"/>
        <result column="scenario_id" jdbcType="VARCHAR" property="scenarioId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.log.vo.ExcelImportLogVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
id,object_type,import_type,batch_no,info_type,display_index,error_detail,tenant_id,module_code,scenario_id,remark,enabled,creator,create_time,modifier,modify_time,parent_id
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.objectType != null and params.objectType != ''">
                and object_type = #{params.objectType,jdbcType=VARCHAR}
            </if>
            <if test="params.importType != null and params.importType != ''">
                and import_type = #{params.importType,jdbcType=VARCHAR}
            </if>
            <if test="params.batchNo != null and params.batchNo != ''">
                and batch_no = #{params.batchNo,jdbcType=VARCHAR}
            </if>
            <if test="params.infoType != null and params.infoType != ''">
                and info_type = #{params.infoType,jdbcType=VARCHAR}
            </if>
            <if test="params.displayIndex != null">
                and display_index = #{params.displayIndex,jdbcType=INTEGER}
            </if>
            <if test="params.errorDetail != null and params.errorDetail != ''">
                and error_detail = #{params.errorDetail,jdbcType=VARCHAR}
            </if>
            <if test="params.tenantId != null and params.tenantId != ''">
                and tenant_id = #{params.tenantId,jdbcType=VARCHAR}
            </if>
            <if test="params.moduleCode != null and params.moduleCode != ''">
                and module_code = #{params.moduleCode,jdbcType=VARCHAR}
            </if>
            <if test="params.scenarioId != null and params.scenarioId != ''">
                and scenario_id = #{params.scenarioId,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.parentId != null and params.parentId != ''">
                and parent_id = #{params.parentId,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from log_excel_import_log
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from log_excel_import_log
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from log_excel_import_log
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from log_excel_import_log
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectParentLog" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from log_excel_import_log where info_type = 'GATHER'
    </select>

    <select id="selectByParentId" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from log_excel_import_log where parent_id = #{parentId,jdbcType=VARCHAR}
    </select>

    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.ips.log.infrastructure.po.ExcelImportLogPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into log_excel_import_log(
        id,
        object_type,
        import_type,
        batch_no,
        info_type,
        display_index,
        error_detail,
        tenant_id,
        module_code,
        scenario_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        parent_id)
        values (
        #{id,jdbcType=VARCHAR},
        #{objectType,jdbcType=VARCHAR},
        #{importType,jdbcType=VARCHAR},
        #{batchNo,jdbcType=VARCHAR},
        #{infoType,jdbcType=VARCHAR},
        #{displayIndex,jdbcType=INTEGER},
        #{errorDetail,jdbcType=VARCHAR},
        #{tenantId,jdbcType=VARCHAR},
        #{moduleCode,jdbcType=VARCHAR},
        #{scenarioId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{parentId,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.ips.log.infrastructure.po.ExcelImportLogPO">
        insert into log_excel_import_log(
        id,
        object_type,
        import_type,
        batch_no,
        info_type,
        display_index,
        error_detail,
        tenant_id,
        module_code,
        scenario_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        parent_id)
        values (
        #{id,jdbcType=VARCHAR},
        #{objectType,jdbcType=VARCHAR},
        #{importType,jdbcType=VARCHAR},
        #{batchNo,jdbcType=VARCHAR},
        #{infoType,jdbcType=VARCHAR},
        #{displayIndex,jdbcType=INTEGER},
        #{errorDetail,jdbcType=VARCHAR},
        #{tenantId,jdbcType=VARCHAR},
        #{moduleCode,jdbcType=VARCHAR},
        #{scenarioId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{parentId,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into log_excel_import_log(
        id,
        object_type,
        import_type,
        batch_no,
        info_type,
        display_index,
        error_detail,
        tenant_id,
        module_code,
        scenario_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        parent_id)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.objectType,jdbcType=VARCHAR},
        #{entity.importType,jdbcType=VARCHAR},
        #{entity.batchNo,jdbcType=VARCHAR},
        #{entity.infoType,jdbcType=VARCHAR},
        #{entity.displayIndex,jdbcType=INTEGER},
        #{entity.errorDetail,jdbcType=VARCHAR},
        #{entity.tenantId,jdbcType=VARCHAR},
        #{entity.moduleCode,jdbcType=VARCHAR},
        #{entity.scenarioId,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.parentId,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into log_excel_import_log(
        id,
        object_type,
        import_type,
        batch_no,
        info_type,
        display_index,
        error_detail,
        tenant_id,
        module_code,
        scenario_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        parent_id)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.objectType,jdbcType=VARCHAR},
        #{entity.importType,jdbcType=VARCHAR},
        #{entity.batchNo,jdbcType=VARCHAR},
        #{entity.infoType,jdbcType=VARCHAR},
        #{entity.displayIndex,jdbcType=INTEGER},
        #{entity.errorDetail,jdbcType=VARCHAR},
        #{entity.tenantId,jdbcType=VARCHAR},
        #{entity.moduleCode,jdbcType=VARCHAR},
        #{entity.scenarioId,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.parentId,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.ips.log.infrastructure.po.ExcelImportLogPO">
        update log_excel_import_log set
        object_type = #{objectType,jdbcType=VARCHAR},
        import_type = #{importType,jdbcType=VARCHAR},
        batch_no = #{batchNo,jdbcType=VARCHAR},
        info_type = #{infoType,jdbcType=VARCHAR},
        display_index = #{displayIndex,jdbcType=INTEGER},
        error_detail = #{errorDetail,jdbcType=VARCHAR},
        tenant_id = #{tenantId,jdbcType=VARCHAR},
        module_code = #{moduleCode,jdbcType=VARCHAR},
        scenario_id = #{scenarioId,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        parent_id = #{parentId,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.ips.log.infrastructure.po.ExcelImportLogPO">
        update log_excel_import_log
        <set>
            <if test="item.objectType != null and item.objectType != ''">
                object_type = #{item.objectType,jdbcType=VARCHAR},
            </if>
            <if test="item.importType != null and item.importType != ''">
                import_type = #{item.importType,jdbcType=VARCHAR},
            </if>
            <if test="item.batchNo != null and item.batchNo != ''">
                batch_no = #{item.batchNo,jdbcType=VARCHAR},
            </if>
            <if test="item.infoType != null and item.infoType != ''">
                info_type = #{item.infoType,jdbcType=VARCHAR},
            </if>
            <if test="item.displayIndex != null">
                display_index = #{item.displayIndex,jdbcType=INTEGER},
            </if>
            <if test="item.errorDetail != null and item.errorDetail != ''">
                error_detail = #{item.errorDetail,jdbcType=VARCHAR},
            </if>
            <if test="item.tenantId != null and item.tenantId != ''">
                tenant_id = #{item.tenantId,jdbcType=VARCHAR},
            </if>
            <if test="item.moduleCode != null and item.moduleCode != ''">
                module_code = #{item.moduleCode,jdbcType=VARCHAR},
            </if>
            <if test="item.scenarioId != null and item.scenarioId != ''">
                scenario_id = #{item.scenarioId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.parentId != null and item.parentId != ''">
                parent_id = #{item.parentId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update log_excel_import_log
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="object_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.objectType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="import_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.importType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="batch_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.batchNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="info_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.infoType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="display_index = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.displayIndex,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="error_detail = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.errorDetail,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tenant_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.tenantId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="module_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.moduleCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="scenario_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.scenarioId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="parent_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.parentId,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update log_excel_import_log 
        <set>
            <if test="item.objectType != null and item.objectType != ''">
                object_type = #{item.objectType,jdbcType=VARCHAR},
            </if>
            <if test="item.importType != null and item.importType != ''">
                import_type = #{item.importType,jdbcType=VARCHAR},
            </if>
            <if test="item.batchNo != null and item.batchNo != ''">
                batch_no = #{item.batchNo,jdbcType=VARCHAR},
            </if>
            <if test="item.infoType != null and item.infoType != ''">
                info_type = #{item.infoType,jdbcType=VARCHAR},
            </if>
            <if test="item.displayIndex != null">
                display_index = #{item.displayIndex,jdbcType=INTEGER},
            </if>
            <if test="item.errorDetail != null and item.errorDetail != ''">
                error_detail = #{item.errorDetail,jdbcType=VARCHAR},
            </if>
            <if test="item.tenantId != null and item.tenantId != ''">
                tenant_id = #{item.tenantId,jdbcType=VARCHAR},
            </if>
            <if test="item.moduleCode != null and item.moduleCode != ''">
                module_code = #{item.moduleCode,jdbcType=VARCHAR},
            </if>
            <if test="item.scenarioId != null and item.scenarioId != ''">
                scenario_id = #{item.scenarioId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.parentId != null and item.parentId != ''">
                parent_id = #{item.parentId,jdbcType=VARCHAR},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from log_excel_import_log
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from log_excel_import_log where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>