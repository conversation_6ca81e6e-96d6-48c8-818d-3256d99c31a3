package com.yhl.scp.ips.bpm.listener;

import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEntityEvent;
import org.flowable.engine.delegate.event.AbstractFlowableEngineEventListener;
import org.flowable.engine.delegate.event.FlowableProcessStartedEvent;
import org.flowable.engine.delegate.event.impl.FlowableEntityEventImpl;
import org.flowable.engine.impl.persistence.entity.ExecutionEntityImpl;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <code>GlobalProcessListener</code>
 * <p>
 * 全局流程监听器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-10-20 11:08:54
 */
@Component
@Slf4j
public class GlobalProcessListener extends AbstractFlowableEngineEventListener {

    @Override
    protected void processStarted(FlowableProcessStartedEvent event) {
        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Process starting ...");

        String eventName = event.getType().name();

        FlowableEntityEventImpl flowableEntityEvent = (FlowableEntityEventImpl) event;
        ExecutionEntityImpl processInstance = (ExecutionEntityImpl) flowableEntityEvent.getEntity();

        Date startTime = processInstance.getStartTime();
        String processDefinitionKey = processInstance.getProcessDefinitionKey();
        String processInstanceId = processInstance.getProcessInstanceId();
        String processInstanceBusinessKey = processInstance.getProcessInstanceBusinessKey();

        log.info("流程事件名 -> {}", eventName);
        log.info("流程开始时间 -> {}", startTime);
        log.info("流程定义键 -> {}", processDefinitionKey);
        log.info("流程实例ID -> {}", processInstanceId);
        log.info("流程实例业务键 -> {}", processInstanceBusinessKey);

        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Process started ...");
    }

    @Override
    protected void processCompleted(FlowableEngineEntityEvent event) {
        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Process ending ...");
        String eventName = event.getType().name();

        FlowableEntityEventImpl flowableEntityEvent = (FlowableEntityEventImpl) event;
        ExecutionEntityImpl processInstance = (ExecutionEntityImpl) flowableEntityEvent.getEntity();

        Date startTime = processInstance.getStartTime();
        String processDefinitionKey = processInstance.getProcessDefinitionKey();
        String processInstanceId = processInstance.getProcessInstanceId();
        String processInstanceBusinessKey = processInstance.getProcessInstanceBusinessKey();

        log.info("流程事件名 -> {}", eventName);
        log.info("流程开始时间 -> {}", startTime);
        log.info("流程定义键 -> {}", processDefinitionKey);
        log.info("流程实例ID -> {}", processInstanceId);
        log.info("流程实例业务键 -> {}", processInstanceBusinessKey);

        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Process ended ...");
    }

}