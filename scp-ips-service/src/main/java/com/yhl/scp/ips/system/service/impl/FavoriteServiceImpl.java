package com.yhl.scp.ips.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.i18n.service.UserDefaultLanguageService;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.WorkBenchEnum;
import com.yhl.scp.ips.object.entity.ObjectI18n;
import com.yhl.scp.ips.object.service.ObjectI18nService;
import com.yhl.scp.ips.rbac.entity.Resource;
import com.yhl.scp.ips.rbac.service.ResourceService;
import com.yhl.scp.ips.rbac.vo.WorkBenchResourceVO;
import com.yhl.scp.ips.system.dao.FavoriteDao;
import com.yhl.scp.ips.system.dao.FavoriteItemDao;
import com.yhl.scp.ips.system.entity.Favorite;
import com.yhl.scp.ips.system.entity.FavoriteItem;
import com.yhl.scp.ips.system.service.FavoriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>FavoriteServiceImpl</code>
 * <p>
 * FavoriteServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-12-11 16:13:24
 */
@Service
public class FavoriteServiceImpl implements FavoriteService {

    @Autowired
    private FavoriteDao favoriteDao;
    @Autowired
    private FavoriteItemDao favoriteItemDao;
    @Autowired
    private ResourceService resourceService;

    @Autowired
    private ObjectI18nService objectI18nService;

    @Override
    public BaseResponse doCreate(Favorite favorite) {
        favorite.setId(UUIDUtil.getUUID());
        favorite.setUserId(SystemHolder.getUserId());
        favorite.setModule(SystemHolder.getModuleCode());
        favoriteDao.insert(favorite);
        return BaseResponse.success("创建成功");
    }

    @Override
    public BaseResponse doCreatePersonal(WorkBenchResourceVO workBenchResourceVO) {
        String userId = SystemHolder.getUserId();
        String resourceId = workBenchResourceVO.getResourceId();
        if(StrUtil.isBlank(resourceId)){
            return BaseResponse.error("无资源id，无法收藏");
        }

        // 查询是否存在用户个人收藏夹
        Favorite favorite = Optional.ofNullable(favoriteDao.selectByUserIdPersonal(userId))
                .orElseGet(() -> createNewFavorite(userId));

        // 判断是否已经收藏
        boolean alreadyFavorite = favorite.getFavoriteItems().stream()
                .anyMatch(item -> Objects.equals(item.getItemId(), resourceId));

        if (alreadyFavorite) {
            return BaseResponse.error("当前页面已经收藏，请勿重复收藏");
        }

        // 插入新收藏项
        FavoriteItem favoriteItem = new FavoriteItem();
        favoriteItem.setId(UUIDUtil.getUUID());
        favoriteItem.setFavoriteId(favorite.getId());
        favoriteItem.setItemId(resourceId);
        favoriteItemDao.insert(favoriteItem);

        return BaseResponse.success("收藏成功");
    }

    /**
     * 创建新的个人收藏夹
     */
    private Favorite createNewFavorite(String userId) {
        Favorite favorite = new Favorite();
        favorite.setId(UUIDUtil.getUUID());
        favorite.setName(SystemHolder.getUserName());
        favorite.setUserId(userId);
        favorite.setModule(WorkBenchEnum.PERSONAL.getCode());
        favorite.setDescription("个人收藏");
        favoriteDao.insert(favorite);
        return favorite;
    }

    @Override
    public BaseResponse doUpdate(Favorite favorite) {
        Favorite sourceFavorite = favoriteDao.selectByPrimaryKey(favorite.getId());
        sourceFavorite.setName(favorite.getName());
        sourceFavorite.setDescription(favorite.getDescription());
        favoriteDao.updateByPrimaryKey(sourceFavorite);
        return BaseResponse.success("修改成功");
    }

    @Override
    public Favorite selectById(String id) {
        return favoriteDao.selectByPrimaryKey(id);
    }

    @Override
    public BaseResponse doDelete(String id) {
        favoriteItemDao.deleteByFavoriteId(id);
        favoriteDao.deleteByPrimaryKey(id);
        return BaseResponse.success("删除成功");
    }

    @Override
    public BaseResponse doDeleteFavoriteItem(String id) {
        favoriteItemDao.deleteByPrimaryKey(id);
        return BaseResponse.success("删除成功");
    }

    @Override
    public BaseResponse doAddItem(FavoriteItem favoriteItem) {
        if (StringUtils.isEmpty(favoriteItem.getFavoriteId())) {
            return BaseResponse.error("请选择收藏夹");
        }
        favoriteItem.setId(UUIDUtil.getUUID());
        favoriteItemDao.insert(favoriteItem);
        return BaseResponse.success("添加成功");
    }

    @Override
    public List<Favorite> selectByUserId(String userId) {
        List<Favorite> favorites = favoriteDao.selectByUserId(userId, SystemHolder.getModuleCode());
        List<Resource> resources = resourceService.selectAll();
        List<ObjectI18n> objectI18ns = objectI18nService.selectByObjectType("RESOURCE");
        Map<String, ObjectI18n> i18nMap = objectI18ns.stream().collect(Collectors.toMap(item -> item.getObjectId() + item.getLanguageCode(), v -> v));
        for (Resource resource : resources) {
            ObjectI18n objectI18n = i18nMap.get(resource.getId() + SystemHolder.getLanguage());
            if (null != objectI18n && StringUtils.isNotEmpty(objectI18n.getDesc())) {
                resource.setResourceName(objectI18n.getDesc());
            }
        }
        Map<String, Resource> resourceMap = resources.stream().collect(Collectors.toMap(item -> item.getId(), v -> v));
        for (Favorite favorite : favorites) {
            List<FavoriteItem> favoriteItems = favorite.getFavoriteItems();
            if (!CollectionUtils.isEmpty(favoriteItems)) {
                for (FavoriteItem favoriteItem : favoriteItems) {
                    Resource resource = resourceMap.get(favoriteItem.getItemId());
                    if (null != resource) {
                        favoriteItem.setName(resource.getResourceName());
                        favoriteItem.setUrl(resource.getUrl());
                    }
                }
            }
        }


        return favorites;
    }

    @Override
    public List<WorkBenchResourceVO> selectWorkBenchResource(String userId) {
        return favoriteDao.selectWorkBenchResource(userId);
    }
}
