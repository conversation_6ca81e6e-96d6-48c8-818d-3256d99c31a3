package com.yhl.scp.ips.collection.domain.factory;

import com.yhl.scp.ips.collection.domain.entity.CollectionValueDO;
import com.yhl.scp.ips.collection.dto.CollectionValueDTO;
import com.yhl.scp.ips.collection.infrastructure.dao.CollectionValueDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>CollectionValueFactory</code>
 * <p>
 * 值集值领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-12-07 17:38:20
 */
@Component
public class CollectionValueFactory {

    @Resource
    private CollectionValueDao collectionValueDao;

    CollectionValueDO create(CollectionValueDTO dto) {
        // TODO
        return null;
    }

}
