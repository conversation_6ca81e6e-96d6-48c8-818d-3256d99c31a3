<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.bpm.dao.BpmHistoryDao">
    <insert id="insertVariables">
        insert into act_hi_varinst (ID_, PROC_INST_ID_, NAME_, TEXT_,
                                    VAR_TYPE_, REV_, CREATE_TIME_, LAST_UPDATED_TIME_)
        values (#{id,jdbcType=VARCHAR}, #{processInstanceId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{value,jdbcType=VARCHAR},
                'string', 1, now(), now())
    </insert>
</mapper>