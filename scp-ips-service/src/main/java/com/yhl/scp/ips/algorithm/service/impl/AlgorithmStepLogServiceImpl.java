package com.yhl.scp.ips.algorithm.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.ips.algorithm.convertor.AlgorithmStepLogConvertor;
import com.yhl.scp.ips.algorithm.domain.entity.AlgorithmStepLogDO;
import com.yhl.scp.ips.algorithm.domain.service.AlgorithmStepLogDomainService;
import com.yhl.scp.ips.algorithm.dto.AlgorithmStepLogDTO;
import com.yhl.scp.ips.algorithm.infrastructure.dao.AlgorithmStepLogDao;
import com.yhl.scp.ips.algorithm.infrastructure.po.AlgorithmStepLogPO;
import com.yhl.scp.ips.algorithm.service.AlgorithmStepLogService;
import com.yhl.scp.ips.algorithm.vo.AlgorithmStepLogVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>AlgorithmStepLogServiceImpl</code>
 * <p>
 * AlgorithmStepLogServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-10 10:04:05
 */
@Slf4j
@Service
public class AlgorithmStepLogServiceImpl extends AbstractService implements AlgorithmStepLogService {

    @Resource
    private AlgorithmStepLogDao algorithmStepLogDao;

    @Resource
    private AlgorithmStepLogDomainService algorithmStepLogDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(AlgorithmStepLogDTO algorithmStepLogDTO) {
        // 0.数据转换
        AlgorithmStepLogDO algorithmStepLogDO = AlgorithmStepLogConvertor.INSTANCE.dto2Do(algorithmStepLogDTO);
        AlgorithmStepLogPO algorithmStepLogPO = AlgorithmStepLogConvertor.INSTANCE.dto2Po(algorithmStepLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        algorithmStepLogDomainService.validation(algorithmStepLogDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(algorithmStepLogPO);
        algorithmStepLogDao.insert(algorithmStepLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(AlgorithmStepLogDTO algorithmStepLogDTO) {
        // 0.数据转换
        AlgorithmStepLogDO algorithmStepLogDO = AlgorithmStepLogConvertor.INSTANCE.dto2Do(algorithmStepLogDTO);
        AlgorithmStepLogPO algorithmStepLogPO = AlgorithmStepLogConvertor.INSTANCE.dto2Po(algorithmStepLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        algorithmStepLogDomainService.validation(algorithmStepLogDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(algorithmStepLogPO);
        algorithmStepLogDao.update(algorithmStepLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<AlgorithmStepLogDTO> list) {
        List<AlgorithmStepLogPO> newList = AlgorithmStepLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        algorithmStepLogDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<AlgorithmStepLogDTO> list) {
        List<AlgorithmStepLogPO> newList = AlgorithmStepLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        algorithmStepLogDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return algorithmStepLogDao.deleteBatch(idList);
        }
        return algorithmStepLogDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public AlgorithmStepLogVO selectByPrimaryKey(String id) {
        AlgorithmStepLogPO po = algorithmStepLogDao.selectByPrimaryKey(id);
        return AlgorithmStepLogConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_auth_algorithm_step_log")
    public List<AlgorithmStepLogVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_auth_algorithm_step_log")
    public List<AlgorithmStepLogVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<AlgorithmStepLogVO> dataList = algorithmStepLogDao.selectByCondition(sortParam, queryCriteriaParam);
        AlgorithmStepLogServiceImpl target = SpringBeanUtils.getBean(AlgorithmStepLogServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<AlgorithmStepLogVO> selectByParams(Map<String, Object> params) {
        List<AlgorithmStepLogPO> list = algorithmStepLogDao.selectByParams(params);
        return AlgorithmStepLogConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<AlgorithmStepLogVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<AlgorithmStepLogVO> invocation(List<AlgorithmStepLogVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
