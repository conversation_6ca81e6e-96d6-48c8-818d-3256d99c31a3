${AnsiColor.BRIGHT_YELLOW}                      ,--,
${AnsiColor.BRIGHT_YELLOW}                    ,--.'|   ,---,        ,--,
${AnsiColor.BRIGHT_YELLOW}         ,--,    ,--,  | :  '  .' \     ,--.'|
${AnsiColor.BRIGHT_YELLOW}       ,'_ /| ,---.'|  : ' /  ;    '.   |  | :                                ,---,
${AnsiColor.BRIGHT_YELLOW}  .--. |  | : |   | : _' |:  :       \  :  : '                            ,-+-. /  |
${AnsiColor.BRIGHT_YELLOW},'_ /| :  . | :   : |.'  |:  |   /\   \ |  ' |      ,---.     ,--.--.    ,--.'|'   |${AnsiColor.BRIGHT_RED}     ██▓ ██▓███    ██████
${AnsiColor.BRIGHT_YELLOW}|  ' | |  . . |   ' '  ; :|  :  ' ;.   :'  | |     /     \   /       \  |   |  ,"' |${AnsiColor.BRIGHT_RED}    ▓██▒▓██░  ██▒▒██    ▒
${AnsiColor.BRIGHT_YELLOW}|  | ' |  | | '   |  .'. ||  |  ;/  \   \  | :    /    /  | .--.  .-. | |   | /  | |${AnsiColor.BRIGHT_RED}    ▒██▒▓██░ ██▓▒░ ▓██▄
${AnsiColor.BRIGHT_YELLOW}:  | | :  ' ; |   | :  | ''  :  | \  \ ,'  : |__ .    ' / |  \__\/: . . |   | |  | |${AnsiColor.BRIGHT_RED}    ░██░▒██▄█▓▒ ▒  ▒   ██▒
${AnsiColor.BRIGHT_YELLOW}|  ; ' |  | ' '   : |  : ;|  |  '  '--' |  | '.'|'   ;   /|  ," .--.; | |   | |  |/ ${AnsiColor.BRIGHT_RED}    ░██░▒██▒ ░  ░▒██████▒▒
${AnsiColor.BRIGHT_YELLOW}:  | : ;  ; | |   | '  ,/ |  :  :       ;  :    ;'   |  / | /  /  ,.  | |   | |--'  ${AnsiColor.BRIGHT_RED}    ░▓  ▒▓▒░ ░  ░▒ ▒▓▒ ▒ ░
${AnsiColor.BRIGHT_YELLOW}'  :  `--'   \;   : ;--'  |  | ,'       |  ,   / |   :    |;  :   .'   \|   |/      ${AnsiColor.BRIGHT_RED}     ▒ ░░▒ ░     ░ ░▒  ░ ░
${AnsiColor.BRIGHT_YELLOW}:  ,      .-./|   ,/      `--''          ---`-'   \   \  / |  ,     .-./'---'       ${AnsiColor.BRIGHT_RED}     ▒ ░░░       ░  ░  ░
${AnsiColor.BRIGHT_YELLOW} `--`----'    '---'                                `----'   `--`---'                ${AnsiColor.BRIGHT_RED}     ░                 ░
${AnsiColor.BRIGHT_WHITE} :: Spring Boot :: ${AnsiColor.BRIGHT_CYAN}${spring-boot.formatted-version}
${AnsiColor.BRIGHT_WHITE} :: Application Name :: ${AnsiColor.BRIGHT_CYAN}${spring.application.name}${AnsiColor.BRIGHT_WHITE} :: Server Port :: ${AnsiColor.BRIGHT_CYAN}${server.port}