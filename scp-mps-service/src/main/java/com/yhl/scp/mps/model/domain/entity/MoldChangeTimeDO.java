package com.yhl.scp.mps.model.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MoldChangeTimeDO</code>
 * <p>
 * 换模换型时间DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-13 15:20:47
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MoldChangeTimeDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -32029756655725638L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 库存点代码
     */
    private String stockPointCode;
    /**
     * 库存点名称
     */
    private String stockPointName;
    /**
     * 工序代码
     */
    private String operationCode;
    /**
     * 工序名称
     */
    private String operationName;
    /**
     * 资源类型
     */
    private String resourceType;
    /**
     * 资源代码
     */
    private String resourceCode;
    /**
     * 资源名称
     */
    private String resourceName;
    /**
     * 本厂编码
     */
    private String productCode;
    /**
     * 物品名称
     */
    private String productName;
    /**
     * 换模时间（分钟）
     */
    private Long dieChangeTime;

    /**
     * 外换模时间（分钟）
     */
    private Long outsideDieChangeTime;

    /**
     * kid
     */
    private String kid;

}
