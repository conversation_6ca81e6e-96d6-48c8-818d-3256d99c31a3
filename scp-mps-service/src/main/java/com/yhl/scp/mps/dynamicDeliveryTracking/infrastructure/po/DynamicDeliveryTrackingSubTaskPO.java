package com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>DynamicDeliveryTrackingSubTaskPO</code>
 * <p>
 * 动态交付跟踪工序任务明细表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 13:47:44
 */
public class DynamicDeliveryTrackingSubTaskPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -28253567970066186L;

    /**
     * 动态交付跟踪工序任务ID
     */
    private String taskId;
    /**
     * 计划数量
     */
    private BigDecimal plannedQuantity;
    /**
     * 完工数量
     */
    private BigDecimal finishedQuantity;
    /**
     * 生产开始时间
     */
    private Date startTime;
    /**
     * 生产结束时间
     */
    private Date endTime;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public BigDecimal getPlannedQuantity() {
        return plannedQuantity;
    }

    public void setPlannedQuantity(BigDecimal plannedQuantity) {
        this.plannedQuantity = plannedQuantity;
    }

    public BigDecimal getFinishedQuantity() {
        return finishedQuantity;
    }

    public void setFinishedQuantity(BigDecimal finishedQuantity) {
        this.finishedQuantity = finishedQuantity;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

}
