package com.yhl.scp.mps.productionLot.domain.factory;

import com.yhl.scp.mps.productionLot.domain.entity.ProductionLotDO;
import com.yhl.scp.mps.productionLot.dto.ProductionLotDTO;
import com.yhl.scp.mps.productionLot.infrastructure.dao.ProductionLotDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>ProductionLotFactory</code>
 * <p>
 * 生产经济批量领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-14 15:24:06
 */
@Component
public class ProductionLotFactory {

    @Resource
    private ProductionLotDao productionLotDao;

    ProductionLotDO create(ProductionLotDTO dto) {
        // TODO
        return null;
    }

}
