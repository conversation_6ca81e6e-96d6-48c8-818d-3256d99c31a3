package com.yhl.scp.mps.algorithm.config.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.utils.CommonDateUtils;
import com.yhl.platform.common.utils.IOUtils;
import com.yhl.scp.ips.common.SystemHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * <code>AlgorithmConfigBase</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-05-15 16:03:50
 */

@Slf4j
@Component
public abstract class AlgorithmConfigBase {
// @Autowired
// 	private ParamGlobalService paramGlobalService;

    /**
     * 本地配置文件名称
     */
    public static final String CONFIG_FILE_NAME = "rule_conf.json";

    /**
     * 生成算法文件名称
     */
    public static final String ALGORITHM_CONFIG_FILE_NAME = "conf.json";


    String getWorkPath(String workspacePath, String executorId) {
        log.info("排程文件夹:" + executorId);
        String workPath = workspacePath + File.separator + SystemHolder.getScenario() + File.separator + executorId + File.separator;
        File workspaceFile = new File(workPath);
        if (workspaceFile.isDirectory() && !workspaceFile.exists()) workspaceFile.mkdirs();
        return workPath;
    }

    String getInputPath(String workspacePath, String executorId) {
        String inputPath = getWorkPath(workspacePath, executorId) + "/input/";
        File file = new File(inputPath);
        if (file.isDirectory() && !file.exists()) file.mkdirs();
        return inputPath;
    }

    /**
     * TODO(将配置conf.json 文件写入工作路径).
     *
     * @param data
     * @param filePath
     * <AUTHOR>
     * @since 2021年12月28日 下午5:14:58
     */
    void writeConfigFile(String data, String filePath) {
        IOUtils.string2JSONFile(data, filePath, ALGORITHM_CONFIG_FILE_NAME);
    }

    /**
     * TODO(读取配置文件).
     *
     * @param executePath 算法执行目录
     * @return
     * @throws IOException
     * <AUTHOR>
     * @since 2021年12月28日 下午5:28:00
     */
    JSONObject getConf(String executePath) throws IOException {
        Resource resource = new ClassPathResource(CONFIG_FILE_NAME);
        File jsonFile = new File(executePath + ALGORITHM_CONFIG_FILE_NAME);
        FileUtils.copyInputStreamToFile(resource.getInputStream(), jsonFile);
        String json = FileUtils.readFileToString(jsonFile);
        JSONObject jsonObject = JSONObject.parseObject(json);
        jsonObject.put("modules", new JSONArray());
        return jsonObject;
    }


    JSONObject getConf() throws IOException {
        StringBuilder result = new StringBuilder();
        Resource resource = new ClassPathResource(CONFIG_FILE_NAME);
        // 使用BufferedReader读取文件内容
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                result.append(line).append("\n");
            }
        }
        return JSONObject.parseObject(result.toString());
    }

    /**
     * 自动排程算法配置
     *
     * @return
     * <AUTHOR>
     * @since 2021年12月28日 下午6:10:50
     */
    JSONObject getConfLoader(Date beginTime, Date endTime) {
        // ParamGlobal paramGlobal = paramGlobalService.select();
        // TODO 这个全家配置
        JSONObject jsonObject = getConfLoaderObject(null, beginTime, endTime);
        return jsonObject;
    }

    private JSONObject getConfLoaderObject(Object paramGlobal, Date beginTime, Date endTime) {
        JSONObject jsonObject = new JSONObject();
        JSONObject confLoader = new JSONObject();
        if (beginTime != null && endTime != null) {
            confLoader.put("beginTime", CommonDateUtils.dateToString(beginTime, CommonDateUtils.COMMON_DATE_STR1));
            confLoader.put("endTime", CommonDateUtils.dateToString(endTime, CommonDateUtils.COMMON_DATE_STR1));
        }
        if (paramGlobal != null) {
            confLoader.put("noSwitchIfSameProduct", "paramGlobal.getNoSwitchIfSameProduct()");
        }
        confLoader.put("outputFolder", "output");
        confLoader.put("dumpDebugDetail", true);
        jsonObject.put("confLoader", confLoader);
        return jsonObject;
    }

    JSONObject getDataLoader() {
        JSONObject jsonObject = new JSONObject();
        JSONObject dataLoader = new JSONObject();
        dataLoader.put("inputFolder", "input");
        dataLoader.put("productFileName", "productInSockingPoint.json");
        jsonObject.put("dataLoader", dataLoader);
        return jsonObject;
    }

    JSONObject getSolver4Rule() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("solver4Rule", new JSONObject());
        return jsonObject;
    }

    /**
     * TODO(规则算法模块).
     *
     * @return
     * <AUTHOR>
     * @since 2021年12月28日 下午5:43:37
     */
    JSONObject getRuleLoader() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("ruleLoader", new JSONObject());
        return jsonObject;
    }

    /**
     * TODO(算法结果文件).
     *
     * @return
     * <AUTHOR>
     * @since 2021年12月28日 下午5:54:25
     */
    JSONObject getResultDumper() {
        JSONObject result = new JSONObject();
        result.put("resultFile", "result_file.json");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("resultDumper", result);
        return jsonObject;
    }

}
