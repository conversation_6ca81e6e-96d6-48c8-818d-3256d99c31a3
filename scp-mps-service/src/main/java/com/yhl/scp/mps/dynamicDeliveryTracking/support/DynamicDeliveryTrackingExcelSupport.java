package com.yhl.scp.mps.dynamicDeliveryTracking.support;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.ddd.BasePO;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.common.excel.DataImportInfo;
import com.yhl.scp.common.excel.ImportDataTypeEnum;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepResourceDO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mps.dynamicDeliveryTracking.convertor.DynamicDeliveryTrackingSubTaskConvertor;
import com.yhl.scp.mps.dynamicDeliveryTracking.convertor.DynamicDeliveryTrackingTaskConvertor;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.dao.DynamicDeliveryTrackingDao;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.dao.DynamicDeliveryTrackingSubTaskDao;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.dao.DynamicDeliveryTrackingTaskDao;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingPO;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingSubTaskPO;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingTaskPO;
import com.yhl.scp.mps.dynamicDeliveryTracking.service.DynamicDeliveryTrackingSubTaskService;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingSubTaskVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingTaskVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>DynamicDeliveryTrackingJcExcelSupport</code>
 * <p>
 * 动态追踪导入支持类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-04 11:02:39
 */
@Service
public class DynamicDeliveryTrackingExcelSupport {

    @Resource
    private DynamicDeliveryTrackingSubTaskService dynamicDeliveryTrackingSubTaskService;
    @Resource
    private DynamicDeliveryTrackingDao dynamicDeliveryTrackingDao;
    @Resource
    private DynamicDeliveryTrackingTaskDao dynamicDeliveryTrackingTaskDao;
    @Resource
    private DynamicDeliveryTrackingSubTaskDao dynamicDeliveryTrackingSubTaskDao;
    @Resource
    private IpsFeign ipsFeign;
    @Resource
    private MdsFeign mdsFeign;

    public void processStep(Map<String, DynamicDeliveryTrackingTaskVO> taskVOMap,
                            String stepName,
                            String resourceId,
                            BigDecimal qty,
                            Date startTime,
                            List<DataImportInfo> importLogList,
                            List<DynamicDeliveryTrackingTaskPO> taskInsertList,
                            Map<String, List<DynamicDeliveryTrackingTaskVO>> taskMap,
                            List<DynamicDeliveryTrackingSubTaskPO> subTaskInsertList,
                            String trackingId,
                            Integer rowIndex) {
        if (StringUtils.isEmpty(resourceId) || qty == null || startTime == null) {
            return;
        }

        DynamicDeliveryTrackingTaskVO taskVO = taskVOMap.get(stepName);
        if (taskVO == null) {
            // 特殊情况：压制可能对应成型
            if ("压制".equals(stepName)) {
                taskVO = taskVOMap.get("成型");
            }
            if (taskVO == null) {
                return;
            }
        }

        RoutingStepResourceDO routingStepResourceDO = taskVO.getRoutingStepResourceDOList().stream()
                .filter(t -> resourceId.equals(t.getPhysicalResourceId()))
                .findFirst()
                .orElse(null);

        if (routingStepResourceDO == null) {
            String remark = "行数：" + rowIndex + ";" + stepName + "资源在候选资源中不存在";
            logAndAddError(importLogList, remark, rowIndex);
        } else {
            setupAndAddTask(taskVO, trackingId, resourceId, qty, startTime, taskInsertList, taskMap, subTaskInsertList);
        }
    }

    private void logAndAddError(List<DataImportInfo> importLogList, String remark, int rowIndex) {
        DataImportInfo dataImportInfo = new DataImportInfo();
        dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
        dataImportInfo.setRemark(remark);
        dataImportInfo.setDisplayIndex(rowIndex);
        importLogList.add(dataImportInfo);
    }

    private void setupAndAddTask(DynamicDeliveryTrackingTaskVO taskVO,
                                 String trackingId,
                                 String resourceId,
                                 BigDecimal qty,
                                 Date startTime,
                                 List<DynamicDeliveryTrackingTaskPO> taskInsertList,
                                 Map<String, List<DynamicDeliveryTrackingTaskVO>> taskMap,
                                 List<DynamicDeliveryTrackingSubTaskPO> subTaskInsertList) {
        taskVO.setId(UUIDUtil.getUUID());
        taskVO.setDynamicDeliveryTrackingId(trackingId);
        taskVO.setPhysicalResourceId(resourceId);
        taskVO.setQuantity(qty);
        taskVO.setStartTime(startTime);

        generateSubTask(taskVO, subTaskInsertList);
        DynamicDeliveryTrackingTaskPO taskPO = DynamicDeliveryTrackingTaskConvertor.INSTANCE.vo2Po(taskVO);
        taskInsertList.add(taskPO);
        DynamicDeliveryTrackingTaskVO newTaskVO = new DynamicDeliveryTrackingTaskVO();
        BeanUtils.copyProperties(taskVO, newTaskVO);

        taskMap.computeIfAbsent(trackingId, k -> new ArrayList<>()).add(newTaskVO);
    }

    private void generateSubTask(DynamicDeliveryTrackingTaskVO taskVO, List<DynamicDeliveryTrackingSubTaskPO> subTaskInsertList) {
        List<DynamicDeliveryTrackingSubTaskVO> subTaskVOS = dynamicDeliveryTrackingSubTaskService.generateSubTask(taskVO);
        subTaskVOS.stream().max(Comparator.comparing(DynamicDeliveryTrackingSubTaskVO::getEndTime))
                .ifPresent(taskPO -> taskVO.setEndTime(taskPO.getEndTime()));
        if (CollectionUtils.isNotEmpty(subTaskVOS)) {
            subTaskInsertList.addAll(DynamicDeliveryTrackingSubTaskConvertor.INSTANCE.vo2Pos(subTaskVOS));
        }
    }

    public ImportRelatedDataHolder<DynamicDeliveryTrackingPO> getRelatedDataHolder() {
        List<DynamicDeliveryTrackingPO> alreadyExitData = dynamicDeliveryTrackingDao.selectByParams(new HashMap<>());
        Map<String, DynamicDeliveryTrackingPO> existingDataMap = alreadyExitData.stream().collect(Collectors.toMap(x ->
                        x.getProductCode() + "&" + DateUtils.dateToString(x.getDeliveryTime(), DateUtils.COMMON_DATE_STR4),
                Function.identity(), (v1, v2) -> v1));
        // 组成唯一键的字段
        List<String> uniqueKeys = ListUtil.of("productCode", "deliveryTime");
        // 外键字段
        List<String> foreignKeys = ListUtil.of();
        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();
        return ImportRelatedDataHolder.<DynamicDeliveryTrackingPO>builder()
                .existingData(alreadyExitData)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .existingDataMap(existingDataMap)
                .build();
    }


    public void doDataProcess(List<DynamicDeliveryTrackingPO> insertList,
                              List<DynamicDeliveryTrackingPO> updateList,
                              List<DynamicDeliveryTrackingSubTaskPO> subTaskInsertList,
                              List<String> trackingIds,
                              List<DynamicDeliveryTrackingPO> deteleList,
                              List<DynamicDeliveryTrackingTaskPO> taskInsertList) {

        subTaskInsertList.forEach(t ->
                {
                    t.setId(UUIDUtil.getUUID());
                }
        );
        deleteSourceTaskAndSubTask(trackingIds);

        if (CollectionUtils.isNotEmpty(deteleList)) {
            List<String> ids = deteleList.stream().map(DynamicDeliveryTrackingPO::getId).collect(Collectors.toList());
            dynamicDeliveryTrackingDao.deleteBatch(ids);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            BasePOUtils.insertBatchFiller(insertList);
            dynamicDeliveryTrackingDao.insertBatch(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            BasePOUtils.updateBatchFiller(updateList);
            dynamicDeliveryTrackingDao.updateBatch(updateList);
        }
        if (CollectionUtils.isNotEmpty(taskInsertList)) {
            BasePOUtils.insertBatchFiller(taskInsertList);
            dynamicDeliveryTrackingTaskDao.insertBatch(taskInsertList);
        }
        if (CollectionUtils.isNotEmpty(subTaskInsertList)) {
            BasePOUtils.insertBatchFiller(subTaskInsertList);
            dynamicDeliveryTrackingSubTaskDao.insertBatch(subTaskInsertList);
        }
    }

    private void deleteSourceTaskAndSubTask(List<String> trackingIds) {
        if (CollectionUtils.isNotEmpty(trackingIds)) {
            List<DynamicDeliveryTrackingTaskPO> sourceDataList =
                    dynamicDeliveryTrackingTaskDao.selectByParams(ImmutableMap.of("trackingIdList", trackingIds));
            if (CollectionUtils.isNotEmpty(sourceDataList)) {
                List<String> taskIds = sourceDataList.stream().map(BasePO::getId).collect(Collectors.toList());
                dynamicDeliveryTrackingTaskDao.deleteBatch(taskIds);
                dynamicDeliveryTrackingSubTaskDao.deleteByTaskIds(taskIds);
            }
        }
    }


    public String getWarehousedOperation() {
        // 进仓工序
        return ipsFeign.getByCollectionCode("WAREHOUSING_PROCESS").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElse(null);
    }

    public String getWarehousedOperationResource() {
        return ipsFeign.getByCollectionCode("INBOUND_PRODUCTION_LINE").stream()
                .map(CollectionValueVO::getCollectionValue).filter(StrUtil::isNotBlank).findFirst()
                .flatMap(collectionValue -> {
                    List<PhysicalResourceVO> resources = mdsFeign.getPhysicalResourceParams(
                            ImmutableMap.of("physicalResourceCode", collectionValue));
                    return resources != null && !resources.isEmpty()
                            ? Optional.of(resources.get(0).getId()) : Optional.empty();
                }).orElse(null);
    }


}
