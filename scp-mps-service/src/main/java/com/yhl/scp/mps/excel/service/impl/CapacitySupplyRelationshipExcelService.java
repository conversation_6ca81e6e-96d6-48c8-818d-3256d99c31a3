package com.yhl.scp.mps.excel.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.common.excel.*;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.excel.service.AbstractExcelService;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.basic.routing.vo.StandardStepBasicVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mps.capacityBalance.convertor.CapacitySupplyRelationshipConvertor;
import com.yhl.scp.mps.capacityBalance.dto.CapacitySupplyRelationshipDTO;
import com.yhl.scp.mps.capacityBalance.enums.CapacityBalanceRule;
import com.yhl.scp.mps.capacityBalance.enums.LockStatusEnum;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacitySupplyRelationshipDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacitySupplyRelationshipPO;
import com.yhl.scp.mps.capacityBalance.service.CapacityLoadService;
import com.yhl.scp.mps.capacityBalance.service.CapacitySupplyRelationshipService;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import com.yhl.scp.mps.enums.SupplyModelEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>CapacitySupplyRelationshipExcelService</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-19 16:19:10
 */
@Service
public class CapacitySupplyRelationshipExcelService extends AbstractExcelService<CapacitySupplyRelationshipDTO, CapacitySupplyRelationshipPO, CapacitySupplyRelationshipVO> {

    @Resource
    private CapacitySupplyRelationshipService capacitySupplyRelationshipService;

    @Resource
    private CapacitySupplyRelationshipDao capacitySupplyRelationshipDao;

    @Resource
    private CapacityLoadService capacityLoadService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Override
    public BaseDao<CapacitySupplyRelationshipPO, CapacitySupplyRelationshipVO> getBaseDao() {
        return capacitySupplyRelationshipDao;
    }

    @Override
    public Function<CapacitySupplyRelationshipDTO, CapacitySupplyRelationshipPO> getDTO2POConvertor() {
        return CapacitySupplyRelationshipConvertor.INSTANCE::dto2Po;
    }

    @Override
    public Class<CapacitySupplyRelationshipDTO> getDTOClass() {
        return CapacitySupplyRelationshipDTO.class;
    }

    @Override
    public BaseService<CapacitySupplyRelationshipDTO, CapacitySupplyRelationshipVO> getBaseService() {
        return capacitySupplyRelationshipService;
    }

    @Override
    protected void fillIdForUpdateData(List<CapacitySupplyRelationshipDTO> updateList, Map<String, CapacitySupplyRelationshipPO> existingDataMap) {
        //全是新增，没有更新数据
    }
    protected void classificationData(ImportContext importContext,
                                      List<CapacitySupplyRelationshipDTO> excelData,
                                      ImportAnalysisResultHolder<CapacitySupplyRelationshipDTO, CapacitySupplyRelationshipPO> resultHolder,
                                      ImportRelatedDataHolder<CapacitySupplyRelationshipPO> relatedDataHolder) {
        List<CapacitySupplyRelationshipPO> databaseDataList = relatedDataHolder.getExistingData();
        List<String> mainKeys = relatedDataHolder.getMainKeys();
        ImportTypeEnum importType = importContext.getImportType();
        List<String> foreignKeys = relatedDataHolder.getForeignKeys();
        Map<String, List<SimpleVO>> foreignDataMap = relatedDataHolder.getForeignDataMap();
        Map<String, CapacitySupplyRelationshipPO> dataMap = relatedDataHolder.getExistingDataMap();

        List<DataImportInfo> importLogList = resultHolder.getImportLogList();

        List<CapacitySupplyRelationshipDTO> toInsertList = new ArrayList<>();
        List<CapacitySupplyRelationshipDTO> toUpdateList = new ArrayList<>();
        List<CapacitySupplyRelationshipPO> toDeleteList = new ArrayList<>();
        //导入数据全部视为新增
        toInsertList.addAll(excelData);
        resultHolder.setUpdateList(toUpdateList);
        resultHolder.setInsertList(toInsertList);
        resultHolder.setDeleteList(toDeleteList);
    }

    @Override
    protected List<CapacitySupplyRelationshipDTO> transferToDTOData(MultipartFile file, ImportContext importContext, List<DataImportInfo> importLogList) {

        BaseListener<CapacitySupplyRelationshipDTO> listener = new BaseListener<>(getDTOClass(), importContext.getImportType().getCode());
        List<DataImportInfo> dataImportInfoList = EasyExcelUtils.importExcel(file, getDTOClass(), listener);
        String objectType = importContext.getImportExcelStrategy().getCode();
        if (com.yhl.platform.common.utils.CollectionUtils.isNotEmpty(dataImportInfoList)) {
            String batchNo = UUIDUtil.getUUID();
            dataImportInfoList.forEach(item -> {
                item.setBatchNo(batchNo);
                item.setObjectType(objectType);
            });
            importLogList.addAll(dataImportInfoList);
        }
        // 总条数
        Integer allCount = listener.allCount;
        // 生成导入汇总信息
        DataImportInfo dataImportInfo = new DataImportInfo();
        dataImportInfo.setInfoType(ImportDataTypeEnum.GATHER.getCode());
        // 备注总条数
        dataImportInfo.setRemark(String.valueOf(allCount));
        importLogList.add(dataImportInfo);
        // 合法数据
        return listener.getData();
    }

    @Override
    protected void afterInsert(ImportAnalysisResultHolder<CapacitySupplyRelationshipDTO, CapacitySupplyRelationshipPO> resultHolder, ImportContext importContext) {
        //重新计算产能负荷
        capacityLoadService.saveCapacityLoadBasedOnVersionNew();
    }

    @Override
    protected ImportRelatedDataHolder<CapacitySupplyRelationshipPO> prepareData(List<CapacitySupplyRelationshipDTO> capacitySupplyRelationshipDTOS) {
        List<CapacitySupplyRelationshipPO> alreadyExitData = capacitySupplyRelationshipDao.selectLatestData(null);
        String planPeriod;
        String versionCode;
        //一批数据里面它们的业务预测计划周期和版本是相同的
        if (CollectionUtils.isNotEmpty(alreadyExitData)){
            planPeriod = alreadyExitData.get(0).getPlanPeriod();
            versionCode = alreadyExitData.get(0).getVersionCode();
        } else {
            planPeriod = null;
            versionCode = null;
        }
        //导入数据锁定状态默认为未锁定，规则默认为手工修改
        capacitySupplyRelationshipDTOS.forEach(t->{
            t.setLockStatus(LockStatusEnum.UNLOCKED.getCode());
            t.setRule(CapacityBalanceRule.manualModification.getCode());
            if (SupplyModelEnum.LOCAL.getDesc().equals(t.getSupplyModel())){
                t.setSupplyModel(SupplyModelEnum.LOCAL.getCode());
            }else if (SupplyModelEnum.OUTSOURCED.getDesc().equals(t.getSupplyModel())){
                t.setSupplyModel(SupplyModelEnum.OUTSOURCED.getCode());
            }
            t.setImportFlag(YesOrNoEnum.YES.getCode());
            t.setPlanPeriod(planPeriod);
            t.setVersionCode(versionCode);
        });

        // 组成唯一键的字段
        List<String> uniqueKeys = ListUtil.of("vehicleModelCode", "productCode", "forecastTime","operationCode", "resourceCode", "supplyModel","supplyTime","beat");
        List<String> foreignKeys = ListUtil.empty();
        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();
        Map<String, CapacitySupplyRelationshipPO> existingDataMap = new HashMap<>();
        return ImportRelatedDataHolder.<CapacitySupplyRelationshipPO>builder()
                .existingData(alreadyExitData)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .existingDataMap(existingDataMap)
                .build();
    }

    @Override
    protected void specialVerification(ImportAnalysisResultHolder<CapacitySupplyRelationshipDTO, CapacitySupplyRelationshipPO> resultHolder, ImportContext importContext) {
        List<DataImportInfo> importLogList = resultHolder.getImportLogList();
        List<CapacitySupplyRelationshipDTO> insertList = resultHolder.getInsertList();

        String mdsScenario = getMdsScenario();

        List<StandardResourceVO> resourceTimeVOS = newMdsFeign.selectResourceAndOperation();
        Map<String, List<StandardResourceVO>> resourceTimeMap = resourceTimeVOS.stream().collect(Collectors.groupingBy(StandardResourceVO::getStandardResourceCode));
        //设备对于生产组织
        Map<String, StandardResourceVO> resourceAndOrgMap = resourceTimeVOS.stream().collect(Collectors.toMap(StandardResourceVO::getStandardResourceCode, Function.identity()));
        //标准工艺数据
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(mdsScenario);

        Map<String, String> standardStepMap = standardStepVOS.stream().collect(Collectors.toMap(t -> t.getStockPointCode() + "-" + t.getStandardStepCode(), StandardStepBasicVO::getStandardStepName));
        Iterator<CapacitySupplyRelationshipDTO> iterator = insertList.iterator();
        while (iterator.hasNext()){
            CapacitySupplyRelationshipDTO dto = iterator.next();

            String remark = null;
            boolean error = false;
            if (!resourceAndOrgMap.containsKey(dto.getResourceCode())){

                remark = "行数：" + dto.getRowIndex() + ";设备编码"+dto.getResourceCode()+"不存在";
                error = true;

                iterator.remove();
            }else {
                dto.setResourceName(resourceAndOrgMap.get(dto.getResourceCode()).getStandardResourceName());
            }
            if (!resourceTimeMap.containsKey(dto.getResourceCode())){
                remark = "行数：" + dto.getRowIndex() + ";导入设备"+dto.getResourceCode()+"工序编码"+dto.getOperationCode()+"不存在";
                error = true;
            }else {
                StandardResourceVO standardResourceVO = resourceAndOrgMap.get(dto.getResourceCode());
                String key = standardResourceVO.getOrganizationCode() + "-" + dto.getOperationCode();
                if (!standardStepMap.containsKey(key)){
                    remark = "行数：" + dto.getRowIndex() + ";导入设备"+dto.getResourceCode()+"工序编码"+dto.getOperationCode()+"不存在";
                    error = true;
                }else {
                    dto.setOperationName(standardStepMap.get(key));
                }
            }
            if (error){
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark(remark);
                dataImportInfo.setDisplayIndex(dto.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
            }
        }

    }

    private String getMdsScenario() {
        return ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode()).getData();
    }

    @Override
    public void exportTemplate(HttpServletResponse response, String excelStrategy) {
        super.exportTemplate(response, excelStrategy);
    }
}
