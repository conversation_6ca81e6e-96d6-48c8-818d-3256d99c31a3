package com.yhl.scp.mps.capacityBalance.domain.factory;

import com.yhl.scp.mps.capacityBalance.domain.entity.CapacitySupplyRelationshipDO;
import com.yhl.scp.mps.capacityBalance.dto.CapacitySupplyRelationshipDTO;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacitySupplyRelationshipDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>CapacitySupplyRelationshipFactory</code>
 * <p>
 * 产能供应关系领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 15:41:29
 */
@Component
public class CapacitySupplyRelationshipFactory {

    @Resource
    private CapacitySupplyRelationshipDao capacitySupplyRelationshipDao;

    CapacitySupplyRelationshipDO create(CapacitySupplyRelationshipDTO dto) {
        // TODO
        return null;
    }

}
