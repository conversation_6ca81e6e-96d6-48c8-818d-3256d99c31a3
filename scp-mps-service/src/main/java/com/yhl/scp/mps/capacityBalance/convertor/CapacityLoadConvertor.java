package com.yhl.scp.mps.capacityBalance.convertor;

import com.yhl.scp.mps.capacityBalance.domain.entity.CapacityLoadDO;
import com.yhl.scp.mps.capacityBalance.dto.CapacityLoadDTO;
import com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacityLoadPO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>CapacityLoadConvertor</code>
 * <p>
 * 产能负荷转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 15:40:31
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CapacityLoadConvertor {

    CapacityLoadConvertor INSTANCE = Mappers.getMapper(CapacityLoadConvertor.class);

    CapacityLoadDO dto2Do(CapacityLoadDTO obj);

    CapacityLoadDTO do2Dto(CapacityLoadDO obj);

    List<CapacityLoadDO> dto2Dos(List<CapacityLoadDTO> list);

    List<CapacityLoadDTO> do2Dtos(List<CapacityLoadDO> list);

    CapacityLoadVO do2Vo(CapacityLoadDO obj);

    CapacityLoadVO po2Vo(CapacityLoadPO obj);

    List<CapacityLoadVO> po2Vos(List<CapacityLoadPO> list);

    CapacityLoadPO do2Po(CapacityLoadDO obj);

    CapacityLoadDO po2Do(CapacityLoadPO obj);

    CapacityLoadPO dto2Po(CapacityLoadDTO obj);

    List<CapacityLoadPO> dto2Pos(List<CapacityLoadDTO> obj);

}
