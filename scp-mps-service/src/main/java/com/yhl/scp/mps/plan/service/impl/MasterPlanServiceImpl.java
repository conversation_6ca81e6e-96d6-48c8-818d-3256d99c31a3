package com.yhl.scp.mps.plan.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.beust.jcommander.internal.Sets;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.EnumUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.platform.component.external.service.ExtensionService;
import com.yhl.scp.biz.common.util.PageUtils;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.resource.enums.ResourceCategoryEnum;
import com.yhl.scp.mds.basic.routing.vo.BomRoutingStepInputBasicVO;
import com.yhl.scp.mds.basic.routing.vo.StandardStepBasicVO;
import com.yhl.scp.mds.curingTime.vo.MasterPlanCuringTimeVO;
import com.yhl.scp.mds.extension.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.mds.extension.organization.vo.ProductionOrganizationVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.extension.routing.infrastructure.po.StandardStepPO;
import com.yhl.scp.mds.extension.routing.vo.BomRoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepResourceVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productBox.vo.ProductBoxRelationVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mds.routing.vo.ProductCandidateResourceVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.algorithm.strategy.impl.CancelPlanCommand;
import com.yhl.scp.mps.cache.service.CacheDelService;
import com.yhl.scp.mps.cache.service.CacheGetService;
import com.yhl.scp.mps.cache.service.CacheSetService;
import com.yhl.scp.mps.demand.vo.OperationEarilWarningInfoVO;
import com.yhl.scp.mps.dispatch.enums.OperationEnum;
import com.yhl.scp.mps.domain.dispatch.IScheduleExtended;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import com.yhl.scp.mps.model.service.MoldChangeTimeService;
import com.yhl.scp.mps.model.vo.MoldChangeTimeVO;
import com.yhl.scp.mps.order.dto.WorkOrderDeletionDTO;
import com.yhl.scp.mps.order.service.WorkOrderDeletionService;
import com.yhl.scp.mps.plan.convertor.MasterPlanConvertor;
import com.yhl.scp.mps.plan.domain.entity.MasterPlanDO;
import com.yhl.scp.mps.plan.domain.service.MasterPlanDomainService;
import com.yhl.scp.mps.plan.dto.MasterPlanDTO;
import com.yhl.scp.mps.plan.dto.UpdateDueDateDTO;
import com.yhl.scp.mps.plan.dto.UpdateRemarkDTO;
import com.yhl.scp.mps.plan.infrastructure.dao.DeliveryPlanOverviewDao;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanDao;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanExtDao;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.infrastructure.po.MasterPlanPO;
import com.yhl.scp.mps.plan.req.MasterPlanReq;
import com.yhl.scp.mps.plan.service.MasterPlanRelationService;
import com.yhl.scp.mps.plan.service.MasterPlanService;
import com.yhl.scp.mps.plan.vo.*;
import com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.dao.SubInventoryCargoLocationDao;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import com.yhl.scp.mps.util.LabelValueThree;
import com.yhl.scp.sds.basic.enums.KitStatusEnum;
import com.yhl.scp.sds.basic.order.vo.OperationTaskBasicVO;
import com.yhl.scp.sds.extension.feedback.vo.FeedbackProductionVO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import com.yhl.scp.sds.extension.order.vo.OperationTaskVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.feedback.service.FeedbackProductionService;
import com.yhl.scp.sds.order.convertor.OperationConvertor;
import com.yhl.scp.sds.order.infrastructure.dao.WorkOrderDao;
import com.yhl.scp.sds.order.service.OperationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MasterPlanServiceImpl</code>
 * <p>
 * 主生产计划表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-26 13:52:51
 */
@Slf4j
@Service
public class MasterPlanServiceImpl extends AbstractService implements MasterPlanService {

    @Resource
    private MasterPlanDao masterPlanDao;
    @Resource
    private MasterPlanDomainService masterPlanDomainService;
    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private OperationService operationService;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private WorkOrderDao workOrderDao;
    @Resource
    private MasterPlanExtDao operationExtDao;
    @Resource
    private MoldChangeTimeService moldChangeTimeService;
    @Resource
    private OperationTaskExtDao operationTaskExtDao;
    @Resource
    private CancelPlanCommand cancelPlanCommand;
    @Resource
    private SubInventoryCargoLocationDao subInventoryCargoLocationDao;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private MasterPlanRelationService masterPlanRelationService;
    @Resource
    private DeliveryPlanOverviewDao deliveryPlanOverviewDao;
    @Resource
    private WorkOrderDeletionService workOrderDeletionService;
    @Resource
    private IpsFeign ipsFeign;
    @Resource
    private CacheSetService cacheSetService;

    public static final String WARNING_COLOUR = "yellow";
    public static final String ERROR_COLOUR = "red";
    public static final String PARAM_STOCK_POINT_TYPE = "stockPointType";
    public static final String PARAM_PRODUCT_CODES = "productCodes";
    public static final String PARAM_PRODUCT_CODE_LIST = "productCodeList";

    public static final String NORMAL_OPERATION = "NORMEAL_PROCESS";
    public static final String FORMING_PROCESS = "FORMING_PROCESS";
    public static final String COATING_PROCESS = "COATING_PROCESS";

    public static final String PRE_OPERATION = "预处理";
    public static final String PAINTING_OPERATION = "印刷";
    public static final String FORMING_OPERATION = "成型";
    public static final String MERGING_OPERATION = "合片";
    public static final String PACKAGING_OPERATION = "包装";

    public static final String FIELD_FULFILLMENT_STATUS = "fulfillmentStatus";
    public static final String FIELD_DEMAND_TIME = "demandTime";
    public static final String FIELD_DEMAND_QTY = "demandQuantity";
    public static final String FIELD_SUPPLY_QTY = "supplyQty";
    public static final String FIELD_BLOCK_COLOUR = "blockColour";

    public static final String BLUE_COLOUR = "BLUE";
    public static final String GREEN_COLOUR = "GREEN";
    public static final String RED_COLOUR = "RED";
    public static final String EMPTY_COLOUR = "EMPTY";

    public static final String SUB_INVENTORY_CPSJ = "CPSJ";

    @Resource
    private IScheduleExtended scheduleExtended;

    @Resource
    private FeedbackProductionService feedbackProductionService;

    @Resource
    private CacheGetService cacheGetService;

    @Resource
    private CacheDelService cacheDelService;
    private ExtensionService extensionService;

    private static String getDelayInfo(MasterPlanTaskVO masterOperationTaskVO, WorkOrderPO workOrderPO) {
        if (Objects.nonNull(workOrderPO) && Objects.nonNull(workOrderPO.getDueDate()) && Objects.nonNull(masterOperationTaskVO) && Objects.nonNull(masterOperationTaskVO.getEndTime())) {
            if (workOrderPO.getDueDate().getTime() < masterOperationTaskVO.getEndTime().getTime()) {
                return "延期";
            } else {
                return "未延期";
            }
        }
        return "未延期";
    }

    /**
     * 组装工序任务迭代器
     *
     * @param operationTasks  工序任务
     * @param operationMap    工序MAP
     * @param stepResourceMap 工序资源MAP
     */
    private static void taskAssembleLoop(List<MasterPlanTaskVO> operationTasks, Map<String, OperationPO> operationMap, Map<String, RoutingStepResourceVO> stepResourceMap) {
        // 设置UUID
        operationTasks.forEach(x -> x.setUuid(UUIDUtil.getUUID()));
        // 按物品分组获取成型工序最早工序任务
        Map<String, MasterPlanTaskVO> collect = operationTasks.stream().filter(x -> Objects.nonNull(x.getProductionStartTime()) && StringUtils.isNotBlank(x.getProductStockPointCode())).filter(x -> FORMING_PROCESS.equals(x.getStandardStepType())).collect(Collectors.groupingBy(MasterPlanTaskVO::getProductStockPointCode, Collectors.collectingAndThen(Collectors.toList(), list -> list.stream().min(Comparator.comparing(MasterPlanTaskVO::getProductionStartTime)).orElse(new MasterPlanTaskVO()))));
        for (MasterPlanTaskVO operationTaskVO : operationTasks) {
            String operationId = operationTaskVO.getOperationId();
            OperationPO operationPO = operationMap.get(operationId);
            if (Objects.isNull(operationPO) || StringUtils.isBlank(operationPO.getOrderId())) {
                continue;
            }
            // 工序任务对应的子工序
            OperationVO operationVO = OperationConvertor.INSTANCE.po2Vo(operationPO);
            operationTaskVO.setOperationVO(operationVO);
            String routingStepId = operationVO.getRoutingStepId();
            String physicalResourceId = operationTaskVO.getPhysicalResourceId();
            String key = String.join("&", routingStepId, physicalResourceId);
            BigDecimal quantity = operationVO.getQuantity();
            if (stepResourceMap.containsKey(key)) {
                // BigDecimal unitProductionTime = stepResourceMap.get(key).getUnitProductionTime();
                // BigDecimal productionTime = BigDecimalUtils.multiply(quantity, unitProductionTime, 0);
                // 设置生产时间
                // operationTaskVO.setProductionTime(productionTime);
                // 设置生产时间
                long time = operationTaskVO.getProductionEndTime().getTime() - operationTaskVO.getProductionStartTime().getTime();
                BigDecimal productionTime = new BigDecimal(time).divide(new BigDecimal(1000), RoundingMode.UP);
                operationTaskVO.setProductionTime(productionTime);
                // 设置单位制造时间
                // operationTaskVO.setUnitHour(unitProductionTime);
            }
            String productStockPointCode = operationTaskVO.getProductStockPointCode();
            String uuid = operationTaskVO.getUuid();
            operationTaskVO.setFirstInEachProduct(false);
            if (collect.containsKey(productStockPointCode) && uuid.equals(collect.get(productStockPointCode).getUuid())) {
                operationTaskVO.setFirstInEachProduct(true);
            }
        }
    }

    private static void operationSetter(List<MasterPlanTaskVO> operationTasks, Map<String, OperationPO> operationMap) {
        for (MasterPlanTaskVO operationTaskVO : operationTasks) {
            String operationId = operationTaskVO.getOperationId();
            OperationPO operationPO = operationMap.get(operationId);
            // 工序任务对应的子工序
            OperationVO operationVO = OperationConvertor.INSTANCE.po2Vo(operationPO);
            operationTaskVO.setOperationVO(operationVO);
        }
    }

    public static List<InventoryBatchDetailVO> getFinishInventory(List<InventoryBatchDetailVO> inventoryBatchDetails, Map<String, SubInventoryCargoLocationVO> cargoLocationMap) {
        if (CollectionUtils.isEmpty(inventoryBatchDetails)) {
            return new ArrayList<>();
        }
        return inventoryBatchDetails.stream().filter(p -> {
            String freightSpace = p.getFreightSpace();
            SubInventoryCargoLocationVO subInventoryCargoLocation = cargoLocationMap.get(freightSpace);
            return null != subInventoryCargoLocation;
        }).collect(Collectors.toList());
    }

    @Override
    public void doBatchCancelPlan(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException("参数为空！");
        }
        List<OperationVO> operationVOS = operationService.selectByParams(ImmutableMap.of("ids", ids));
        cancelPlanCommand.doCancelAmsOperationPlan(operationVOS);
        log.info("工单取消计划清除数据完成");
    }

    @Override
    public void doBatchClose(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException("请选择关闭得数据");
        }
        // 查询需要关闭删除得制造订单ids
        List<String> deleteOrderIds = operationExtDao.selectDeleteWorkOrder(ids);
        if (CollectionUtils.isEmpty(deleteOrderIds)) {
            throw new BusinessException("未找到有效得关闭订单id");
        }
        handleDeleteWorkOrder(deleteOrderIds);
        scheduleExtended.doClosePlan(deleteOrderIds);
    }

    private void handleDeleteWorkOrder(List<String> deleteWorkOrderIds) {
        List<WorkOrderPO> workOrderPOS = workOrderDao.selectByParams(ImmutableMap.of("ids", deleteWorkOrderIds));
        List<WorkOrderDeletionDTO> workOrderDeletionPOS = new ArrayList<>();
        for (WorkOrderPO workOrderPO : workOrderPOS) {
            WorkOrderDeletionDTO workOrderDeletionPO = new WorkOrderDeletionDTO();
            BeanUtils.copyProperties(workOrderPO, workOrderDeletionPO);
            workOrderDeletionPO.setRemark(workOrderPO.getRemark() + "-计划关闭删除订单");
            workOrderDeletionPOS.add(workOrderDeletionPO);
        }
        workOrderDeletionService.doCreateBatch(workOrderDeletionPOS);
        log.info("记录删除制造订单deletion数量：{}", workOrderDeletionPOS.size());
    }

    @Override
    public BaseResponse<Void> doCreate(MasterPlanDTO masterPlanDTO) {
        // 0.数据转换
        MasterPlanDO masterPlanDO = MasterPlanConvertor.INSTANCE.dto2Do(masterPlanDTO);
        MasterPlanPO masterPlanPO = MasterPlanConvertor.INSTANCE.dto2Po(masterPlanDTO);
        // 1.数据校验
        masterPlanDomainService.validation(masterPlanDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(masterPlanPO);
        masterPlanDao.insertWithPrimaryKey(masterPlanPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MasterPlanDTO masterPlanDTO) {
        // 0.数据转换
        MasterPlanDO masterPlanDO = MasterPlanConvertor.INSTANCE.dto2Do(masterPlanDTO);
        MasterPlanPO masterPlanPO = MasterPlanConvertor.INSTANCE.dto2Po(masterPlanDTO);
        // 1.数据校验
        masterPlanDomainService.validation(masterPlanDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(masterPlanPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MasterPlanDTO> list) {
        List<MasterPlanPO> newList = MasterPlanConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        masterPlanDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<MasterPlanDTO> list) {
        List<MasterPlanPO> newList = MasterPlanConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        masterPlanDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return masterPlanDao.deleteBatch(idList);
        }
        return masterPlanDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MasterPlanVO selectByPrimaryKey(String id) {
        MasterPlanPO po = masterPlanDao.selectByPrimaryKey(id);
        return MasterPlanConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mps_master_plan")
    public List<MasterPlanVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mps_master_plan")
    public List<MasterPlanVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MasterPlanVO> dataList = masterPlanDao.selectByCondition(sortParam, queryCriteriaParam);
        MasterPlanServiceImpl target = SpringBeanUtils.getBean(MasterPlanServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MasterPlanVO> selectByParams(Map<String, Object> params) {
        List<MasterPlanPO> list = masterPlanDao.selectByParams(params);
        return MasterPlanConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MasterPlanVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MASTER_PLAN.getCode();
    }

    @Override
    public List<MasterPlanVO> invocation(List<MasterPlanVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public PageInfo<MasterPlanWorkOrderBodyVO> masterPlanWorkOrder(Pagination pagination, MasterPlanReq masterPlanReq) {
        String scenario = SystemHolder.getScenario();
        String userId = SystemHolder.getUserId();

        // Executor for initial and later I/O bound tasks (data fetching)
        ExecutorService ioExecutor = Executors.newFixedThreadPool(20);
        // Executor for initial and later CPU bound tasks (data processing)
        ExecutorService cpuExecutor = Executors.newFixedThreadPool(6);
        try {
            // --- Step 0: Launch Initial Query and Setup ---
            PlanningHorizonVO planningHorizon = newMdsFeign.selectPlanningHorizon(scenario);
            assembleQueryRequest(masterPlanReq, planningHorizon, true, scenario);

            // --- Step 1: Launch Parallel Queries (on ioExecutor) ---
            CompletableFuture<List<MasterPlanTaskVO>> operationTasksFuture = getOperationTasksFuture(masterPlanReq, scenario, ioExecutor);
            CompletableFuture<List<OperationPlanVO>> unPlanOperationsFuture = getUnPlanOperationsFuture(masterPlanReq, scenario, ioExecutor);
            CompletableFuture.allOf(operationTasksFuture, unPlanOperationsFuture).join();

            // --- Step 2: Get Initial Results ---
            List<MasterPlanTaskVO> operationTasks = operationTasksFuture.join();
            List<OperationPlanVO> unPlanOperations = unPlanOperationsFuture.join();

            // --- Step 3: Perform Initial Checks ---
            if (CollectionUtils.isEmpty(masterPlanReq.getPhysicalResourceIds())) {
                log.warn("No physical resources found after assembling request. Returning empty page.");
                return new PageInfo<>();
            }
            if (CollectionUtils.isEmpty(operationTasks) && CollectionUtils.isEmpty(unPlanOperations)) {
                log.warn("No planned tasks or unplanned operations found. Returning empty page.");
                return new PageInfo<>();
            }

            Map<String, PhysicalResourceVO> mainResourceMap = CollectionUtils.isEmpty(masterPlanReq.getPhysicalResources()) ? new HashMap<>() : masterPlanReq.getPhysicalResources().stream().collect(Collectors.toMap(PhysicalResourceVO::getId, Function.identity(), (v1, v2) -> v1));

            // CACHING HW_LIMIT_STAND_RESOURCE_CODE
            String continuousFurnace = cacheGetService.getHwLimitStandResourceCode(scenario);
            // CACHING TOOL_RESOURCE_MAP
            Map<String, PhysicalResourceVO> toolResourceMap = cacheGetService.getToolResourceMap(scenario);
            // CACHING WORK_ORDER_MAP
            Map<String, WorkOrderPO> workOrderMap = cacheGetService.getWorkOrderMap(scenario);
            // CACHING COMPREHENSIVE_YIELD_MAP
            List<WorkOrderPO> workOrders = new ArrayList<>(workOrderMap.values());
            Map<String, BigDecimal> orderId2ComprehensiveYieldMap = cacheGetService.getOrderId2ComprehensiveYieldMap(workOrders, scenario);
            // CACHING STANDARD_STEP_MAP
            Map<String, String> standardStepMap = cacheGetService.getStandardStepMap(scenario);
            // CACHING NEW_STOCK_POINT_LIST
            List<NewStockPointVO> newStockPoints = cacheGetService.getNewStockPoints(scenario);

            // --- Step 4: Launch *All* Dependent Data Fetches ---

            List<String> operationIds = operationTasks.stream().map(OperationTaskVO::getOperationId).distinct().collect(Collectors.toList());

            if (Boolean.FALSE.equals(masterPlanReq.getUseCache())) {
                // 清空缓存
                cacheDelService.delMasterPlanCache(scenario, userId);
            }
            log.info("Launching dependent data fetches...");
            CompletableFuture<Map<String, StandardResourceVO>> standardResourceMapFuture = getStandardResourceMapFuture(mainResourceMap, scenario, userId, ioExecutor);

            CompletableFuture<Map<String, String>> operationId2ToolResourceIdMapFuture = getOperationId2ToolResourceIdMapFuture(operationIds, scenario, userId, ioExecutor);
            CompletableFuture<Map<String, OperationPO>> operationMapFuture = getOperationMapFuture(operationIds, scenario, ioExecutor);
            // Depends on operationMapFuture
            CompletableFuture<Map<String, RoutingStepResourceVO>> stepResourceMapFuture = getStepResourceMapFuture(operationMapFuture, scenario, userId, ioExecutor);
            // Depends on operationMapFuture
            CompletableFuture<Map<String, NewProductStockPointVO>> productMapFuture = getProductMapFuture(operationMapFuture, workOrderMap, unPlanOperations, scenario, userId, ioExecutor);
            // Depends on productMapFuture
            CompletableFuture<Map<String, MdsProductStockPointBaseVO>> productStockPointBaseMapFuture = getProductStockPointBaseMapFuture(productMapFuture, scenario, userId, ioExecutor);
            // Depends on productMapFuture
            CompletableFuture<Map<String, MoldChangeTimeVO>> moldChangeTimeMapFuture = getMoldChangeTimeMapFuture(productMapFuture, mainResourceMap, scenario, userId, ioExecutor);
            // Depends on productMapFuture
            CompletableFuture<List<InventoryBatchDetailVO>> inventoryBatchDetailsFuture = getInventoryBatchDetailsFuture(productMapFuture, scenario, userId, ioExecutor);
            // Depends on inventoryBatchDetailsFuture
            CompletableFuture<Map<String, SubInventoryCargoLocationVO>> cargoLocationMapFuture = getCargoLocationMapFuture(inventoryBatchDetailsFuture, scenario, userId, ioExecutor);
            // Depends on productMapFuture
            CompletableFuture<Map<String, List<DeliveryPlanVO2>>> deliveryPlanMapFuture = getDeliveryPlanMapFuture(productMapFuture, planningHorizon, scenario, userId, ioExecutor);
            // Depends on productMapFuture
            CompletableFuture<List<SafetyStockLevelVO>> safetyStockLevelsFuture = getSafetyStockLevelsFuture(productMapFuture, scenario, userId, ioExecutor);
            // Depends on productMapFuture
            CompletableFuture<Map<String, ProductCandidateResourceVO>> productCandidateResourceMapFuture = getProductCandidateResourceMapFuture(productMapFuture, mainResourceMap, scenario, userId, ioExecutor);
            // Depends on bomTreeNewsFuture
            CompletableFuture<List<BomRoutingStepInputVO>> bomTreeNewsFuture = getBomTreeNewsFuture(productMapFuture, scenario, userId, ioExecutor);

            // --- Step 5: Wait for Dependent Fetches to Complete ---
            log.info("Waiting for dependent data fetches to complete...");
            CompletableFuture.allOf(standardResourceMapFuture, operationId2ToolResourceIdMapFuture, operationMapFuture, stepResourceMapFuture, productMapFuture, productStockPointBaseMapFuture, moldChangeTimeMapFuture, inventoryBatchDetailsFuture, cargoLocationMapFuture, deliveryPlanMapFuture, safetyStockLevelsFuture, productCandidateResourceMapFuture).join();
            log.info("Dependent data fetches complete.");
            // --- Step 6: Extract Fetched Data ---
            Map<String, StandardResourceVO> standardResourceMap = standardResourceMapFuture.join();
            Map<String, String> operationId2ToolResourceIdMap = operationId2ToolResourceIdMapFuture.join();
            Map<String, OperationPO> operationMap = operationMapFuture.join();
            Map<String, RoutingStepResourceVO> stepResourceMap = stepResourceMapFuture.join();
            Map<String, NewProductStockPointVO> productMap = productMapFuture.join();
            Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap = productStockPointBaseMapFuture.join();
            Map<String, MoldChangeTimeVO> moldChangeTimeMap = moldChangeTimeMapFuture.join();
            List<InventoryBatchDetailVO> inventoryBatchDetails = inventoryBatchDetailsFuture.join();
            Map<String, SubInventoryCargoLocationVO> cargoLocationMap = cargoLocationMapFuture.join();
            Map<String, List<DeliveryPlanVO2>> deliveryPlanMap = deliveryPlanMapFuture.join();
            List<SafetyStockLevelVO> safetyStockLevels = safetyStockLevelsFuture.join();
            Map<String, ProductCandidateResourceVO> productCandidateResourceMap = productCandidateResourceMapFuture.join();
            List<BomRoutingStepInputVO> routingStepInputVOS = bomTreeNewsFuture.join();

            // --- Step 7: Perform Main Synchronous Processing ---
            log.info("Starting main synchronous processing...");
            // Assemble tasks (Modifies operationTasks)
            taskAssembleLoop(operationTasks, operationMap, stepResourceMap);
            // ... (group tasks, prepare inventory maps, etc. as before) ...
            Map<String, List<MasterPlanTaskVO>> resourceTaskGroup = operationTasks.stream().collect(Collectors.groupingBy(MasterPlanTaskVO::getPhysicalResourceId));
            Map<String, List<MasterPlanTaskVO>> workOrderTaskGroup = operationTasks.stream().filter(e -> Objects.nonNull(e.getOperationVO()) && StringUtils.isNotEmpty(e.getOperationVO().getOrderId())).collect(Collectors.groupingBy(p -> p.getOperationVO().getOrderId()));
            // Prepare inventory maps
            List<String> saleOrganizations = newStockPoints.stream().filter(e -> StringUtils.isNotEmpty(e.getOrganizeType()) && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType())).map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
            List<String> productOrganizations = newStockPoints.stream().filter(e -> StringUtils.isNotEmpty(e.getOrganizeType()) && StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode().equals(e.getOrganizeType())).map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
            Map<String, List<InventoryBatchDetailVO>> finishInventoryMap = inventoryBatchDetails.stream().filter(t -> StringUtils.isEmpty(t.getOperationCode()) && saleOrganizations.contains(t.getStockPointCode()) && SUB_INVENTORY_CPSJ.equals(t.getSubinventory())).collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));
            Map<String, List<InventoryBatchDetailVO>> semiFinishInventoryMap = inventoryBatchDetails.stream().filter(t -> StringUtils.isEmpty(t.getOperationCode()) && productOrganizations.contains(t.getStockPointCode())).collect(Collectors.groupingBy(p -> String.join("-", p.getStockPointCode(), p.getProductCode())));
            Map<String, List<InventoryBatchDetailVO>> operationInventoryMap = inventoryBatchDetails.stream().filter(t -> productOrganizations.contains(t.getStockPointCode())).collect(Collectors.groupingBy(p -> String.join("-", p.getStockPointCode(), p.getProductCode(), p.getOperationCode())));
            Map<String, List<String>> bomTreeNewMap = routingStepInputVOS.stream().collect(Collectors.groupingBy(BomRoutingStepInputVO::getSourceProductCode, Collectors.mapping(t -> t.getProductCode(), Collectors.toList())));

            // Other preparations
            Map<String, String> productCodeVehicleCodeMap = productMap.values().stream().filter(x -> StringUtils.isNotBlank(x.getVehicleModelCode())).collect(Collectors.toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getVehicleModelCode, (v1, v2) -> v1));

            List<Date> intervalDates = DateUtils.getIntervalDates(planningHorizon.getPlanStartTime(), planningHorizon.getPlanEndTime());
            List<String> dynamicHeaders = intervalDates.stream().map(p -> DateUtils.dateToString(p, DateUtils.COMMON_DATE_STR3)).collect(Collectors.toList());

            String specialStockPoint = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT").stream()
                    .map(CollectionValueVO::getCollectionValue)
                    .findFirst()
                    .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT"));
            String specialStockPoint2 = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT_S2").stream()
                    .map(CollectionValueVO::getCollectionValue)
                    .findFirst()
                    .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT_S2"));
            List<MasterPlanWorkOrderBodyVO> result = new ArrayList<>();
            Map<String, List<String>> parentSubOperationMap = new HashMap<>();
            CompletableFuture<Void> unPlanOperationAssembleFuture = CompletableFuture.runAsync(() ->
                    getUnPlanOperations(result, unPlanOperations, productMap, operationInventoryMap, standardStepMap,
                            productStockPointBaseMap, orderId2ComprehensiveYieldMap, cargoLocationMap,
                            productCodeVehicleCodeMap, workOrderMap, deliveryPlanMap, finishInventoryMap, semiFinishInventoryMap,
                            intervalDates, dynamicHeaders, scenario, userId, specialStockPoint, specialStockPoint2), cpuExecutor);
            Map<String, List<Map<String, Object>>> dynamicMap = new HashMap<>();
            CompletableFuture<Void> operationTaskAssembleFuture = CompletableFuture.runAsync(() ->
                    getOperationTasks(result, mainResourceMap, standardResourceMap, resourceTaskGroup,
                            workOrderTaskGroup, productMap, safetyStockLevels, workOrderMap,
                            continuousFurnace, operationMap, standardStepMap, operationInventoryMap, cargoLocationMap,
                            moldChangeTimeMap, productStockPointBaseMap, operationId2ToolResourceIdMap, toolResourceMap,
                            productCodeVehicleCodeMap, orderId2ComprehensiveYieldMap, productCandidateResourceMap,
                            deliveryPlanMap, finishInventoryMap, semiFinishInventoryMap, intervalDates,
                            dynamicHeaders, dynamicMap, parentSubOperationMap, specialStockPoint, specialStockPoint2, bomTreeNewMap), cpuExecutor);

            CompletableFuture<Void> mainProcessFutures = CompletableFuture.allOf(unPlanOperationAssembleFuture, operationTaskAssembleFuture);
            mainProcessFutures.join();

            log.info("Main synchronous processing finished.");

            // --- Step 8: Filter and Paginate ---
            PageInfo<MasterPlanWorkOrderBodyVO> pageInfo = PageUtils.getPageInfo(filterAndSortResult(masterPlanReq, result), masterPlanReq.getPageNum(), masterPlanReq.getPageSize());
            List<MasterPlanWorkOrderBodyVO> pageList = pageInfo.getList();

            // --- Step 9: Launch Post-Processing Fetches (submitted to ioExecutor) ---
            log.info("Launching post-processing fetches...");
            List<String> fgCodes = pageList.stream().map(MasterPlanWorkOrderBodyVO::getParentProductCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());

            DynamicDataSourceContextHolder.setDataSource(scenario);
            // 查询订单的顶层制造订单的物料
            List<WorkOrderVO> workOrderVOS = operationTaskExtDao.selectWorkOrderWithParentProductId(pageList.stream().map(MasterPlanWorkOrderBodyVO::getWorkOrderId).distinct().collect(Collectors.toList()));
            Map<String, WorkOrderVO> parentWorkOrderMap = workOrderVOS.stream().collect(Collectors.toMap(WorkOrderVO::getId, Function.identity(), (v1, v2) -> v1));
            DynamicDataSourceContextHolder.clearDataSource();
            CompletableFuture<Map<String, List<String>>> partRiskLevelMapFuture = getPartRiskLevelMapFuture(fgCodes, scenario, ioExecutor);
            CompletableFuture<Map<String, Integer>> piecePerBoxMapFuture = getPiecePerBoxMapFuture(fgCodes, scenario, ioExecutor);
            CompletableFuture<Map<String, MasterPlanRelationVO>> masterPlanRelationMapFuture = getMasterPlanRelationMapFuture(pageList, scenario, ioExecutor);
            CompletableFuture<Map<String, BigDecimal>> feedbackProductionMapFuture = getFeedbackProductionMapFuture(pageList, scenario, ioExecutor, parentSubOperationMap);
            CompletableFuture<Map<String, BigDecimal>> curingTimeMapFuture = getCuringTimeMapFuture(pageList, scenario, ioExecutor, parentWorkOrderMap);
            CompletableFuture<Map<String, BigDecimal>> currentForecastQuantityMapFuture = getCurrentForecastQuantityMapFuture(pageList, scenario, ioExecutor, parentWorkOrderMap);
            CompletableFuture<Map<String, BigDecimal>> nextForecastQuantityMapFuture = getNextForecastQuantityMapFuture(pageList, scenario, ioExecutor, parentWorkOrderMap);
            // --- Step 10: Wait for Post-Processing Fetches ---
            CompletableFuture.allOf(partRiskLevelMapFuture, piecePerBoxMapFuture, masterPlanRelationMapFuture, feedbackProductionMapFuture, curingTimeMapFuture, currentForecastQuantityMapFuture, nextForecastQuantityMapFuture).join();
            log.info("Post-processing fetches complete.");

            // --- Step 11: Final Assembly ---
            Map<String, List<String>> partRiskLevelMap = partRiskLevelMapFuture.join();
            Map<String, Integer> piecePerBoxMap = piecePerBoxMapFuture.join();
            Map<String, MasterPlanRelationVO> masterPlanRelationMap = masterPlanRelationMapFuture.join();
            Map<String, BigDecimal> feedbackProductionMap = feedbackProductionMapFuture.join();
            Map<String, BigDecimal> curingTimeMap = curingTimeMapFuture.join();
            Map<String, BigDecimal> currentForecastQuantityMap = currentForecastQuantityMapFuture.join();
            Map<String, BigDecimal> nextForecastQuantityMap = nextForecastQuantityMapFuture.join();
            Map<String, BigDecimal> planQuantityMap = result.stream().filter(e -> StringUtils.isNotEmpty(e.getProductCode()) && StringUtils.isNotEmpty(e.getPlannedQuantity())).collect(Collectors.groupingBy(MasterPlanWorkOrderBodyVO::getProductCode, Collectors.reducing(BigDecimal.ZERO, e -> new BigDecimal(e.getPlannedQuantity()), BigDecimal::add)));
            setErrorColour(pageList, productMap, deliveryPlanMap, safetyStockLevels, dynamicMap, planQuantityMap, scenario);
            postProcessLoop(pageList, partRiskLevelMap, piecePerBoxMap, masterPlanRelationMap, feedbackProductionMap, parentSubOperationMap, curingTimeMap, currentForecastQuantityMap, nextForecastQuantityMap);
            if (Boolean.TRUE.equals(masterPlanReq.getShowAbnormalShift())) {
                assembleAbnormalShift(pageList, scenario, userId, mainResourceMap);
            }
            return pageInfo;
        } finally {
            // Shutdown locally created executor services gracefully
            shutdownExecutorService(ioExecutor, "ioExecutor");
            shutdownExecutorService(cpuExecutor, "cpuExecutor");
            // If using Spring-injected beans, remove the shutdown logic here.
        }
    }

    /**
     * 获取固化时间
     *
     * @param result
     * @param scenario
     * @param ioExecutor
     * @param parentWorkOrderMap
     * @return
     */
    private CompletableFuture<Map<String, BigDecimal>> getCuringTimeMapFuture(List<MasterPlanWorkOrderBodyVO> result, String scenario, ExecutorService ioExecutor, Map<String, WorkOrderVO> parentWorkOrderMap) {
        return CompletableFuture.supplyAsync(() -> {
            Map<String, BigDecimal> curingTimeMap = MapUtil.newHashMap();
            List<String> workOrderIds = result.stream().map(MasterPlanWorkOrderBodyVO::getWorkOrderId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(workOrderIds)) {
                return curingTimeMap;
            }
            Set<String> stockPointCodes = Sets.newHashSet();
            Set<String> productCodes = Sets.newHashSet();
            workOrderIds.forEach(productId -> {
                WorkOrderVO workOrderVO = parentWorkOrderMap.get(productId);
                if (Objects.isNull(workOrderVO)) {
                    return;
                }
                stockPointCodes.add(workOrderVO.getStockPointCode());
                productCodes.add(workOrderVO.getProductCode());
            });
            DynamicDataSourceContextHolder.setDataSource(scenario);
            NewStockPointVO saleStockPoint = operationTaskExtDao.selectBcSaleStockPoint();
            List<MasterPlanCuringTimeVO> beforeCuringTimes = operationTaskExtDao.selectBeforeCuringTime(stockPointCodes, productCodes);
            List<MasterPlanCuringTimeVO> afterCuringTimes = Objects.nonNull(saleStockPoint) ? operationTaskExtDao.selectAfterCuringTime(saleStockPoint.getStockPointCode(), productCodes) : Lists.newArrayList();
            DynamicDataSourceContextHolder.clearDataSource();
            if (CollectionUtils.isEmpty(beforeCuringTimes) && CollectionUtils.isEmpty(afterCuringTimes)) {
                return curingTimeMap;
            }
            Map<String, BigDecimal> beforeStockProductMap = beforeCuringTimes.stream().collect(Collectors.toMap(x -> String.join("&", x.getStockPointCode(), x.getProductCode()), MasterPlanCuringTimeVO::getCuringTime, (v1, v2) -> v1));
            Map<String, BigDecimal> afterStockProductMap = afterCuringTimes.stream().collect(Collectors.toMap(MasterPlanCuringTimeVO::getProductCode, MasterPlanCuringTimeVO::getCuringTime, (v1, v2) -> v1));
            parentWorkOrderMap.forEach((workOrderId, value) -> {
                String joinKey = String.join("&", value.getStockPointCode(), value.getProductCode());
                BigDecimal beforeCuringTime = beforeStockProductMap.getOrDefault(joinKey, BigDecimal.ZERO);
                BigDecimal afterCuringTime = afterStockProductMap.getOrDefault(value.getProductCode(), BigDecimal.ZERO);
                curingTimeMap.put(workOrderId, beforeCuringTime.add(afterCuringTime));
            });
            return curingTimeMap;
        }, ioExecutor);
    }

    /**
     * 获取下月预测量
     *
     * @param result
     * @param scenario
     * @param ioExecutor
     * @param parentWorkOrderMap
     * @return
     */
    private CompletableFuture<Map<String, BigDecimal>> getNextForecastQuantityMapFuture(List<MasterPlanWorkOrderBodyVO> result, String scenario, ExecutorService ioExecutor, Map<String, WorkOrderVO> parentWorkOrderMap) {
        return CompletableFuture.supplyAsync(() -> {
            Map<String, BigDecimal> forecastQuantityMap = MapUtil.newHashMap();
            List<String> workOrderIds = result.stream().map(MasterPlanWorkOrderBodyVO::getWorkOrderId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(workOrderIds)) {
                return forecastQuantityMap;
            }
            Set<String> productCodes = Sets.newHashSet();
            workOrderIds.forEach(productId -> {
                WorkOrderVO workOrderVO = parentWorkOrderMap.get(productId);
                if (Objects.isNull(workOrderVO)) {
                    return;
                }
                productCodes.add(workOrderVO.getProductCode());
            });
            String planPeriod = DateUtils.dateToString(new Date(), "yyyyMM");
            String forecastTime = DateUtils.dateToString(DateUtils.getMonthFirstDay(DateUtil.nextMonth()), DateUtils.COMMON_DATE_STR1);
            DynamicDataSourceContextHolder.setDataSource(scenario);
            // 获取最新的一致性需求预测版本
            String consistenceVersionId = operationTaskExtDao.selectMaxVersionId(planPeriod);
            if (StringUtils.isBlank(consistenceVersionId)) {
                DynamicDataSourceContextHolder.clearDataSource();
                return forecastQuantityMap;
            }
            List<ConsistenceDemandForecastDataDetailVO> detailVOS = CollectionUtils.isEmpty(productCodes)
                    ? new ArrayList<>() : operationTaskExtDao.selectMasterPlanConsistenceDemandForecastDataDetails(
                    consistenceVersionId, forecastTime, productCodes);
            DynamicDataSourceContextHolder.clearDataSource();
            if (CollectionUtils.isEmpty(detailVOS)) {
                return forecastQuantityMap;
            }
            Map<String, BigDecimal> forecastProductQuantityMap = detailVOS.stream().collect(Collectors.toMap(ConsistenceDemandForecastDataDetailVO::getProductCode, ConsistenceDemandForecastDataDetailVO::getForecastQuantity, (v1, v2) -> v1));
            parentWorkOrderMap.forEach((workOrderId, value) -> {
                BigDecimal forecastQuantity = forecastProductQuantityMap.getOrDefault(value.getProductCode(), BigDecimal.ZERO);
                forecastQuantityMap.put(workOrderId, forecastQuantity);
            });
            return forecastQuantityMap;
        }, ioExecutor);
    }

    /**
     * 获取当前月预测剩余量
     *
     * @param result
     * @param scenario
     * @param ioExecutor
     * @param parentWorkOrderMap
     * @return
     */
    private CompletableFuture<Map<String, BigDecimal>> getCurrentForecastQuantityMapFuture(List<MasterPlanWorkOrderBodyVO> result, String scenario, ExecutorService ioExecutor, Map<String, WorkOrderVO> parentWorkOrderMap) {
        return CompletableFuture.supplyAsync(() -> {
            Map<String, BigDecimal> forecastQuantityMap = MapUtil.newHashMap();
            List<String> workOrderIds = result.stream().map(MasterPlanWorkOrderBodyVO::getWorkOrderId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(workOrderIds)) {
                return forecastQuantityMap;
            }
            Set<String> productCodes = Sets.newHashSet();
            workOrderIds.forEach(productId -> {
                WorkOrderVO workOrderVO = parentWorkOrderMap.get(productId);
                if (Objects.isNull(workOrderVO)) {
                    return;
                }
                productCodes.add(workOrderVO.getProductCode());
            });
            String planPeriod = DateUtils.dateToString(DateUtil.date(), "yyyyMM");
            String forecastTime = DateUtils.dateToString(DateUtils.getMonthFirstDay(DateUtil.date()), DateUtils.COMMON_DATE_STR1);
            DynamicDataSourceContextHolder.setDataSource(scenario);
            // 获取最新的一致性需求预测版本
            String consistenceVersionId = operationTaskExtDao.selectMaxVersionId(planPeriod);
            if (StringUtils.isBlank(consistenceVersionId)) {
                DynamicDataSourceContextHolder.clearDataSource();
                return forecastQuantityMap;
            }
            List<ConsistenceDemandForecastDataDetailVO> detailVOS = CollectionUtils.isEmpty(productCodes)
                    ? new ArrayList<>() : operationTaskExtDao.selectMasterPlanConsistenceDemandForecastDataDetails(
                    consistenceVersionId, forecastTime, productCodes);
            String deliveryTime = DateUtils.dateToString(DateUtil.date(), DateUtils.YEAR_MONTH);
            List<WarehouseReleaseRecordVO> deliveryRecords = operationTaskExtDao.selectWarehouseReleaseRecordByTime(deliveryTime, productCodes);
            DynamicDataSourceContextHolder.clearDataSource();
            if (CollectionUtils.isEmpty(detailVOS) && CollectionUtils.isEmpty(deliveryRecords)) {
                return forecastQuantityMap;
            }
            Map<String, BigDecimal> forecastProductQuantityMap = CollectionUtils.isEmpty(detailVOS) ?
                    MapUtil.newHashMap() :
                    detailVOS.stream().collect(Collectors.toMap(ConsistenceDemandForecastDataDetailVO::getProductCode, ConsistenceDemandForecastDataDetailVO::getForecastQuantity, (v1, v2) -> v1));
            Map<String, BigDecimal> deliveryMap = CollectionUtils.isEmpty(deliveryRecords) ?
                    MapUtil.newHashMap() :
                    deliveryRecords.stream().collect(Collectors.toMap(WarehouseReleaseRecordVO::getItemCode, WarehouseReleaseRecordVO::getSumQty, (v1, v2) -> v1));
            parentWorkOrderMap.forEach((workOrderId, value) -> {
                BigDecimal forecastQuantity = forecastProductQuantityMap.getOrDefault(value.getProductCode(), BigDecimal.ZERO);
                BigDecimal deliveryQuantity = deliveryMap.getOrDefault(value.getProductCode(), BigDecimal.ZERO);
                forecastQuantityMap.put(workOrderId, forecastQuantity.subtract(deliveryQuantity));
            });
            return forecastQuantityMap;
        }, ioExecutor);
    }

    private CompletableFuture<Map<String, BigDecimal>> getFeedbackProductionMapFuture(List<MasterPlanWorkOrderBodyVO> result, String scenario, ExecutorService executor, Map<String, List<String>> parentSubOperationMap) {
        return CompletableFuture.supplyAsync(() -> {
            List<String> operationIds = result.stream().map(MasterPlanWorkOrderBodyVO::getOperationId).distinct().collect(Collectors.toList());
            if (MapUtil.isNotEmpty(parentSubOperationMap)) {
                operationIds.addAll(parentSubOperationMap.values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(operationIds)) {
                return new HashMap<>();
            }
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<FeedbackProductionVO> feebackList = feedbackProductionService.selectByOperationIds(operationIds);
            DynamicDataSourceContextHolder.clearDataSource();
            return feebackList.stream().filter(e -> StringUtils.isNotEmpty(e.getOperationId()) && e.getReportingQuantity() != null).collect(Collectors.groupingBy(FeedbackProductionVO::getOperationId, Collectors.reducing(BigDecimal.ZERO, FeedbackProductionVO::getReportingQuantity, BigDecimal::add)));
        }, executor);
    }

    private void shutdownExecutorService(ExecutorService executor, String name) {
        // Helper method to shut down executor services (same as before)
        if (executor != null && !executor.isShutdown()) {
            try {
                log.info("Shutting down {}...", name);
                executor.shutdown();
                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                    log.warn("{} did not terminate in 10 seconds, forcing shutdown.", name);
                    executor.shutdownNow();
                    if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                        log.error("{} did not terminate even after shutdownNow.", name);
                    }
                }
                log.info("{} shut down successfully.", name);
            } catch (InterruptedException ie) {
                log.error("Shutdown of {} interrupted.", name, ie);
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    private CompletableFuture<List<MasterPlanTaskVO>> getOperationTasksFuture(MasterPlanReq masterPlanReq, String scenario, ExecutorService executor) {
        return CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<MasterPlanTaskVO> masterPlanTasks = operationTaskExtDao.selectByMasterReq(masterPlanReq);
            DynamicDataSourceContextHolder.clearDataSource();
            return masterPlanTasks;
        }, executor);
    }

    private CompletableFuture<List<OperationPlanVO>> getUnPlanOperationsFuture(MasterPlanReq masterPlanReq, String scenario, ExecutorService executor) {
        return CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<OperationPlanVO> operationPlans = operationTaskExtDao.selectUnPlanOperation(masterPlanReq);
            DynamicDataSourceContextHolder.clearDataSource();
            return operationPlans;
        }, executor);
    }

    private CompletableFuture<Map<String, MasterPlanRelationVO>> getMasterPlanRelationMapFuture(List<MasterPlanWorkOrderBodyVO> result, String scenario, ExecutorService executor) {
        return CompletableFuture.supplyAsync(() -> {
            List<String> workOrderNos = result.stream().map(MasterPlanWorkOrderBodyVO::getWorkOrderNumber).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            List<String> topWorkOrderNos = result.stream().map(MasterPlanWorkOrderBodyVO::getTopWorkOrderNumber).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            workOrderNos.addAll(topWorkOrderNos);
            workOrderNos = workOrderNos.stream().distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(workOrderNos)) {
                return new HashMap<>();
            }
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<MasterPlanRelationVO> masterPlanRelations = masterPlanRelationService.selectByParams(ImmutableMap.of("orderNos", workOrderNos));
            DynamicDataSourceContextHolder.clearDataSource();
            return masterPlanRelations.stream().collect(Collectors.toMap(MasterPlanRelationVO::getOrderNo, Function.identity(), (v1, v2) -> v1));
        }, executor);
    }

    private CompletableFuture<Map<String, Integer>> getPiecePerBoxMapFuture(List<String> resultFgCodes, String scenario, ExecutorService executor) {
        return CompletableFuture.supplyAsync(() -> {
            if (CollectionUtils.isEmpty(resultFgCodes)) {
                return new HashMap<>();
            }
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<ProductBoxRelationVO> productBoxRelations = operationTaskExtDao.selectBoxPieces(resultFgCodes);
            DynamicDataSourceContextHolder.clearDataSource();
            return productBoxRelations.stream().collect(Collectors.toMap(ProductBoxRelationVO::getProductCode, ProductBoxRelationVO::getPiecePerBox, (v1, v2) -> v1));
        }, executor);
    }

    private CompletableFuture<Map<String, List<String>>> getPartRiskLevelMapFuture(List<String> resultFgCodes, String scenario, ExecutorService executor) {
        return CompletableFuture.supplyAsync(() -> {
            if (CollectionUtils.isEmpty(resultFgCodes)) {
                return new HashMap<>();
            }
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<PartRiskLevelVO> partRiskLevels = operationTaskExtDao.selectPartRiskLevels(resultFgCodes);
            DynamicDataSourceContextHolder.clearDataSource();
            return partRiskLevels.stream().collect(Collectors.groupingBy(PartRiskLevelVO::getProductCode, Collectors.mapping(PartRiskLevelVO::getMaterialRiskLevel, Collectors.toList())));
        }, executor);
    }

    private void postProcessLoop(List<MasterPlanWorkOrderBodyVO> result, Map<String, List<String>> partRiskLevelMap, Map<String, Integer> piecePerBoxMap, Map<String, MasterPlanRelationVO> masterPlanRelationMap, Map<String, BigDecimal> feedbackMap, Map<String, List<String>> parentSubOperationMap, Map<String, BigDecimal> curingTimeMap, Map<String, BigDecimal> currentForecastQuantityMap, Map<String, BigDecimal> nextForecastQuantityMap) {
        result.parallelStream().forEach(item -> {
            String workOrderId = item.getWorkOrderId();
            item.setCuringTime(curingTimeMap.getOrDefault(workOrderId, BigDecimal.ZERO));
            item.setCurrentMonthForecastRemainQuantity(currentForecastQuantityMap.getOrDefault(workOrderId, BigDecimal.ZERO));
            item.setNextMonthForecastQuantity(nextForecastQuantityMap.getOrDefault(workOrderId, BigDecimal.ZERO));
            String fgCode = item.getParentProductCode();
            item.setPartRiskLevel("低");
            if (partRiskLevelMap.containsKey(fgCode)) {
                if (partRiskLevelMap.get(fgCode).contains("高")) {
                    item.setPartRiskLevel("高");
                } else if (!"高".equals(item.getPartRiskLevel()) && partRiskLevelMap.get(fgCode).contains("中")) {
                    item.setPartRiskLevel("中");
                }
            }
            if (piecePerBoxMap.containsKey(fgCode)) {
                item.setPiecePerBox(piecePerBoxMap.get(fgCode));
            }
            String workOrderNumber = item.getWorkOrderNumber();
            if (StringUtils.isNotBlank(workOrderNumber) && masterPlanRelationMap.containsKey(workOrderNumber)) {
                item.setPlanNo(masterPlanRelationMap.get(workOrderNumber).getPlanNo());
                item.setErpOrderNo(masterPlanRelationMap.get(workOrderNumber).getErpOrderNo());
            }
            String topWorkOrderNumber = item.getTopWorkOrderNumber();
            if (StringUtils.isNotBlank(topWorkOrderNumber) && masterPlanRelationMap.containsKey(topWorkOrderNumber)) {
                item.setPlanNo(masterPlanRelationMap.get(topWorkOrderNumber).getPlanNo());
            }
            String operationId = item.getOperationId();
            if (parentSubOperationMap.containsKey(operationId)) {
                // 连续炉父工序汇总子工序对应的报工数量
                BigDecimal feedbackQuantity = BigDecimal.ZERO;
                for (String parentSubOperationId : parentSubOperationMap.get(operationId)) {
                    if (!feedbackMap.isEmpty() && StringUtils.isNotBlank(parentSubOperationId) && feedbackMap.containsKey(parentSubOperationId)) {
                        feedbackQuantity = feedbackQuantity.add(feedbackMap.get(parentSubOperationId));
                    }
                }
                item.setReportingQuantity(feedbackQuantity.stripTrailingZeros().toPlainString());
            } else {
                if (!feedbackMap.isEmpty() && StringUtils.isNotBlank(operationId) && feedbackMap.containsKey(operationId)) {
                    item.setReportingQuantity(feedbackMap.get(operationId).stripTrailingZeros().toPlainString());
                }
            }
        });
    }

    private void assembleAbnormalShift(List<MasterPlanWorkOrderBodyVO> result, String scenario, String userId, Map<String, PhysicalResourceVO> mainResourceMap) {
        String startDate = result.stream().map(MasterPlanWorkOrderBodyVO::getPlanDate).filter(Objects::nonNull).min(Comparator.naturalOrder()).orElse("");
        String endDate = result.stream().filter(item -> Objects.nonNull(item.getPlanDate())).map(MasterPlanWorkOrderBodyVO::getPlanEndDate).max(Comparator.naturalOrder()).orElse("");
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            return;
        }
        Date start = DateUtils.truncateTimeOfDate(DateUtils.stringToDate(startDate, DateUtils.COMMON_DATE_STR1));
        Date end = DateUtils.truncateTimeOfDate(DateUtils.stringToDate(endDate, DateUtils.COMMON_DATE_STR1));
        List<String> resourceIds = result.stream().map(MasterPlanWorkOrderBodyVO::getResourceId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<ResourceCalendarVO> abnormalResourceCalendars = cacheGetService.getAbnormalResourceCalendar(scenario, userId, new ArrayList<>(), resourceIds, start, end);
        if (CollectionUtils.isEmpty(abnormalResourceCalendars)) {
            return;
        }
        abnormalResourceCalendars.parallelStream().forEach(item -> {
            String resourceName = "";
            String physicalResourceId = item.getPhysicalResourceId();
            if (mainResourceMap.containsKey(physicalResourceId)) {
                PhysicalResourceVO physicalResourceVO = mainResourceMap.get(physicalResourceId);
                resourceName = physicalResourceVO.getPhysicalResourceName();
            }
            MasterPlanWorkOrderBodyVO body = MasterPlanWorkOrderBodyVO.builder().abnormalShift(Boolean.TRUE).resourceName(resourceName).productType(item.getShiftName()).planDate(DateUtils.dateToString(item.getStartTime(), DateUtils.COMMON_DATE_STR1)).planEndDate(DateUtils.dateToString(item.getEndTime(), DateUtils.COMMON_DATE_STR1)).build();
            result.add(body);
        });
        result.sort(Comparator.comparing(MasterPlanWorkOrderBodyVO::getPlanDate, Comparator.nullsLast(Comparator.naturalOrder())));
    }

    private void getOperationTasks(List<MasterPlanWorkOrderBodyVO> result, Map<String, PhysicalResourceVO> mainResourceMap, Map<String, StandardResourceVO> standardResourceMap, Map<String, List<MasterPlanTaskVO>> resourceTaskGroup, Map<String, List<MasterPlanTaskVO>> workOrderTaskGroup, Map<String, NewProductStockPointVO> productMap, List<SafetyStockLevelVO> safetyStockLevels, Map<String, WorkOrderPO> workOrderMap, String continuousFurnace, Map<String, OperationPO> operationMap, Map<String, String> standardStepMap, Map<String, List<InventoryBatchDetailVO>> operationInventoryMap, Map<String, SubInventoryCargoLocationVO> cargoLocationMap, Map<String, MoldChangeTimeVO> moldChangeTimeMap, Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap, Map<String, String> operationId2ToolResourceIdMap, Map<String, PhysicalResourceVO> toolResourceMap, Map<String, String> productCodeVehicleCodeMap, Map<String, BigDecimal> orderId2ComprehensiveYieldMap, Map<String, ProductCandidateResourceVO> productCandidateResourceMap, Map<String, List<DeliveryPlanVO2>> deliveryPlanMap, Map<String, List<InventoryBatchDetailVO>> finishInventoryMap, Map<String, List<InventoryBatchDetailVO>> semiFinishInventoryMap, List<Date> intervalDates, List<String> dynamicHeaders, Map<String, List<Map<String, Object>>> dynamicMap, Map<String, List<String>> parentSubOperationMap, String specialStockPoint, String specialStockPoint2, Map<String, List<String>> bomTreeNewMap) {
        Map<String, List<SafetyStockLevelVO>> safetyStockLevelMap = safetyStockLevels.stream().collect(Collectors.groupingBy(p -> String.join("&", p.getStockPointId(), p.getProductCode())));
        Map<String, Map<String, Integer>> productOfQtyMap = new HashMap<>();
        Map<String, Map<String, Integer>> productOfOriginalQtyMap = new HashMap<>();
        for (Map.Entry<String, PhysicalResourceVO> entry : mainResourceMap.entrySet()) {
            PhysicalResourceVO mainResource = entry.getValue();
            String standardResourceId = mainResource.getStandardResourceId();
            String standardResourceCode = (standardResourceMap.get(standardResourceId) != null) ? standardResourceMap.get(standardResourceId).getStandardResourceCode() : "";

            List<MasterPlanTaskVO> operationTaskOnResource = resourceTaskGroup.getOrDefault(mainResource.getId(), new ArrayList<>());
            // Group tasks by product
            Map<String, List<MasterPlanTaskVO>> productTaskMap = operationTaskOnResource.stream().filter(x -> Objects.nonNull(x.getOperationVO()) && StringUtils.isNotBlank(x.getOperationVO().getProductId())).collect(Collectors.groupingBy(p -> p.getOperationVO().getProductId()));

            for (Map.Entry<String, List<MasterPlanTaskVO>> productTaskEntry : productTaskMap.entrySet()) {
                List<MasterPlanTaskVO> productTaskEntryValue = productTaskEntry.getValue();
                productTaskEntryValue.sort(Comparator.comparing(OperationTaskVO::getStartTime));
                NewProductStockPointVO newProductStockPoint = productMap.get(productTaskEntry.getKey());
                // Handle a case where product might not be found
                if (newProductStockPoint == null) {
                    log.warn("Product not found for ID: {}", productTaskEntry.getKey());
                    continue;
                }

                // Safety stock calculation
                String safetyKey = String.join("&", newProductStockPoint.getStockPointCode(), newProductStockPoint.getProductCode());
                BigDecimal minStockDay = Optional.ofNullable(safetyStockLevelMap.get(safetyKey)).filter(list -> !list.isEmpty()).map(list -> list.get(0)).map(SafetyStockLevelVO::getMinStockDay).orElse(BigDecimal.ONE);

                // Determine product code for delivery plan lookup (handling parent/top orders)
                String productCodeForDeliveryPlan = newProductStockPoint.getProductCode();
                String parentProductCode = newProductStockPoint.getProductCode(); // Default to current product code
                if (CollectionUtils.isNotEmpty(productTaskEntryValue)) {
                    String orderId = productTaskEntryValue.get(0).getOperationVO().getOrderId();
                    WorkOrderPO workOrderPO = workOrderMap.get(orderId);
                    if (Objects.nonNull(workOrderPO) && StringUtils.isNotBlank(workOrderPO.getTopOrderId())) {
                        // 查找成品的制造订单对应的物品的发货计划
                        WorkOrderPO parentOrder = workOrderMap.get(workOrderPO.getTopOrderId());
                        if (Objects.nonNull(parentOrder)) {
                            String productId = parentOrder.getProductId();
                            NewProductStockPointVO parentWorkOrderProduct = productMap.get(productId);
                            productCodeForDeliveryPlan = parentWorkOrderProduct.getProductCode();
                            // Update parentProductCode as well
                            parentProductCode = parentWorkOrderProduct.getProductCode();
                        } else {
                            log.error("顶层制造订单没找到，id：{}", workOrderPO.getTopOrderId());
                        }
                    }
                }

                // Get delivery plan details
                List<DeliveryPlanVO2> detailList = deliveryPlanMap.getOrDefault(productCodeForDeliveryPlan, new ArrayList<>());
                Map<String, Integer> qtyMap = detailList.stream().collect(Collectors.groupingBy(p -> DateUtils.dateToString(p.getDemandTime(), DateUtils.COMMON_DATE_STR3), Collectors.summingInt(DeliveryPlanVO2::getDemandQuantity)));
                Map<String, Integer> originalQtyMap = new HashMap<>(qtyMap);
                productOfQtyMap.put(newProductStockPoint.getProductCode(), qtyMap);
                productOfOriginalQtyMap.put(newProductStockPoint.getProductCode(), originalQtyMap);

                // Pre-process continuous furnace operations if applicable
                if (continuousFurnace.equals(standardResourceCode)) {
                    prepProcessOperation(productTaskEntryValue, operationMap, parentSubOperationMap);
                }

                List<MasterPlanWorkOrderBodyVO> subList = new ArrayList<>();
                // Calculate inventory levels
                String ycl = getInventory(PRE_OPERATION, newProductStockPoint, standardStepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
                String ys = getInventory(PAINTING_OPERATION, newProductStockPoint, standardStepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
                String cx = getInventory(FORMING_OPERATION, newProductStockPoint, standardStepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
                String hp = getInventory(MERGING_OPERATION, newProductStockPoint, standardStepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
                String bz = getInventory(PACKAGING_OPERATION, newProductStockPoint, standardStepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
                double totalQuantity = Arrays.stream(new String[]{cx, hp, bz})
                        .mapToDouble(Double::parseDouble).reduce(Double::sum).orElse(0);

                // Iterate through tasks for the current product and resource
                for (MasterPlanTaskVO masterOperationTask : productTaskEntryValue) {
                    String topWorkOrderNumber = null;
                    String topWorkOrderQuantity = null;
                    String currentWorkOrderNumber = null;
                    String currentWorkOrderQuantity = "0";
                    // Use the one derived earlier
                    String currentTestOrderNumber = null;

                    OperationVO operation = masterOperationTask.getOperationVO();
                    if (operation == null || StringUtils.isBlank(operation.getOrderId())) {
                        log.warn("Skipping task due to missing operationVO or orderId. Task UUID: {}", masterOperationTask.getUuid());
                        // Skip task if essential info is missing
                        continue;
                    }
                    String orderId = operation.getOrderId();
                    WorkOrderPO workOrder = workOrderMap.get(orderId);
                    String parentId = operation.getParentId();
                    OperationPO parentOperation = operationMap.get(parentId);

                    if (Objects.nonNull(workOrder)) {
                        currentWorkOrderNumber = workOrder.getOrderNo();
                        currentWorkOrderQuantity = workOrder.getQuantity() != null ? workOrder.getQuantity().stripTrailingZeros().toPlainString() : "0";
                        // Assign specific test number
                        currentTestOrderNumber = workOrder.getTestOrderNumber();

                        if (StringUtils.isNotBlank(workOrder.getTopOrderId())) {
                            WorkOrderPO parentOrder = workOrderMap.get(workOrder.getTopOrderId());
                            if (Objects.nonNull(parentOrder)) {
                                topWorkOrderNumber = parentOrder.getOrderNo();
                                topWorkOrderQuantity = parentOrder.getQuantity() != null ? parentOrder.getQuantity().stripTrailingZeros().toPlainString() : null;
                                // Use the top order's test number if available and different
                                if (StringUtils.isNotBlank(parentOrder.getTestOrderNumber())) {
                                    currentTestOrderNumber = parentOrder.getTestOrderNumber();
                                }
                            } else {
                                log.error("Top order not found for ID during loop: {}", workOrder.getTopOrderId());
                            }
                        }
                    } else {
                        log.warn("Work order not found for ID during loop: {}", orderId);
                        // Decide if you want to continue without work order info or skip
                    }

                    String standardStepType = masterOperationTask.getStandardStepType();
                    if (StringUtils.isNotBlank(standardStepType) && standardStepType.equals(NORMAL_OPERATION)) {
                        // Skip non-key operations if needed
                        continue;
                    }

                    String resourceKey = String.join("&", newProductStockPoint.getId(), masterOperationTask.getStandardStepId(), masterOperationTask.getPhysicalResourceId());

                    // Calculate mold time
                    // Use sequence number? or operation code? Check key used in moldChangeTimeMapFuture
                    String moldMapKey = String.join("&", operation.getRoutingStepSequenceNo() + "", mainResource.getPhysicalResourceCode(), newProductStockPoint.getProductCode());
                    long moldTime = 0L;
                    MoldChangeTimeVO moldChangeTimeVO = moldChangeTimeMap.get(moldMapKey);
                    if (null != moldChangeTimeVO && null != moldChangeTimeVO.getDieChangeTime()) {
                        moldTime = moldChangeTimeVO.getDieChangeTime();
                    }

                    // Get product base VO
                    MdsProductStockPointBaseVO productStockPointBase = productStockPointBaseMap.getOrDefault(newProductStockPoint.getProductCode(), new MdsProductStockPointBaseVO());

                    String delay = getDelayInfo(masterOperationTask, workOrder);
                    String toolResourceId = operationId2ToolResourceIdMap.get(masterOperationTask.getOperationId());
                    PhysicalResourceVO toolResource = (StringUtils.isNotEmpty(toolResourceId)) ? toolResourceMap.getOrDefault(toolResourceId, new PhysicalResourceVO()) : new PhysicalResourceVO();
                    // Use parentProductCode here
                    String vehicleModelCode = productCodeVehicleCodeMap.get(parentProductCode);

                    MasterPlanWorkOrderBodyVO masterPlanWorkOrderBodyVO = assembleMasterPlanWorkOrderBodyVO(masterOperationTask, parentProductCode, operation, workOrder, mainResource, toolResource, newProductStockPoint, moldTime, ycl, ys, bz, hp, cx, totalQuantity, productStockPointBase, orderId2ComprehensiveYieldMap, delay, resourceKey, dynamicHeaders, productCandidateResourceMap, finishInventoryMap, semiFinishInventoryMap, cargoLocationMap, vehicleModelCode, parentOperation);

                    // Set top order info, defaulting to current if no top order
                    masterPlanWorkOrderBodyVO.setTopWorkOrderNumber(topWorkOrderNumber != null ? topWorkOrderNumber : currentWorkOrderNumber);
                    masterPlanWorkOrderBodyVO.setTopWorkOrderQuantity(topWorkOrderQuantity != null ? topWorkOrderQuantity : currentWorkOrderQuantity);
                    // Set derived test number
                    masterPlanWorkOrderBodyVO.setTestOrderNumber(currentTestOrderNumber);
                    masterPlanWorkOrderBodyVO.setMinStockDay(minStockDay);
                    subList.add(masterPlanWorkOrderBodyVO);
                }

                // Assemble dynamic data for the sublist
                // assembleMasterPlanSubList(subList, workOrderTaskGroup, minStockDay, intervalDates, originalQtyMap, qtyMap, dynamicMap, bomTreeNewMap);
                result.addAll(subList);
            }
        }
        Map<String, List<MasterPlanWorkOrderBodyVO>> subListMap = result.stream().collect(Collectors.groupingBy(MasterPlanWorkOrderBodyVO::getProductCode));
        for (Map.Entry<String, List<MasterPlanWorkOrderBodyVO>> entry : subListMap.entrySet()) {
            List<MasterPlanWorkOrderBodyVO> subList = entry.getValue();
            subList = subList.stream().filter(t -> !t.getUnPlan()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(subList)) {
                continue;
            }
            assembleMasterPlanSubList(subList, workOrderTaskGroup,
                    subList.get(0).getMinStockDay(), intervalDates, productOfOriginalQtyMap.get(entry.getKey()),
                    productOfQtyMap.get(entry.getKey()), dynamicMap, bomTreeNewMap, subListMap);
        }

    }

    private CompletableFuture<Map<String, String>> getOperationId2ToolResourceIdMapFuture(List<String> operationIds, String scenario, String userId, ExecutorService executor) {
        return CompletableFuture.supplyAsync(() -> cacheGetService.getOperationId2ToolResourceIdMap(scenario, userId, operationIds), executor);
    }

    private CompletableFuture<Map<String, StandardResourceVO>> getStandardResourceMapFuture(Map<String, PhysicalResourceVO> mainResourceMap, String scenario, String userId, ExecutorService executor) {
        return CompletableFuture.supplyAsync(() -> cacheGetService.getStandardResourceMap(scenario, userId, mainResourceMap), executor);
    }

    private CompletableFuture<Map<String, OperationPO>> getOperationMapFuture(List<String> operationIds, String scenario, ExecutorService executor) {
        return CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<OperationPO> operations = CollectionUtils.isEmpty(operationIds) ? new ArrayList<>() : operationExtDao.selectByIds(operationIds);
            DynamicDataSourceContextHolder.clearDataSource();
            return operations.stream().collect(Collectors.toMap(OperationPO::getId, Function.identity(), (v1, v2) -> v1));
        }, executor);
    }

    private CompletableFuture<Map<String, RoutingStepResourceVO>> getStepResourceMapFuture(CompletableFuture<Map<String, OperationPO>> operationMapFuture, String scenario, String userId, ExecutorService executor) {
        return operationMapFuture.thenApplyAsync(operationMap -> cacheGetService.getStepResourceMap(scenario, userId, operationMap), executor);
    }

    private CompletableFuture<Map<String, NewProductStockPointVO>> getProductMapFuture(CompletableFuture<Map<String, OperationPO>> operationMapFuture, Map<String, WorkOrderPO> workOrderMap, List<OperationPlanVO> unPlanOperations, String scenario, String userId, ExecutorService executor) {
        return operationMapFuture.thenApplyAsync(operationMap -> cacheGetService.getProductMap(scenario, userId, operationMap, workOrderMap, unPlanOperations), executor);
    }

    private CompletableFuture<Map<String, MdsProductStockPointBaseVO>> getProductStockPointBaseMapFuture(CompletableFuture<Map<String, NewProductStockPointVO>> productMapFuture, String scenario, String userId, ExecutorService executor) {
        return productMapFuture.thenApplyAsync(productMap -> cacheGetService.getProductStockPointBaseMap(scenario, userId, productMap), executor);
    }

    private CompletableFuture<Map<String, MoldChangeTimeVO>> getMoldChangeTimeMapFuture(CompletableFuture<Map<String, NewProductStockPointVO>> productMapFuture, Map<String, PhysicalResourceVO> mainResourceMap, String scenario, String userId, ExecutorService executor) {
        return productMapFuture.thenApplyAsync(productMap -> cacheGetService.getMoldChangeTimeMap(scenario, userId, productMap, mainResourceMap), executor);
    }

    private CompletableFuture<List<InventoryBatchDetailVO>> getInventoryBatchDetailsFuture(CompletableFuture<Map<String, NewProductStockPointVO>> productMapFuture, String scenario, String userId, ExecutorService executor) {
        return productMapFuture.thenApplyAsync(productMap -> cacheGetService.getInventoryBatchDetails(scenario, userId, productMap), executor);
    }

    private CompletableFuture<List<BomRoutingStepInputVO>> getBomTreeNewsFuture(CompletableFuture<Map<String, NewProductStockPointVO>> productMapFuture, String scenario, String userId, ExecutorService executor) {
        return productMapFuture.thenApplyAsync(productMap -> cacheGetService.getBomTreeNews(scenario, userId, productMap), executor);
    }

    private CompletableFuture<Map<String, SubInventoryCargoLocationVO>> getCargoLocationMapFuture(CompletableFuture<List<InventoryBatchDetailVO>> inventoryBatchDetailsFuture, String scenario, String userId, ExecutorService executor) {
        return inventoryBatchDetailsFuture.thenApplyAsync(inventoryBatchDetails -> cacheGetService.getCargoLocationMap(scenario, userId, inventoryBatchDetails), executor);
    }

    private CompletableFuture<Map<String, List<DeliveryPlanVO2>>> getDeliveryPlanMapFuture(CompletableFuture<Map<String, NewProductStockPointVO>> productMapFuture, PlanningHorizonVO planningHorizon, String scenario, String userId, ExecutorService executor) {
        return productMapFuture.thenApplyAsync(productMap -> cacheGetService.getDeliveryPlanMap(scenario, userId, productMap, planningHorizon), executor);
    }

    private CompletableFuture<List<SafetyStockLevelVO>> getSafetyStockLevelsFuture(CompletableFuture<Map<String, NewProductStockPointVO>> productMapFuture, String scenario, String userId, ExecutorService executor) {
        return productMapFuture.thenApplyAsync(productMap -> cacheGetService.getSafetyStockLevels(scenario, userId, productMap), executor);
    }

    private CompletableFuture<Map<String, ProductCandidateResourceVO>> getProductCandidateResourceMapFuture(CompletableFuture<Map<String, NewProductStockPointVO>> productMapFuture, Map<String, PhysicalResourceVO> mainResourceMap, String scenario, String userId, ExecutorService executor) {
        return productMapFuture.thenApplyAsync(productMap -> cacheGetService.getProductCandidateResourceMap(scenario, userId, productMap, mainResourceMap), executor);
    }

    private void assembleMasterPlanSubList(List<MasterPlanWorkOrderBodyVO> subList, Map<String, List<MasterPlanTaskVO>> workOrderTaskGroup, BigDecimal minStockDay, List<Date> intervalDates, Map<String, Integer> originalQtyMap, Map<String, Integer> qtyMap, Map<String, List<Map<String, Object>>> dynamicMap, Map<String, List<String>> bomTreeNewMap, Map<String, List<MasterPlanWorkOrderBodyVO>> subListMap) {
        if (CollectionUtils.isEmpty(subList)) {
            return;
        }
        MasterPlanWorkOrderBodyVO typicalDatum = subList.get(0);
        String finishInventory = typicalDatum.getFinishInventory();
        String semiFinishInventory = typicalDatum.getSemiFinishInventory();
        String totalTime = typicalDatum.getTotalTime();
        // 如果是多层bom的产品，要找到另一个半片的产品的库存，取其中小的进行库存覆盖
        String saFinishInventory = null;
        String saSemiFinishInventory = null;
        String saTotalTime = null;
        String parentProductCode = typicalDatum.getParentProductCode();
        List<String> saProductCodeList = bomTreeNewMap.get(parentProductCode);
        boolean saFlag = false;
        if (CollectionUtils.isNotEmpty(saProductCodeList)) {
            String saProductCode = saProductCodeList.stream().filter(t -> !typicalDatum.getProductCode()
                    .equals(t)).findFirst().orElse(null);
            if (StringUtils.isNotEmpty(saProductCode)) {
                List<MasterPlanWorkOrderBodyVO> saBodyVOS = subListMap.get(saProductCode);
                if (CollectionUtils.isNotEmpty(saBodyVOS)) {
                    MasterPlanWorkOrderBodyVO saBodyVO = saBodyVOS.get(0);
                    saFinishInventory = saBodyVO.getFinishInventory();
                    saSemiFinishInventory = saBodyVO.getSemiFinishInventory();
                    saTotalTime = saBodyVO.getTotalTime();
                    saFlag = true;
                }
            }
        }

        Integer bohInventory = getBohInventory(finishInventory, semiFinishInventory, totalTime, saFinishInventory, saSemiFinishInventory, saTotalTime, saFlag);
        // 使用期初冲减需求
        Map<String, String> colourMap = new HashMap<>();
        for (Date intervalDate : intervalDates) {
            String currentDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
            qtyMap.putIfAbsent(currentDate, 0);
            if (qtyMap.get(currentDate) == 0) {
                continue;
            }
            Integer demandQty = qtyMap.get(currentDate);
            if (demandQty <= bohInventory) {
                bohInventory -= demandQty;
                qtyMap.put(currentDate, 0);
                colourMap.put(currentDate, GREEN_COLOUR);
            } else {
                qtyMap.put(currentDate, demandQty - bohInventory);
                bohInventory = 0;
                break;
            }
        }
        subList.sort(Comparator.comparing(MasterPlanWorkOrderBodyVO::getPlanDate));
        for (MasterPlanWorkOrderBodyVO masterPlanWorkOrderBodyVO : subList) {
            String standardStepType = masterPlanWorkOrderBodyVO.getStandardStepType();
            if (!MasterPlanServiceImpl.FORMING_PROCESS.equals(standardStepType)) {
                continue;
            }
            OperationVO operationVO = masterPlanWorkOrderBodyVO.getOperationVO();
            MasterPlanTaskVO lastTask = workOrderTaskGroup.get(operationVO.getOrderId()).stream().sorted(Comparator.comparing(OperationTaskVO::getEndTime).reversed()).collect(Collectors.toList()).get(0);
            // 最后一道工序的完成时间+最小安全库存天数 = 要去覆盖哪一天的发货计划
            // 可提供覆盖的日期，暂不考虑+安全库存天数
            Date lastEndTime = DateUtils.truncateTimeOfDate(DateUtils.moveCalendar(operationVO.getEndTime(), Calendar.DAY_OF_YEAR, minStockDay.intValue()));

            // 排产数量，可提供覆盖的量
            // int planQty = operationVO.getQuantity().intValue();
            int planQty = Integer.parseInt(masterPlanWorkOrderBodyVO.getTopWorkOrderQuantity());
            for (Date intervalDate : intervalDates) {
                String currentDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
                if (!qtyMap.containsKey(currentDate)) {
                    continue;
                }
                // 未覆盖数量
                Integer unfulfilledQty = qtyMap.get(currentDate);
                // 判断是否能提供覆盖日期
                if (intervalDate.getTime() >= lastEndTime.getTime()) {
                    // 如果发货计划数量 < 排产数量，直接覆盖
                    if (unfulfilledQty <= planQty) {
                        // 修改可供的排产数量
                        planQty = planQty - unfulfilledQty;
                        colourMap.putIfAbsent(currentDate, BLUE_COLOUR);
                        unfulfilledQty = 0;
                    } else if (planQty > 0) {
                        // 修改发货计划的数量，剩余数量靠其余排产任务进行满足
                        unfulfilledQty = unfulfilledQty - planQty;
                        planQty = 0;
                    }
                }
                // 重新赋值发货计划覆盖数量
                qtyMap.put(currentDate, unfulfilledQty);
            }
            List<Map<String, Object>> dynamicData = new ArrayList<>();
            for (Date intervalDate : intervalDates) {
                Map<String, Object> dataMap = new HashMap<>();
                String currentDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
                if (!originalQtyMap.containsKey(currentDate)) {
                    dataMap.put(FIELD_SUPPLY_QTY, 0);
                    dataMap.put(FIELD_FULFILLMENT_STATUS, YesOrNoEnum.YES.getCode());
                    dataMap.put(FIELD_DEMAND_TIME, currentDate);
                    dataMap.put(FIELD_DEMAND_QTY, 0);
                    dataMap.put(FIELD_BLOCK_COLOUR, EMPTY_COLOUR);
                    dynamicData.add(dataMap);
                    continue;
                }

                Integer demandQuantity = originalQtyMap.getOrDefault(currentDate, 0);
                Integer unfulfilledQty = qtyMap.get(currentDate);
                dataMap.put(FIELD_DEMAND_TIME, currentDate);
                dataMap.put(FIELD_DEMAND_QTY, demandQuantity);
                dataMap.put(FIELD_SUPPLY_QTY, demandQuantity - unfulfilledQty);
                // 未被覆盖
                if (Objects.equals(unfulfilledQty, demandQuantity)) {
                    dataMap.put(FIELD_FULFILLMENT_STATUS, YesOrNoEnum.NO.getCode());
                    dataMap.put(FIELD_BLOCK_COLOUR, RED_COLOUR);
                }
                // 完全覆盖
                if (demandQuantity > unfulfilledQty && unfulfilledQty == 0) {
                    dataMap.put(FIELD_FULFILLMENT_STATUS, YesOrNoEnum.YES.getCode());
                    dataMap.put(FIELD_BLOCK_COLOUR, colourMap.get(currentDate));
                }
                // 部分覆盖
                if (demandQuantity > unfulfilledQty && unfulfilledQty > 0) {
                    dataMap.put(FIELD_FULFILLMENT_STATUS, "PART");
                    dataMap.put(FIELD_BLOCK_COLOUR, RED_COLOUR);
                }
                if (demandQuantity == 0) {
                    dataMap.put(FIELD_FULFILLMENT_STATUS, YesOrNoEnum.YES.getCode());
                    dataMap.put(FIELD_BLOCK_COLOUR, EMPTY_COLOUR);
                }
                dynamicData.add(dataMap);
            }
            masterPlanWorkOrderBodyVO.setDynamicData(dynamicData);
            if (FORMING_PROCESS.equals(masterPlanWorkOrderBodyVO.getStandardStepType())) {
                // OperationVO operation = masterPlanWorkOrderBodyVO.getOperationVO();
                if (Objects.nonNull(operationVO) && StringUtils.isNotBlank(operationVO.getOrderId())) {
                    dynamicMap.put(operationVO.getOrderId(), dynamicData);
                }
            }
        }


        // for (MasterPlanWorkOrderBodyVO item : subList) {
        //     item.setDynamicData(dynamicData);
        //     if (FORMING_PROCESS.equals(item.getStandardStepType())) {
        //         OperationVO operationVO = item.getOperationVO();
        //         if (Objects.nonNull(operationVO) && StringUtils.isNotBlank(operationVO.getOrderId())) {
        //             dynamicMap.put(operationVO.getOrderId(), dynamicData);
        //         }
        //     }
        // }
    }

    /**
     * 未计划组装发货计划
     */
    private void unPlanAssembleMasterPlanSubList(List<Date> intervalDates, Map<String, Integer> originalQtyMap, MasterPlanWorkOrderBodyVO masterPlanWorkOrderBodyVO) {
        List<Map<String, Object>> dynamicData = new ArrayList<>();
        for (Date intervalDate : intervalDates) {
            Map<String, Object> dataMap = new HashMap<>();
            String currentDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
            dataMap.put(FIELD_SUPPLY_QTY, 0);
            dataMap.put(FIELD_FULFILLMENT_STATUS, YesOrNoEnum.YES.getCode());
            dataMap.put(FIELD_DEMAND_TIME, currentDate);
            dataMap.put(FIELD_BLOCK_COLOUR, EMPTY_COLOUR);
            if (!originalQtyMap.containsKey(currentDate)) {
                dataMap.put(FIELD_DEMAND_QTY, 0);
            } else {
                Integer demandQuantity = originalQtyMap.getOrDefault(currentDate, 0);
                dataMap.put(FIELD_DEMAND_QTY, demandQuantity);
            }
            dynamicData.add(dataMap);
        }
        masterPlanWorkOrderBodyVO.setDynamicData(dynamicData);
    }

    private void prepProcessOperation(List<MasterPlanTaskVO> productTaskEntryValue, Map<String, OperationPO> operationMap, Map<String, List<String>> parentSubOperationMap) {
        // 将子工序按父工序ID分组
        Map<String, List<MasterPlanTaskVO>> taskMap = productTaskEntryValue.stream().collect(Collectors.groupingBy(task -> task.getOperationVO().getParentId()));

        // 处理每个父工序的任务列表
        for (List<MasterPlanTaskVO> tasks : taskMap.values()) {
            MasterPlanTaskVO parentTask = tasks.get(0);
            OperationVO operationVO = parentTask.getOperationVO();
            // 获取父工序的ID
            String parentId = operationVO.getParentId();
            if (StringUtils.isBlank(parentId) || !operationMap.containsKey(parentId)) {
                continue;
            }
            // 获取父工序信息
            OperationPO parentOperationPO = operationMap.get(parentId);
            // 创建新的父工序任务对象
            parentTask.setOperationVO(OperationConvertor.INSTANCE.po2Vo(parentOperationPO));
            parentTask.setProductionStartTime(parentOperationPO.getStartTime());
            parentTask.setProductionEndTime(parentOperationPO.getEndTime());
            parentTask.setStartTime(parentOperationPO.getStartTime());
            parentTask.setEndTime(parentOperationPO.getEndTime());
            parentTask.setOperationId(parentId);
            parentTask.setWhetherHwResource(Boolean.TRUE);
            parentTask.setMoldLimitQuantity(tasks.size());

            // 移除所有子工序任务，并添加新的父工序任务
            List<String> uuidsToRemove = tasks.stream().map(MasterPlanTaskVO::getUuid).collect(Collectors.toList());
            productTaskEntryValue.removeIf(task -> uuidsToRemove.contains(task.getUuid()));
            productTaskEntryValue.add(parentTask);
            List<String> subOperationIds = StreamUtils.columnToList(tasks, MasterPlanTaskVO::getOperationId);
            parentSubOperationMap.put(parentId, subOperationIds);
        }
    }

    /**
     * 组装查询请求对象
     *
     * @param masterPlanReq     查询请求对象
     * @param planningHorizonVO 计划期间
     * @param productFlag       物品标志位
     * @param scenario          场景
     */
    private void assembleQueryRequest(MasterPlanReq masterPlanReq, PlanningHorizonVO planningHorizonVO, Boolean productFlag, String scenario) {
        DynamicDataSourceContextHolder.setDataSource(scenario);
        // 设置计划历史展望期间，方便查询历史展望期之后的工序
        Date historyRetrospectStartTime = planningHorizonVO.getHistoryRetrospectStartTime();
        if (null == masterPlanReq.getDeliverStartTime() && null == masterPlanReq.getDeliverEndTime()) {
            masterPlanReq.setHistoryRetrospectStartTime(historyRetrospectStartTime);
            // 如果不指定排产期间，使用历史展望开始时间作为排产开始时间，计划期间结束时间作为排产结束时间
            masterPlanReq.setDeliverStartTime(historyRetrospectStartTime);
            masterPlanReq.setDeliverEndTime(planningHorizonVO.getPlanEndTime());
        } else {
            masterPlanReq.setHistoryRetrospectStartTime(masterPlanReq.getDeliverStartTime());
        }
        if (Boolean.TRUE.equals(productFlag)) {
            String productCode = masterPlanReq.getProductCode();
            if (StringUtils.isNotBlank(productCode)) {
                List<String> productCodes = operationTaskExtDao.selectSaProductCodes(productCode);
                productCodes.add(productCode);
                productCodes = productCodes.stream().distinct().collect(Collectors.toList());
                masterPlanReq.setProductCodes(productCodes);
                masterPlanReq.setProductCode(null);
            }
        }
        List<String> physicalResourceIds = masterPlanReq.getPhysicalResourceIds();
        if (CollectionUtils.isNotEmpty(physicalResourceIds)) {
            List<PhysicalResourceVO> physicalResources = newMdsFeign.selectByPhysicalIds(scenario, physicalResourceIds);
            masterPlanReq.setPhysicalResources(physicalResources);
            return;
        }
        String standardResourceId = masterPlanReq.getStandardResourceId();
        // 查询物理资源 DATA PERMISSION
        List<PhysicalResourceVO> physicalResources = operationTaskExtDao.selectPhysicalResource(null, standardResourceId, null);
        if (CollectionUtils.isNotEmpty(physicalResources)) {
            masterPlanReq.setPhysicalResourceIds(physicalResources.stream().map(PhysicalResourceVO::getId).collect(Collectors.toList()));
            masterPlanReq.setPhysicalResources(physicalResources);
        }
        DynamicDataSourceContextHolder.clearDataSource();
    }

    private MasterPlanWorkOrderBodyVO assembleMasterPlanWorkOrderBodyVO(MasterPlanTaskVO masterPlanTask, String parentProductCode, OperationVO operation, WorkOrderPO workOrder, PhysicalResourceVO mainResource, PhysicalResourceVO toolResource, NewProductStockPointVO newProductStockPoint, long moldTime, String ycl, String ys, String bz, String hp, String cx, double totalQuantity, MdsProductStockPointBaseVO productStockPointBase, Map<String, BigDecimal> orderId2ComprehensiveYieldMap, String delay, String resourceKey, List<String> dynamicHeaders, Map<String, ProductCandidateResourceVO> productCandidateResourceMap, Map<String, List<InventoryBatchDetailVO>> finishInventoryMap, Map<String, List<InventoryBatchDetailVO>> semiFinishInventoryMap, Map<String, SubInventoryCargoLocationVO> cargoLocationMap, String vehicleModelCode, OperationPO parentOperation) {
        String productCode = newProductStockPoint.getProductCode();
        String stockPointCode = newProductStockPoint.getStockPointCode();
        MasterPlanWorkOrderBodyVO masterPlanWorkOrderBodyVO = MasterPlanWorkOrderBodyVO.builder().issuedStatus(Optional.of(workOrder).map(WorkOrderPO::getPlanStatus).orElse(null)).moldLimitQuantity(masterPlanTask.getMoldLimitQuantity() == null ? 1 : masterPlanTask.getMoldLimitQuantity()).whetherHwResource(null == masterPlanTask.getWhetherHwResource() ? Boolean.FALSE : masterPlanTask.getWhetherHwResource()).parentProductCode(parentProductCode).operationStepCode(masterPlanTask.getOperationCode()).workOrderId(operation.getOrderId()).dueDate(Optional.of(workOrder).map(WorkOrderPO::getDueDate).orElse(null)).standardStepId(masterPlanTask.getStandardStepId()).standardStepCode(masterPlanTask.getStandardStepCode()).standardStepType(masterPlanTask.getStandardStepType()).resourceId(mainResource.getId()).resourceName(mainResource.getPhysicalResourceName()).resourceCode(mainResource.getPhysicalResourceCode()).toolResourceId(toolResource.getId()).toolResourceCode(toolResource.getPhysicalResourceCode()).toolResourceName(toolResource.getPhysicalResourceName()).operationId(masterPlanTask.getOperationId()).operationName(masterPlanTask.getStandardStepName()).operationCode(masterPlanTask.getRoutingStepNo()).planDate(Objects.nonNull(masterPlanTask.getProductionStartTime()) ? DateUtils.dateToString(masterPlanTask.getProductionStartTime(), DateUtils.COMMON_DATE_STR1) : null).planDateTime(masterPlanTask.getStartTime()).planEndDate(Objects.nonNull(masterPlanTask.getProductionEndTime()) ? DateUtils.dateToString(masterPlanTask.getProductionEndTime(), DateUtils.COMMON_DATE_STR1) : null).planEndDateTime(masterPlanTask.getEndTime()).productCode(productCode).productName(newProductStockPoint.getProductName()).productId(newProductStockPoint.getId()).productType(vehicleModelCode).moldChangeTime(String.valueOf(moldTime)).packagingMethod(masterPlanTask.getBoxType())
                // 实时库存数据
                .preTreatment(ycl).printing(ys).postPackaging(bz).postLamination(hp).suppress(cx)
                // 工序在制量
                .totalTime(String.format("%.0f", totalQuantity)).productionTime(convertSecondsToHours(masterPlanTask.getProductionTime()) + "h")
                // 产品工艺基础数据表 获取值
                .grilleType(productStockPointBase.getGridType()).productionMode(productStockPointBase.getProductionModel()).hud(productStockPointBase.getHud()).wired(productStockPointBase.getClampType()).heaterWire(productStockPointBase.getSealEdge()).weight(null != productStockPointBase.getProductWidth() ? productStockPointBase.getProductWidth().stripTrailingZeros().toPlainString() : "0").length(null != productStockPointBase.getProductLength() ? productStockPointBase.getProductLength().stripTrailingZeros().toPlainString() : "0").color(productStockPointBase.getProductColor())
                // 排产数量
                .plannedQuantity(operation.getQuantity().stripTrailingZeros().toPlainString()).parentPlannedQuantity(parentOperation == null ? operation.getQuantity().stripTrailingZeros().toPlainString() : parentOperation.getQuantity().stripTrailingZeros().toPlainString())
                // 根据报工反馈判断
                .workOrderStatus("未执行").delay(delay).workOrderNumber(Optional.of(workOrder).map(WorkOrderPO::getOrderNo).orElse(null)).completeSetQuantity(Optional.of(workOrder).filter(po -> po.getFulfilledQuantity() != null).map(po -> po.getFulfilledQuantity().stripTrailingZeros()).orElse(BigDecimal.ZERO)).kitStatus(Optional.of(workOrder).map(x -> EnumUtils.getDescByCode(KitStatusEnum.class, workOrder.getKitStatus())).orElse(null)).kitStatusCode(Optional.of(workOrder).map(WorkOrderPO::getKitStatus).orElse(null)).planStatus(EnumUtils.getDescByCode(OperationEnum.class, operation.getPlanStatus())).comprehensiveYield(orderId2ComprehensiveYieldMap.get(operation.getOrderId())).header(dynamicHeaders).finishInventory("0").semiFinishInventory("0").firstInEachProduct(masterPlanTask.getFirstInEachProduct()).orderType(Optional.of(workOrder).map(WorkOrderPO::getOrderType).orElse(null)).operationVO(operation).unPlan(false).operationTaskId(masterPlanTask.getId()).remark(masterPlanTask.getRemark()).build();
        if (productCandidateResourceMap.containsKey(resourceKey)) {
            BigDecimal unitProductionTime = productCandidateResourceMap.get(resourceKey).getUnitProductionTime();
            masterPlanWorkOrderBodyVO.setBeat(null == unitProductionTime ? "0" : String.valueOf(unitProductionTime.setScale(0, RoundingMode.HALF_UP)));
        }
        assembleInventory(parentProductCode, stockPointCode, productCode, finishInventoryMap, semiFinishInventoryMap, cargoLocationMap, masterPlanWorkOrderBodyVO);
        return masterPlanWorkOrderBodyVO;
    }

    /**
     * 组装库存数据
     */
    private void assembleInventory(String parentProductCode, String stockPointCode, String productCode, Map<String, List<InventoryBatchDetailVO>> finishInventoryMap, Map<String, List<InventoryBatchDetailVO>> semiFinishInventoryMap, Map<String, SubInventoryCargoLocationVO> cargoLocationMap, MasterPlanWorkOrderBodyVO masterPlanWorkOrderBodyVO) {
        String semiKey = stockPointCode + "-" + productCode;
        if (!StringUtils.equals(productCode, parentProductCode) && semiFinishInventoryMap.containsKey(semiKey)) {
            // 维护产品编码对应的半品库存
            List<InventoryBatchDetailVO> semiFinishList = getFinishInventory(semiFinishInventoryMap.get(semiKey), cargoLocationMap);
            BigDecimal semiFinishInventory = semiFinishList.stream().map(t -> new BigDecimal(t.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
            masterPlanWorkOrderBodyVO.setSemiFinishInventory(semiFinishInventory.setScale(0).toString());
        }
        if (finishInventoryMap.containsKey(parentProductCode)) {
            // 维护产品编码对应的成品库存
            List<InventoryBatchDetailVO> finishList = getFinishInventory(finishInventoryMap.get(parentProductCode), cargoLocationMap);
            BigDecimal finishInventory = finishList.stream().map(t -> new BigDecimal(t.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
            masterPlanWorkOrderBodyVO.setFinishInventory(finishInventory.setScale(0).toString());
        }
    }

    /**
     * 维护工序维度异常预警信息标识及动态内容，用于覆盖发货计划
     *
     * @param pagedResult       结果集
     * @param productMap        物品Map
     * @param deliveryPlanMap   发货计划Map
     * @param safetyStockLevels 发货计划Map
     * @param dynamicMap        动态表头Map
     * @param planQuantityMap   计划量Map
     * @param scenario          场景
     */
    private void setErrorColour(List<MasterPlanWorkOrderBodyVO> pagedResult, Map<String, NewProductStockPointVO> productMap, Map<String, List<DeliveryPlanVO2>> deliveryPlanMap, List<SafetyStockLevelVO> safetyStockLevels, Map<String, List<Map<String, Object>>> dynamicMap, Map<String, BigDecimal> planQuantityMap, String scenario) {
        DynamicDataSourceContextHolder.setDataSource(scenario);
        // 获取成型工序
        List<String> workOrderIds = pagedResult.stream().map(MasterPlanWorkOrderBodyVO::getWorkOrderId).distinct().collect(Collectors.toList());
        Map<String, List<OperationEarilWarningInfoVO>> orderOperationGroup = new HashMap<>();
        if (CollectionUtils.isNotEmpty(workOrderIds)) {
            orderOperationGroup = operationTaskExtDao.queryOperationListByOrderIds(workOrderIds).stream().collect(Collectors.groupingBy(OperationEarilWarningInfoVO::getOrderId));
        }

        // 获取成型工序及成型后工序的物料编码
        Set<String> formingProductCodeSet = new HashSet<>();
        for (List<OperationEarilWarningInfoVO> orderOperList : orderOperationGroup.values()) {
            // 按照工序顺序号排序
            orderOperList.sort(Comparator.comparing(OperationEarilWarningInfoVO::getStandardStepCode));
            for (int i = 0; i < orderOperList.size(); i++) {
                OperationEarilWarningInfoVO orderOperation = orderOperList.get(i);
                if (FORMING_OPERATION.equals(orderOperation.getStandardStepName()) && i < (orderOperList.size() - 1)) {
                    formingProductCodeSet.add(orderOperation.getProductCode());
                }
            }
        }

        // 获取计划期间发货计划数量
        Map<String, Integer> deliveryQtyMap = deliveryPlanMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, t -> t.getValue().stream().mapToInt(DeliveryPlanVO2::getDemandQuantity).sum()));
        Map<String, BigDecimal> safetyStockLevelMap = safetyStockLevels.stream().collect(Collectors.toMap(e -> e.getStockCode() + "&" + e.getProductCode(), SafetyStockLevelVO::getStandardStockDay, (v1, v2) -> v1));
        // 处理异常预警信息标识
        for (MasterPlanWorkOrderBodyVO masterPlanWorkOrderBodyVO : pagedResult) {
            String workOrderId = masterPlanWorkOrderBodyVO.getWorkOrderId();
            String standardStepType = masterPlanWorkOrderBodyVO.getStandardStepType();
            if (dynamicMap.containsKey(workOrderId) && MasterPlanServiceImpl.COATING_PROCESS.equals(standardStepType)) {
                masterPlanWorkOrderBodyVO.setDynamicData(dynamicMap.get(workOrderId));
            }
            // 处理异常报警颜色
            if ("not_resource_id".equals(masterPlanWorkOrderBodyVO.getResourceId())) {
                // 没有资源红色预警
                masterPlanWorkOrderBodyVO.setErrorColour(ERROR_COLOUR);
            } else {
                if ("延期".equals(masterPlanWorkOrderBodyVO.getDelay())
                    // || KitStatusEnum.UNKIT.getCode().equals(masterPlanWorkOrderBodyVO.getKitStatusCode())
                ) {
                    // 工序进仓延期,工序未齐套,工序间隔时间不足，防氧化规则违反预警，排产数量超量规则黄色预警
                    masterPlanWorkOrderBodyVO.setErrorColour(WARNING_COLOUR);
                } else {
                    // 工序间隔时间不足，防氧化规则违反预警，排产数量超量规则只对成型工序
                    if (!FORMING_OPERATION.equals(masterPlanWorkOrderBodyVO.getOperationName())) {
                        continue;
                    }
                    if (!orderOperationGroup.containsKey(masterPlanWorkOrderBodyVO.getWorkOrderId())) {
                        continue;
                    }
                    // 成品库存 ➕ 半品库存 ➕ 合计 ➕ 成型工序排产数量之和（除未计划的都查）> 计划期间内发货计划数量之和
                    // ➕ 本厂标准安全库存（计划期间有发货数量的日期，从后往前取，如配置1天，则取最后1天的发货计划数量）
                    BigDecimal finishTotal = StringUtils.isEmpty(masterPlanWorkOrderBodyVO.getFinishInventory()) ? BigDecimal.ZERO : new BigDecimal(masterPlanWorkOrderBodyVO.getFinishInventory());
                    // 成型后工序半品库存
                    BigDecimal semiFinishedTotal = StringUtils.isEmpty(masterPlanWorkOrderBodyVO.getSemiFinishInventory()) ? BigDecimal.ZERO : new BigDecimal(masterPlanWorkOrderBodyVO.getSemiFinishInventory());
                    // 合计
                    BigDecimal totalTime = StringUtils.isEmpty(masterPlanWorkOrderBodyVO.getTotalTime()) ? BigDecimal.ZERO : new BigDecimal(masterPlanWorkOrderBodyVO.getTotalTime());
                    // 计划期间发货计划数量
                    Integer productDeliveryQty = deliveryQtyMap.getOrDefault(masterPlanWorkOrderBodyVO.getProductCode(), 0);
                    BigDecimal totalQty = finishTotal.add(semiFinishedTotal).add(totalTime).add(planQuantityMap.getOrDefault(masterPlanWorkOrderBodyVO.getProductCode(), BigDecimal.ZERO));

                    // 本厂标准安全库存
                    BigDecimal safeStockQty = BigDecimal.ZERO;
                    List<DeliveryPlanVO2> productDeliveryPlanList = deliveryPlanMap.get(masterPlanWorkOrderBodyVO.getProductCode());
                    NewProductStockPointVO productInfo = productMap.get(masterPlanWorkOrderBodyVO.getProductId());
                    if (CollectionUtils.isNotEmpty(productDeliveryPlanList) && productInfo != null) {
                        // 获取安全库存天数，如果安全库存天数未维护，则为0
                        int standardStockDay = safetyStockLevelMap.getOrDefault(productInfo.getStockPointCode() + "&" + productInfo.getProductCode(), BigDecimal.ZERO).intValue();
                        productDeliveryPlanList.sort(Comparator.comparing(DeliveryPlanVO2::getDemandTime).reversed());
                        for (int i = 0; i < productDeliveryPlanList.size(); i++) {
                            if (i >= standardStockDay) {
                                break;
                            }
                            safeStockQty = safeStockQty.add(BigDecimal.valueOf(productDeliveryPlanList.get(i).getDemandQuantity()));
                        }
                    }

                    if (totalQty.compareTo(BigDecimal.valueOf(productDeliveryQty)) > 0) {
                        masterPlanWorkOrderBodyVO.setErrorColour(WARNING_COLOUR);
                        continue;
                    }

                    // 获取成型成型工序的计划结束时间，镀膜工序的计划结束时间
                    Date confectionEndTime = null;
                    Date coatingEndTime = null;
                    List<OperationEarilWarningInfoVO> orderOperationList = orderOperationGroup.get(masterPlanWorkOrderBodyVO.getWorkOrderId());
                    for (OperationEarilWarningInfoVO orderOperation : orderOperationList) {
                        if (FORMING_OPERATION.equals(orderOperation.getStandardStepName())) {
                            confectionEndTime = orderOperation.getEndTime();
                        }
                        if ("镀膜".equals(orderOperation.getStandardStepName())) {
                            coatingEndTime = orderOperation.getEndTime();
                        }
                    }
                    if (Objects.isNull(confectionEndTime) || Objects.isNull(coatingEndTime)) {
                        continue;
                    }
                    BigDecimal subtractHourTime = BigDecimal.valueOf(confectionEndTime.getTime()).subtract(BigDecimal.valueOf(coatingEndTime.getTime())).divide(BigDecimal.valueOf(3600000), 6, RoundingMode.UP);
                    // 工序间隔时间不足(成型工序的计划结束时间 ➖ 镀膜工序的计划结束时间 <= 成型工序最小间隔时间(4H))
                    // 防氧化规则违反预警(成型工序的计划结束时间 ➖ 镀膜工序的计划结束时间 > 镀膜后成型完成时间配置(48H))
                    if (subtractHourTime.compareTo(BigDecimal.valueOf(4)) <= 0 || subtractHourTime.compareTo(BigDecimal.valueOf(48)) > 0) {
                        masterPlanWorkOrderBodyVO.setErrorColour(WARNING_COLOUR);
                    }
                }
            }
        }
        DynamicDataSourceContextHolder.clearDataSource();
    }

    private List<MasterPlanWorkOrderBodyVO> filterAndSortResult(MasterPlanReq masterPlanReq, List<MasterPlanWorkOrderBodyVO> result) {
        if (masterPlanReq.getDeliverStartTime() != null && masterPlanReq.getDeliverEndTime() != null) {
            String planStatus = masterPlanReq.getPlanStatus();
            if (StringUtils.isNotBlank(planStatus) && planStatus.equals(OperationEnum.PLANNED.getCode())) {
                result = filterPlan(masterPlanReq, result);
            } else if (StringUtils.isBlank(planStatus)) {
                List<MasterPlanWorkOrderBodyVO> unPlan = filterByPlanDate(result, false);
                List<MasterPlanWorkOrderBodyVO> plan = filterByPlanDate(result, true);
                List<MasterPlanWorkOrderBodyVO> filteredPlan = filterPlan(masterPlanReq, plan);
                unPlan.addAll(filteredPlan);
                result = unPlan;
            }
        }
        String killStatus = masterPlanReq.getKillStatus();
        if (StringUtils.isNotBlank(killStatus)) {
            result = result.stream().filter(p -> StringUtils.isNotBlank(p.getKitStatusCode()) && p.getKitStatusCode().equals(killStatus)).collect(Collectors.toList());
        }
        String planOperation = masterPlanReq.getPlanOperation();
        if (StringUtils.isNotBlank(planOperation)) {
            result = result.stream().filter(p -> StringUtils.isNotBlank(p.getStandardStepId()) && p.getStandardStepId().equals(planOperation)).collect(Collectors.toList());
        }
        String vehicleModelCode = masterPlanReq.getVehicleModelCode();
        if (StringUtils.isNotBlank(vehicleModelCode)) {
            result = result.stream().filter(p -> StringUtils.isNotBlank(p.getProductType()) && p.getProductType().equals(vehicleModelCode)).collect(Collectors.toList());
        }
        String orderBy = masterPlanReq.getOrderBy();
        if (StringUtils.isNotBlank(orderBy)) {
            com.yhl.platform.common.utils.CollectionUtils.sort(result, orderBy);
        }
        return result;
    }

    private List<MasterPlanWorkOrderBodyVO> filterPlan(MasterPlanReq masterPlanReq, List<MasterPlanWorkOrderBodyVO> result) {
        return result.stream().filter(p -> StringUtils.isNotBlank(p.getPlanDate()) && ((p.getPlanDateTime().getTime() >= masterPlanReq.getDeliverStartTime().getTime() && p.getPlanDateTime().getTime() <= masterPlanReq.getDeliverEndTime().getTime()) || (p.getPlanEndDateTime().getTime() >= masterPlanReq.getDeliverStartTime().getTime() && p.getPlanEndDateTime().getTime() <= masterPlanReq.getDeliverEndTime().getTime()))).collect(Collectors.toList());
    }

    private List<MasterPlanWorkOrderBodyVO> filterByPlanDate(List<MasterPlanWorkOrderBodyVO> result, boolean isPlan) {
        result.removeIf(Objects::isNull);
        return result.stream().filter(p -> isPlan == StringUtils.isNotBlank(p.getPlanDate())).collect(Collectors.toList());
    }

    private void getUnPlanOperations(List<MasterPlanWorkOrderBodyVO> result,
                                     List<OperationPlanVO> unPlanOperations,
                                     Map<String, NewProductStockPointVO> productMap,
                                     Map<String, List<InventoryBatchDetailVO>> operationInventoryMap,
                                     Map<String, String> standardStepMap,
                                     Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap,
                                     Map<String, BigDecimal> orderId2ComprehensiveYieldMap,
                                     Map<String, SubInventoryCargoLocationVO> cargoLocationMap,
                                     Map<String, String> productCodeVehicleCodeMap,
                                     Map<String, WorkOrderPO> workOrderMap,
                                     Map<String, List<DeliveryPlanVO2>> deliveryPlanMap,
                                     Map<String, List<InventoryBatchDetailVO>> finishInventoryMap,
                                     Map<String, List<InventoryBatchDetailVO>> semiFinishInventoryMap,
                                     List<Date> intervalDates, List<String> dynamicHeaders,
                                     String scenario, String userId, String specialStockPoint, String specialStockPoint2) {
        DynamicDataSourceContextHolder.setDataSource(scenario);
        if (CollectionUtils.isEmpty(unPlanOperations)) {
            return;
        }
        // 查询登陆人权限物料
        // TODO 需要切换成通用数据权限方式，保证非生产计划员也能查到数据
        List<String> plannerProduct = newMdsFeign.getPlannerProduct(scenario, userId);
        for (OperationPlanVO operationPlanVO : unPlanOperations) {
            String orderId = operationPlanVO.getOrderId();
            WorkOrderPO workOrder = workOrderMap.get(orderId);
            String productId = operationPlanVO.getProductId();
            NewProductStockPointVO newProductStockPointVO = productMap.get(productId);
            String productCode = newProductStockPointVO.getProductCode();
            if (!plannerProduct.contains(productCode)) {
                continue;
            }
            String topWorkOrderNumber = null;
            String topTestOrderNumber = null;
            String parentProductCode = productCode;
            if (Objects.nonNull(workOrder) && StringUtils.isNotBlank(workOrder.getTopOrderId())) {
                WorkOrderPO parentOrder = workOrderMap.get(workOrder.getTopOrderId());
                if (Objects.nonNull(parentOrder)) {
                    topWorkOrderNumber = parentOrder.getOrderNo();
                    topTestOrderNumber = parentOrder.getTestOrderNumber();
                    NewProductStockPointVO parentProduct = productMap.get(parentOrder.getProductId());
                    parentProductCode = parentProduct.getProductCode();
                }
            }

            String stockPointCode = newProductStockPointVO.getStockPointCode();
            // Get delivery plan details
            List<DeliveryPlanVO2> detailList = deliveryPlanMap.getOrDefault(parentProductCode, new ArrayList<>());
            Map<String, Integer> qtyMap = detailList.stream().collect(Collectors.groupingBy(p -> DateUtils.dateToString(p.getDemandTime(), DateUtils.COMMON_DATE_STR3), Collectors.summingInt(DeliveryPlanVO2::getDemandQuantity)));
            Map<String, Integer> originalQtyMap = new HashMap<>(qtyMap);
            // 产品工艺基础数据
            MdsProductStockPointBaseVO mdsProductStockPointBaseVO = productStockPointBaseMap.containsKey(newProductStockPointVO.getProductCode()) ? productStockPointBaseMap.get(newProductStockPointVO.getProductCode()) : new MdsProductStockPointBaseVO();
            // 工序在制量，获取工序结果
            String ycl = getInventory(PRE_OPERATION, newProductStockPointVO, standardStepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
            String ys = getInventory(PAINTING_OPERATION, newProductStockPointVO, standardStepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
            String cx = getInventory(FORMING_OPERATION, newProductStockPointVO, standardStepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
            String hp = getInventory(MERGING_OPERATION, newProductStockPointVO, standardStepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
            String bz = getInventory(PACKAGING_OPERATION, newProductStockPointVO, standardStepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
            double totalQuantity = Arrays.stream(new String[]{cx, hp, bz})
                    .mapToDouble(Double::parseDouble).reduce(Double::sum).orElse(0);
            String vehicleModelCode = productCodeVehicleCodeMap.getOrDefault(productCode, null);
            MasterPlanWorkOrderBodyVO masterPlanWorkOrderBodyVO = MasterPlanWorkOrderBodyVO.builder().dueDate(operationPlanVO.getWorkOrderDuedate()).parentProductCode(parentProductCode).standardStepId(operationPlanVO.getStandardStepId()).standardStepCode(operationPlanVO.getStandardStepCode()).resourceId("not_resource_id").resourceName("无").resourceCode("无").operationId(operationPlanVO.getId()).operationName(operationPlanVO.getStandardStepName()).operationCode(operationPlanVO.getRoutingStepSequenceNo()).productCode(newProductStockPointVO.getProductCode()).productName(newProductStockPointVO.getProductName()).productId(newProductStockPointVO.getId()).productType(vehicleModelCode).moldChangeTime("0").packagingMethod(operationPlanVO.getBoxType())
                    // 实时库存数据
                    .preTreatment(ycl).printing(ys).postPackaging(bz).postLamination(hp).suppress(cx).totalTime(String.format("%.0f", totalQuantity)).productionTime("0h")
                    // 产品工艺基础数据表 获取值
                    .grilleType(mdsProductStockPointBaseVO.getGridType()).productionMode(mdsProductStockPointBaseVO.getProductionModel()).hud(mdsProductStockPointBaseVO.getHud()).wired(mdsProductStockPointBaseVO.getClampType()).heaterWire(mdsProductStockPointBaseVO.getSealEdge()).weight(null != mdsProductStockPointBaseVO.getProductWidth() ? mdsProductStockPointBaseVO.getProductWidth().stripTrailingZeros().toPlainString() : "0").length(null != mdsProductStockPointBaseVO.getProductLength() ? mdsProductStockPointBaseVO.getProductLength().stripTrailingZeros().toPlainString() : "0").color(mdsProductStockPointBaseVO.getProductColor())
                    // 排产数量
                    .plannedQuantity(operationPlanVO.getQuantity().stripTrailingZeros().toPlainString())
                    // 根据报工反馈判断
                    .workOrderStatus("未执行").delay("延期").workOrderNumber(operationPlanVO.getOrderCode()).topWorkOrderNumber(topWorkOrderNumber).completeSetQuantity(BigDecimal.ZERO).kitStatus("未齐套").kitStatusCode(operationPlanVO.getWorkOrderKitStatus()).beat("0").planStatus(EnumUtils.getDescByCode(OperationEnum.class, operationPlanVO.getPlanStatus())).dynamicData(new ArrayList<>()).comprehensiveYield(orderId2ComprehensiveYieldMap.get(orderId)).header(dynamicHeaders).testOrderNumber(topTestOrderNumber).orderType(workOrder != null ? workOrder.getOrderType() : null).finishInventory("0").semiFinishInventory("0").unPlan(true).remark(operationPlanVO.getRemark()).build();
            assembleInventory(parentProductCode, stockPointCode, productCode, finishInventoryMap, semiFinishInventoryMap, cargoLocationMap, masterPlanWorkOrderBodyVO);
            unPlanAssembleMasterPlanSubList(intervalDates, originalQtyMap, masterPlanWorkOrderBodyVO);
            result.add(masterPlanWorkOrderBodyVO);
        }
        DynamicDataSourceContextHolder.clearDataSource();
    }

    public BigDecimal convertSecondsToHours(BigDecimal seconds) {
        if (null == seconds) {
            return BigDecimal.ZERO;
        }
        BigDecimal secondsInHour = new BigDecimal("3600");
        return seconds.divide(secondsInHour, 1, RoundingMode.HALF_UP);
    }

    private static String getInventory(String op, NewProductStockPointVO newProductStockPointVO, Map<String, String> stepMap,
                                       Map<String, List<InventoryBatchDetailVO>> inventoryMap,
                                       Map<String, SubInventoryCargoLocationVO> cargoLocationMap,
                                       String specialStockPoint, String specialStockPoint2, String parentProductCode) {
        if (Objects.isNull(newProductStockPointVO)) {
            return "0";
        }

        String stockPointCode = newProductStockPointVO.getStockPointCode();
        if (specialStockPoint.equals(stockPointCode) && op.equals(PAINTING_OPERATION)) {
            op = "镀膜";
        }
        if (specialStockPoint.equals(stockPointCode) && op.equals(FORMING_OPERATION)) {
            op = "压制";
        }
        if (specialStockPoint2.equals(stockPointCode) && op.equals(FORMING_OPERATION)) {
            op = "钢化";
        }
        // 工序代码
        String op1 = stepMap.get(stockPointCode + op);
        String opMainKey = String.join("-", stockPointCode, newProductStockPointVO.getProductCode(), op1);
        String opMainKey1 = String.join("-", stockPointCode, newProductStockPointVO.getProductCode(), "null");
        String finalOp = op;
        List<InventoryBatchDetailVO> inventoryBatchDetails = new ArrayList<>();
        boolean parentFlag = MERGING_OPERATION.equals(op) || PACKAGING_OPERATION.equals(op);
        if (parentFlag && !newProductStockPointVO.getProductCode().equals(parentProductCode) && StringUtils.isNotEmpty(parentProductCode)) {
            String parentOpMainKey = String.join("-", stockPointCode, parentProductCode, op1);
            String parentOpMainKey1 = String.join("-", stockPointCode, parentProductCode, "null");
            inventoryBatchDetails = inventoryMap.entrySet().stream()
                    .filter(entry -> entry.getKey().equals(parentOpMainKey)
                            || (PACKAGING_OPERATION.equals(finalOp) && entry.getKey().equals(parentOpMainKey1)))
                    .map(Map.Entry::getValue).flatMap(List::stream).filter(p -> {
                        String freightSpace = p.getFreightSpace();
                        SubInventoryCargoLocationVO subInventoryCargoLocation = cargoLocationMap.get(freightSpace);
                        return null != subInventoryCargoLocation;
                    }).collect(Collectors.toList());
        } else {
            inventoryBatchDetails = inventoryMap.entrySet().stream()
                    .filter(entry -> entry.getKey().equals(opMainKey)
                            || (PACKAGING_OPERATION.equals(finalOp) && entry.getKey().equals(opMainKey1)))
                    .map(Map.Entry::getValue).flatMap(List::stream).filter(p -> {
                        String freightSpace = p.getFreightSpace();
                        SubInventoryCargoLocationVO subInventoryCargoLocation = cargoLocationMap.get(freightSpace);
                        return null != subInventoryCargoLocation;
                    }).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(inventoryBatchDetails)) {
            return "0";
        }
        OptionalDouble sum = inventoryBatchDetails.stream()
                .mapToDouble(t -> Double.parseDouble(t.getCurrentQuantity())).reduce(Double::sum);
        double totalQuantity = sum.orElse(0);
        return String.format("%.0f", totalQuantity);
    }

    @Override
    public List<LabelValue<String>> selectStandardResourceDropdown(String organizationCode) {
        String userId = SystemHolder.getUserId();
        List<StandardResourceVO> standardResourceVOS = operationTaskExtDao.selectStandardResource(organizationCode, userId);
        return standardResourceVOS.stream().map(item -> new LabelValue<>(item.getStandardResourceCode() + "-" + item.getStandardResourceName(), item.getId())).collect(Collectors.toList());
    }

    @Override
    public List<LabelValueThree<String>> selectStandardResourceDropdown2(String organizationCode) {
        String userId = SystemHolder.getUserId();
        List<StandardResourceVO> standardResourceVOS = operationTaskExtDao.selectStandardResource(organizationCode, userId);
        if (CollectionUtils.isEmpty(standardResourceVOS)) {
            return Collections.emptyList();
        }
        return standardResourceVOS.stream().map(item -> new LabelValueThree<>(item.getStandardResourceCode(), item.getStandardResourceName(), item.getId())).collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> selectPhysicalResourceDropdown(String organizationCode, String standardResourceId) {
        List<PhysicalResourceVO> physicalResourceVOS = operationTaskExtDao.selectPhysicalResource(organizationCode, standardResourceId, null);
        return physicalResourceVOS.stream().map(item -> new LabelValue<>(item.getPhysicalResourceCode() + "-" + item.getPhysicalResourceName(), item.getId())).collect(Collectors.toList());
    }

    @Override
    public List<LabelValueThree<String>> selectPhysicalResourceDropdown2(String organizationCode, String standardResourceId) {
        List<PhysicalResourceVO> physicalResourceVOS = operationTaskExtDao.selectPhysicalResource(organizationCode, standardResourceId, null);
        if (CollectionUtils.isEmpty(physicalResourceVOS)) {
            return Collections.emptyList();
        }
        return physicalResourceVOS.stream().map(item -> new LabelValueThree<>(item.getPhysicalResourceName(), item.getPhysicalResourceCode(), item.getId())).collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> operationStepTwo() {
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(SystemHolder.getScenario());
        return standardStepVOS.stream().filter(standardStepVO -> StringUtils.isNotBlank(standardStepVO.getStandardStepType()) && !NORMAL_OPERATION.equals(standardStepVO.getStandardStepType())).map(standardStepVO -> {
            String key = String.join("-", standardStepVO.getStockPointCode(), standardStepVO.getStandardStepCode(), standardStepVO.getStandardStepName());
            return new LabelValue<>(key, standardStepVO.getStandardStepCode());
        }).collect(Collectors.toList());
    }

    @Override
    public PageInfo<MasterPlanWorkOrderVO> masterPlanWorkOrder2(Pagination pagination, MasterPlanReq masterPlanReq) {
        String scenario = SystemHolder.getScenario();
        // 0.查询计划期间
        PlanningHorizonVO planningHorizon = newMdsFeign.selectPlanningHorizon(scenario);
        // 1.组装查询请求对象
        assembleQueryRequest(masterPlanReq, planningHorizon, false, scenario);
        if (CollectionUtils.isEmpty(masterPlanReq.getPhysicalResourceIds())) {
            log.info("未查询到物理资源id");
            return new PageInfo<>();
        }
        DynamicDataSourceContextHolder.setDataSource(scenario);
        // 2.查询工序任务
        // operationTask有父子工序之分，父工序如有1000，可能被切割成500和500两个子工序，排产日期不一致，按照每段去覆盖发货计划
        List<MasterPlanTaskVO> operationTasks = operationTaskExtDao.selectByMasterReq2(masterPlanReq);
        if (CollectionUtils.isEmpty(operationTasks)) {
            return new PageInfo<>();
        }
        List<String> operationIds = operationTasks.stream().map(OperationTaskVO::getOperationId).distinct().collect(Collectors.toList());
        // 查询计划资源
        List<String> resourceIds = operationTasks.stream().map(MasterPlanTaskVO::getPhysicalResourceId).distinct().collect(Collectors.toList());
        List<PhysicalResourceVO> physicalResources = newMdsFeign.selectByPhysicalIds(scenario, resourceIds);
        List<String> physicalResourceCodes = physicalResources.stream().map(PhysicalResourceVO::getPhysicalResourceCode).distinct().collect(Collectors.toList());
        // 工具资源MAP
        Map<String, PhysicalResourceVO> toolResourceMap = physicalResources.stream().filter(t -> ResourceCategoryEnum.TOOL.getCode().equals(t.getResourceCategory())).collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v1));
        List<String> toolResourceIds = new ArrayList<>(toolResourceMap.keySet());
        // 工序ID-工具资源ID
        List<MasterPlanTaskVO> physicalToolOperation = operationTaskExtDao.selectPhysicalOperation2(operationIds);
        Map<String, String> operation2ToolResourceMap = physicalToolOperation.stream().filter(t -> toolResourceIds.contains(t.getPhysicalResourceId())).collect(Collectors.toMap(OperationTaskBasicVO::getOperationId, OperationTaskBasicVO::getPhysicalResourceId, (v1, v2) -> v2));
        // 过滤掉工具资源
        Map<String, PhysicalResourceVO> physicalResourceVOMap = physicalResources.stream().filter(t -> !ResourceCategoryEnum.TOOL.getCode().equals(t.getResourceCategory())).collect(Collectors.toMap(PhysicalResourceVO::getId, Function.identity(), (v1, v2) -> v1));
        operationTasks = operationTasks.stream().filter(t -> !toolResourceIds.contains(t.getPhysicalResourceId())).collect(Collectors.toList());
        // 工序任务对应的子工序，主要获取生产数量
        List<OperationPO> operations = CollectionUtils.isEmpty(operationIds) ? new ArrayList<>() : operationExtDao.selectByIds2(operationIds);
        List<String> stepIds = operations.stream().map(OperationPO::getRoutingStepId).distinct().collect(Collectors.toList());

        Map<String, RoutingStepResourceVO> stepResourceMap = CollectionUtils.isEmpty(stepIds) ? new HashMap<>() : newMdsFeign.selectRoutingStepResourceByRoutingStepIds(scenario, stepIds).stream().collect(Collectors.toMap(p -> String.join("&", p.getRoutingStepId(), p.getPhysicalResourceId()), Function.identity(), (v1, v2) -> v1));
        Map<String, OperationPO> operationMap = operations.stream().collect(Collectors.toMap(OperationPO::getId, Function.identity(), (v1, v2) -> v1));
        // 组装工序任务
        taskAssembleLoop(operationTasks, operationMap, stepResourceMap);
        Map<String, List<MasterPlanTaskVO>> resourceTaskGroup = operationTasks.stream().filter(task -> task.getPhysicalResourceId() != null).collect(Collectors.groupingBy(MasterPlanTaskVO::getPhysicalResourceId));
        // 制造订单
        List<WorkOrderPO> workOrders = operationExtDao.selectPublishWorkOrder();
        // 物品&产品工艺基础数据
        List<String> productIds = operations.stream().map(OperationPO::getProductId).distinct().collect(Collectors.toList());
        productIds.addAll(workOrders.stream().map(WorkOrderPO::getProductId).collect(Collectors.toList()));
        productIds = productIds.stream().distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> newProductStockPoints = newMdsFeign.selectProductStockPointByIds(scenario, productIds);
        if (CollectionUtils.isEmpty(newProductStockPoints)) {
            log.warn("工序对应物料信息不存在，终止查询");
            return new PageInfo<>();
        }
        List<String> productCodes = newProductStockPoints.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
        newProductStockPoints = newMdsFeign.selectByProductCode(SystemHolder.getScenario(), productCodes);
        // 车型映射
        Map<String, NewProductStockPointVO> productMap = newProductStockPoints.stream().collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity(), (v1, v2) -> v1));
        // 物品车型映射
        Map<String, String> productCodeVehicleCodeMap = newProductStockPoints.stream().filter(x -> StringUtils.isNotBlank(x.getVehicleModelCode())).collect(Collectors.toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getVehicleModelCode, (v1, v2) -> v2));
        Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap = newMdsFeign.selectProductStockPointBaseByParams(scenario, ImmutableMap.of(PARAM_PRODUCT_CODE_LIST, productCodes)).stream().collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, Function.identity(), (v1, v2) -> v1));
        // 换模换型时间
        Map<String, MoldChangeTimeVO> moldChangeTimeMap = moldChangeTimeService.selectByParams(ImmutableMap.of("resourceCodes", physicalResourceCodes, PARAM_PRODUCT_CODES, productCodes)).stream().filter(p -> null != p.getDieChangeTime()).collect(Collectors.toMap(p -> String.join("&", p.getOperationCode(), p.getResourceCode(), p.getProductCode()), Function.identity(), (v1, v2) -> v1));
        // 产品工艺基础数据
        List<StandardStepVO> standardSteps = newMdsFeign.selectStandardStepAll(scenario);
        Map<String, String> stepMap = standardSteps.stream().collect(Collectors.toMap(p -> p.getStockPointCode() + p.getStandardStepName(), StandardStepBasicVO::getStandardStepCode, (v1, v2) -> v1));
        // 实时库存数据
        List<NewStockPointVO> newStockPoints = newMdsFeign.selectStockPointByParams(scenario, ImmutableMap.of(PARAM_STOCK_POINT_TYPE, StockPointTypeEnum.BC.getCode()));
        // 获取本厂销售组织类型的仓库(成品库存点)
        List<String> bcSaleStockPointList = newStockPoints.stream().filter(e -> StringUtils.isNotEmpty(e.getOrganizeType()) && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType())).map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        // 获取本厂生产组织类型的仓库(半成品库存点)
        List<String> bcProductStockPointList = newStockPoints.stream().filter(e -> StringUtils.isNotEmpty(e.getOrganizeType()) && StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode().equals(e.getOrganizeType())).map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

        List<String> bcStockPointList = newStockPoints.stream().map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        List<InventoryBatchDetailVO> inventoryBatchDetails = dfpFeign.selectInventoryDataByProductCodes(scenario, productCodes, StockPointTypeEnum.BC.getCode()).stream().filter(p -> bcStockPointList.contains(p.getStockPointCode())).collect(Collectors.toList());
        List<String> spaceList = inventoryBatchDetails.stream().map(InventoryBatchDetailVO::getFreightSpace).distinct().collect(Collectors.toList());
        // 查询可用本厂有效货位库存
        Map<String, SubInventoryCargoLocationVO> cargoLocationMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(spaceList)) {
            // 缩小时间范围
            cargoLocationMap = subInventoryCargoLocationDao.selectByBatchCodeAndStockType(spaceList, StockPointTypeEnum.BC.getCode()).stream().collect(Collectors.toMap(SubInventoryCargoLocationVO::getFreightSpaceCode, Function.identity(), (v1, v2) -> v1));
        }
        // 成品库存
        Map<String, List<InventoryBatchDetailVO>> finishInventoryMap = inventoryBatchDetails.stream().filter(t -> StringUtils.isEmpty(t.getOperationCode()) && bcSaleStockPointList.contains(t.getStockPointCode()) && SUB_INVENTORY_CPSJ.equals(t.getSubinventory())).collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));
        // 半品库存
        Map<String, List<InventoryBatchDetailVO>> semiFinishInventoryMap = inventoryBatchDetails.stream().filter(t -> StringUtils.isEmpty(t.getOperationCode()) && bcProductStockPointList.contains(t.getStockPointCode())).collect(Collectors.groupingBy(p -> String.join("-", p.getStockPointCode(), p.getProductCode())));
        // 工序在制量
        Map<String, List<InventoryBatchDetailVO>> operationInventoryMap = inventoryBatchDetails.stream().filter(t -> bcProductStockPointList.contains(t.getStockPointCode())).collect(Collectors.groupingBy(p -> String.join("-", p.getStockPointCode(), p.getProductCode(), p.getOperationCode())));

        // 获取已计划工序+未计划工序的工艺路径及步骤
        List<String> routingIds = workOrders.stream().map(WorkOrderPO::getRoutingId).filter(routingId -> !StringUtils.isBlank(routingId)).distinct().collect(Collectors.toList());
        List<RoutingStepVO> routingStepByRoutingIds = newMdsFeign.getRoutingStepByRoutingIds(scenario, routingIds);
        Map<String, List<RoutingStepVO>> routingStepMap = routingStepByRoutingIds.stream().collect(Collectors.groupingBy(RoutingStepVO::getRoutingId));
        Map<String, BigDecimal> routingStepOrder = workOrders.stream().filter(workOrderPO -> !StringUtils.isBlank(workOrderPO.getRoutingId())).collect(Collectors.toMap(WorkOrderPO::getId, workOrderPO -> routingStepMap.getOrDefault(workOrderPO.getRoutingId(), new ArrayList<>()).stream().map(RoutingStepVO::getYield).reduce(BigDecimal.ONE, BigDecimal::multiply).setScale(1, RoundingMode.HALF_UP), (v1, v2) -> v2));
        Map<String, WorkOrderPO> workOrderMap = workOrders.stream().collect(Collectors.toMap(WorkOrderPO::getId, Function.identity(), (v1, v2) -> v1));

        // 发货计划时间轴，此刻开始往后30天的日期;使用计划期间开始结束构建时间轴
        Date planStartTime = planningHorizon.getPlanStartTime();
        Date planEndTime = planningHorizon.getPlanEndTime();
        List<Date> intervalDates = DateUtils.getIntervalDates(planStartTime, planEndTime);
        // 材料安全库存
        // 物品候选资源生产节拍
        Map<String, ProductCandidateResourceVO> productCandidateResourceMap = newMdsFeign.selectProductCandidateResourceByParams(scenario, ImmutableMap.of("productIds", productIds, "physicalResourceIds", resourceIds)).stream().collect(Collectors.toMap(p -> String.join("&", p.getProductId(), p.getStandardStepId(), p.getPhysicalResourceId()), Function.identity(), (v1, v2) -> v1));
        List<String> header = intervalDates.stream().map(p -> DateUtils.dateToString(p, DateUtils.COMMON_DATE_STR3)).collect(Collectors.toList());
        // 结果集
        List<MasterPlanWorkOrderBodyVO> result = new ArrayList<>();
        String specialStockPoint = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT"));
        String specialStockPoint2 = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT_S2").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT_S2"));

        // 查询零件风险等级
        log.info("基础数据查询完毕");
        for (Map.Entry<String, PhysicalResourceVO> entry : physicalResourceVOMap.entrySet()) {
            // 排程资源
            PhysicalResourceVO physicalResource = entry.getValue();
            // 当前资源排程任务，存在多个物料，平摊每个物料的发货计划
            List<MasterPlanTaskVO> operationTaskOnResource = resourceTaskGroup.get(physicalResource.getId());
            Map<String, List<MasterPlanTaskVO>> productTaskMap = operationTaskOnResource.stream().collect(Collectors.groupingBy(p -> p.getOperationVO().getProductId()));
            // 分组每个物料对应的工序任务信息，平摊发货计划
            for (Map.Entry<String, List<MasterPlanTaskVO>> productTaskEntry : productTaskMap.entrySet()) {
                List<MasterPlanTaskVO> productTaskEntryValue = productTaskEntry.getValue();
                productTaskEntryValue.sort(Comparator.comparing(OperationTaskVO::getStartTime));
                NewProductStockPointVO newProductStockPointVO = productMap.get(productTaskEntry.getKey());
                // 发货计划物品信息
                String parentProductCode = newProductStockPointVO.getProductCode();
                if (CollectionUtils.isNotEmpty(productTaskEntryValue)) {
                    String orderId = productTaskEntryValue.get(0).getOperationVO().getOrderId();
                    WorkOrderPO workOrderPO = workOrderMap.get(orderId);
                    if (workOrderPO != null) {
                        String topOrderId = workOrderPO.getTopOrderId();
                        if (StringUtils.isNotBlank(topOrderId)) {
                            WorkOrderPO parentOrder = workOrderMap.get(topOrderId);
                            if (parentOrder != null) {
                                String productId = parentOrder.getProductId();
                                NewProductStockPointVO parentWorkOrderProduct = productMap.get(productId);
                                if (parentWorkOrderProduct != null) {
                                    parentProductCode = parentWorkOrderProduct.getProductCode();
                                }
                            }
                        }
                    }
                }
                for (MasterPlanTaskVO masterOperationTaskVO : productTaskEntryValue) {
                    String resourceKey = String.join("&", newProductStockPointVO.getId(), masterOperationTaskVO.getStandardStepId(), masterOperationTaskVO.getPhysicalResourceId());
                    // 工序任务对应工序(父工序)
                    OperationVO operationVO = masterOperationTaskVO.getOperationVO();
                    String parentId = operationVO.getParentId();
                    OperationPO parentOperation = operationMap.get(parentId);
                    // 制造订单
                    WorkOrderPO workOrderPO = workOrderMap.get(operationVO.getOrderId());
                    String mainKey = String.join("&", operationVO.getOperationCode(), physicalResource.getPhysicalResourceCode(), newProductStockPointVO.getProductCode());
                    // 换模时间
                    long moldTime = 0L;
                    MoldChangeTimeVO moldChangeTimeVO = moldChangeTimeMap.get(mainKey);
                    if (null != moldChangeTimeVO) {
                        Long dieChangeTime = moldChangeTimeVO.getDieChangeTime();
                        if (null != dieChangeTime) {
                            moldTime = dieChangeTime;
                        }
                    }
                    // 产品工艺基础数据
                    MdsProductStockPointBaseVO mdsProductStockPointBaseVO = productStockPointBaseMap.containsKey(newProductStockPointVO.getProductCode()) ? productStockPointBaseMap.get(newProductStockPointVO.getProductCode()) : new MdsProductStockPointBaseVO();
                    // 工序在制量，获取工序结果
                    String ycl = getInventory(PRE_OPERATION, newProductStockPointVO, stepMap, operationInventoryMap,
                            cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
                    String ys = getInventory(PAINTING_OPERATION, newProductStockPointVO, stepMap, operationInventoryMap,
                            cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
                    String cx = getInventory(FORMING_OPERATION, newProductStockPointVO, stepMap, operationInventoryMap,
                            cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
                    String hp = getInventory(MERGING_OPERATION, newProductStockPointVO, stepMap, operationInventoryMap,
                            cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
                    String bz = getInventory(PACKAGING_OPERATION, newProductStockPointVO, stepMap, operationInventoryMap,
                            cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
                    OptionalDouble sum = Arrays.stream(new String[]{hp, cx})
                            .mapToDouble(Double::parseDouble).reduce(Double::sum);
                    double totalQuantity = sum.orElse(0.0);
                    String delay = "";
                    if (workOrderPO != null) {
                        delay = workOrderPO.getDueDate().getTime() < masterOperationTaskVO.getEndTime().getTime() ? "延期" : "未延期";
                    }
                    String toolResourceId = operation2ToolResourceMap.get(masterOperationTaskVO.getOperationId());
                    PhysicalResourceVO toolResourceVO = new PhysicalResourceVO();
                    if (StringUtils.isNotEmpty(toolResourceId)) {
                        toolResourceVO = toolResourceMap.getOrDefault(toolResourceId, new PhysicalResourceVO());
                    }
                    String vehicleModelCode = productCodeVehicleCodeMap.getOrDefault(parentProductCode, null);
                    MasterPlanWorkOrderBodyVO masterPlanWorkOrderBodyVO = assembleMasterPlanWorkOrderBodyVO(masterOperationTaskVO, parentProductCode, operationVO, workOrderPO, physicalResource, toolResourceVO, newProductStockPointVO, moldTime, ycl, ys, bz, hp, cx, totalQuantity, mdsProductStockPointBaseVO, routingStepOrder, delay, resourceKey, header, productCandidateResourceMap, finishInventoryMap, semiFinishInventoryMap, cargoLocationMap, vehicleModelCode, parentOperation);
                    result.add(masterPlanWorkOrderBodyVO);
                }
            }
        }
        // 设置风险等级
        List<String> fgCodes = result.stream().map(MasterPlanWorkOrderBodyVO::getParentProductCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, List<String>> partRiskLevelMap = operationTaskExtDao.selectPartRiskLevels(fgCodes).stream().collect(Collectors.groupingBy(PartRiskLevelVO::getProductCode, Collectors.mapping(PartRiskLevelVO::getMaterialRiskLevel, Collectors.toList())));
        Map<String, Integer> piecePerBoxMap = operationTaskExtDao.selectBoxPieces(fgCodes).stream().collect(Collectors.toMap(ProductBoxRelationVO::getProductCode, ProductBoxRelationVO::getPiecePerBox, (v1, v2) -> v1));
        // 设置风险等级和单箱片数
        for (MasterPlanWorkOrderBodyVO item : result) {
            String fgCode = item.getParentProductCode();
            item.setPartRiskLevel("低");
            if (partRiskLevelMap.containsKey(fgCode)) {
                if (partRiskLevelMap.get(fgCode).contains("高")) {
                    item.setPartRiskLevel("高");
                    continue;
                }
                if (partRiskLevelMap.get(fgCode).contains("中")) {
                    item.setPartRiskLevel("中");
                }
            }
            if (piecePerBoxMap.containsKey(fgCode)) {
                item.setPiecePerBox(piecePerBoxMap.get(fgCode));
            }
        }
        PageInfo pageInfo = PageUtils.getPageInfo(filterAndSortResult(masterPlanReq, result), masterPlanReq.getPageNum(), masterPlanReq.getPageSize());
        DynamicDataSourceContextHolder.clearDataSource();
        // 维护工序维度异常预警信息标识及动态内容，用于覆盖发货计划
        log.info("主生产计划工单查询结束");
        return pageInfo;
    }

    @Override
    public List<LabelValue<String>> listResourceDropDown02() {
        List<PhysicalResourceVO> physicalResourceVOS = operationTaskExtDao.selectPhysicalResource(null, null, null);
        return physicalResourceVOS.stream().map(item -> new LabelValue<>(item.getPhysicalResourceCode() + "-" + item.getPhysicalResourceName(), item.getPhysicalResourceCode())).collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> org() {
        List<ProductionOrganizationVO> productionOrganizationVOS = operationTaskExtDao.selectProductionOrganizationWithPermission(SystemHolder.getUserId());
        return productionOrganizationVOS.stream().map(item -> new LabelValue<>(item.getOrganizationCode(), item.getId())).sorted(Comparator.comparing(LabelValue::getLabel)).collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> standardResource() {
        List<StandardResourceVO> productionOrganizationVOS = operationTaskExtDao.selectResourcePermissions(SystemHolder.getUserId());
        List<LabelValue<String>> result = new ArrayList<>();
        result.add(new LabelValue<>("*", "*"));
        result.addAll(productionOrganizationVOS.stream().map(item -> new LabelValue<>(item.getStandardResourceCode(), item.getId())).sorted(Comparator.comparing(LabelValue::getLabel)).collect(Collectors.toList()));
        return result;
    }

    @Override
    public List<LabelValue<String>> physicalResource(List<String> standardResourceIds) {
        List<LabelValue<String>> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(standardResourceIds)) {
            if (standardResourceIds.contains("*")) {
                standardResourceIds = null;
            }
            List<PhysicalResourceVO> physicalResourceVOS = operationTaskExtDao.selectPhysicalResourcePermissions(SystemHolder.getUserId(), standardResourceIds);
            result.addAll(physicalResourceVOS.stream().map(item -> new LabelValue<>(item.getPhysicalResourceCode(), item.getId())).sorted(Comparator.comparing(LabelValue::getLabel)).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    public List<LabelValue<String>> ruleOrg() {
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(SystemHolder.getScenario(), "PRODUCT_ORGANIZATION", "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<String> rangeList = Arrays.asList(rangeData.split(","));
        return rangeList.stream().map(str -> new LabelValue<>(str, str)).collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> operationStep() {
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(SystemHolder.getScenario());
        return standardStepVOS.stream().filter(standardStepVO -> StringUtils.isNotBlank(standardStepVO.getStandardStepType()) && !NORMAL_OPERATION.equals(standardStepVO.getStandardStepType())).map(standardStepVO -> {
            String key = String.join("-", standardStepVO.getStockPointCode(), standardStepVO.getStandardStepCode(), standardStepVO.getStandardStepName());
            return new LabelValue<>(key, standardStepVO.getId());
        }).collect(Collectors.toList());
    }

    @Override
    @Deprecated
    public List<DeliveryPlanGeneralViewVO> getDeliveryPlanGeneralView(MasterPlanReq masterPlanReq) {
        String scenario = SystemHolder.getScenario();
        PlanningHorizonVO planningHorizon = newMdsFeign.selectPlanningHorizon(scenario);
        Date historyRetrospectStartTime = planningHorizon.getHistoryRetrospectStartTime();
        masterPlanReq.setHistoryRetrospectStartTime(historyRetrospectStartTime);
        List<DeliveryPlanGeneralViewVO> result = new ArrayList<>();
        // 工序任务
        List<MasterPlanTaskVO> operationTasks = operationTaskExtDao.selectByMasterReq(masterPlanReq);
        // 获取最新版发货计划
        Map<String, Object> params = new HashMap<>();
        params.put("productCode", masterPlanReq.getProductCode());
        // 支持多产品编码查询
        if (CollectionUtils.isNotEmpty(masterPlanReq.getProductCodes())) {
            params.clear();
            params.put(PARAM_PRODUCT_CODES, masterPlanReq.getProductCodes());
        }

        Date planStartTime = planningHorizon.getPlanStartTime();
        Date planEndTime = planningHorizon.getPlanEndTime();
        params.put("startTimeStr", DateUtils.dateToString(planStartTime, DateUtils.COMMON_DATE_STR1));
        params.put("endTimeStr", DateUtils.dateToString(planEndTime, DateUtils.COMMON_DATE_STR1));
        List<String> plannerProduct = newMdsFeign.getPlannerProduct(SystemHolder.getScenario(), SystemHolder.getUserId());
        List<DeliveryPlanVO2> deliveryPlanVO2s = dfpFeign.selectDeliveryPlanPublishedByParams(scenario, params).stream().filter(p -> plannerProduct.contains(p.getProductCode())).sorted(Comparator.comparing(DeliveryPlanVO2::getDemandTime)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deliveryPlanVO2s)) {
            return result;
        }
        Map<String, List<DeliveryPlanVO2>> deliveryPlanMap = deliveryPlanVO2s.stream().collect(Collectors.groupingBy(DeliveryPlanVO2::getProductCode));
        Map<String, List<DeliveryPlanVO2>> newDeliveryPlanMap = new HashMap<>();
        deliveryPlanMap.forEach((key, value) -> {
            boolean flag = value.stream().allMatch(e -> e != null && Objects.equals(e.getDemandQuantity(), 0));
            if (!flag) {
                newDeliveryPlanMap.put(key, value);
            }
        });
        List<String> deliveryProductCodes = new ArrayList<>(newDeliveryPlanMap.keySet());

        List<Date> intervalDates = DateUtils.getIntervalDates(planStartTime, planEndTime);
        // 查询库存点数据，用于过滤非本厂库存
        params = new HashMap<>();
        params.put(PARAM_STOCK_POINT_TYPE, StockPointTypeEnum.BC.getCode());
        List<NewStockPointVO> newStockPoints = newMdsFeign.selectStockPointByParams(scenario, params);
        List<String> bcStockPointList = newStockPoints.stream().map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

        // 分组数据
        Map<String, List<MasterPlanTaskVO>> productTaskGroup = operationTasks.stream().collect(Collectors.groupingBy(MasterPlanTaskVO::getProductStockPointCode));
        List<String> operationIds = operationTasks.stream().map(OperationTaskVO::getOperationId).distinct().collect(Collectors.toList());
        List<OperationPO> operations = CollectionUtils.isEmpty(operationIds) ? new ArrayList<>() : operationExtDao.selectByIds(operationIds);
        Map<String, OperationPO> operationMap = operations.stream().collect(Collectors.toMap(OperationPO::getId, Function.identity(), (v1, v2) -> v1));
        // 制造订单
        List<WorkOrderPO> workOrders = workOrderDao.selectByParams(new HashMap<>());

        List<String> productIds = operations.stream().map(OperationPO::getProductId).distinct().collect(Collectors.toList());
        productIds.addAll(workOrders.stream().map(WorkOrderPO::getProductId).collect(Collectors.toList()));
        productIds = productIds.stream().distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> newProductStockPoints = newMdsFeign.selectProductStockPointByIds(scenario, productIds);
        List<NewProductStockPointVO> deliveryProductStockPoints = newMdsFeign.selectByProductCode(scenario, deliveryProductCodes);
        newProductStockPoints.addAll(deliveryProductStockPoints);

        // 查询本厂编码对应数据，获取车型编码
        List<String> productCodes = newProductStockPoints.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
        Map<String, NewProductStockPointVO> productMap = newProductStockPoints.stream().collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity(), (v1, v2) -> v1));
        Map<String, NewProductStockPointVO> productCodeMap = newProductStockPoints.stream().collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (v1, v2) -> v1));
        Map<String, String> product2VehicleMap = newProductStockPoints.stream().filter(t -> StringUtils.isNotEmpty(t.getVehicleModelCode())).collect(Collectors.toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getVehicleModelCode, (v1, v2) -> v1));

        // 制造订单对应的子工序的完整制造时间10-50工序，20镀膜，30成型，10，40，50推出来
        // 组装半品-成品映射
        operationSetter(operationTasks, operationMap);
        Map<String, List<MasterPlanTaskVO>> workOrderTaskMap = operationTasks.stream().collect(Collectors.groupingBy(p -> p.getOperationVO().getOrderId()));
        // 材料安全库存
        Map<String, List<SafetyStockLevelVO>> safetyStockLevelMap = dfpFeign.selectSafetyStockLevelByProductCodeList(scenario, deliveryProductCodes).stream().collect(Collectors.groupingBy(p -> String.join("&", p.getStockPointId(), p.getProductCode())));
        // 查询库存数据
        List<InventoryBatchDetailVO> inventoryBatchDetails = dfpFeign.selectInventoryDataByProductCodes(scenario, productCodes, StockPointTypeEnum.BC.getCode());
        List<String> spaceList = inventoryBatchDetails.stream().map(InventoryBatchDetailVO::getFreightSpace).distinct().collect(Collectors.toList());
        // 查询可用本厂有效货位库存
        Map<String, SubInventoryCargoLocationVO> cargoLocationMap = CollectionUtils.isEmpty(spaceList) ? new HashMap<>() : subInventoryCargoLocationDao.selectByBatchCodeAndStockType(spaceList, StockPointTypeEnum.BC.getCode()).stream().collect(Collectors.toMap(SubInventoryCargoLocationVO::getFreightSpaceCode, Function.identity(), (v1, v2) -> v1));
        // 工序在制量
        Map<String, List<InventoryBatchDetailVO>> operationInventoryMap = inventoryBatchDetails.stream().filter(t -> StringUtils.isNotEmpty(t.getOperationCode()) && bcStockPointList.contains(t.getStockPointCode())).collect(Collectors.groupingBy(p -> String.join("-", p.getStockPointCode(), p.getProductCode(), p.getOperationCode())));
        // 成品 & 半品库存
        Map<String, List<InventoryBatchDetailVO>> inventoryMap = inventoryBatchDetails.stream().filter(t -> StringUtils.isEmpty(t.getOperationCode()) && bcStockPointList.contains(t.getStockPointCode())).collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));

        // 发货记录数据
        Map<String, BigDecimal> warehouseReleaseRecordMap = operationTaskExtDao.selectWarehouseReleaseRecord(deliveryProductCodes).stream().collect(Collectors.toMap(WarehouseReleaseRecordVO::getItemCode, WarehouseReleaseRecordVO::getSumQty, (v1, v2) -> v1));
        // 产品工艺基础数据
        Map<String, String> stepMap = newMdsFeign.selectStandardStepAll(scenario).stream().collect(Collectors.toMap(p -> p.getStockPointCode() + p.getStandardStepName(), StandardStepBasicVO::getStandardStepCode, (v1, v2) -> v2));

        String specialStockPoint = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT"));
        String specialStockPoint2 = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT_S2").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT_S2"));

        // 取值切换为末道工序
        for (String productCode : deliveryProductCodes) {
            DeliveryPlanGeneralViewVO viewVO = new DeliveryPlanGeneralViewVO();
            viewVO.setProductCode(productCode);
            viewVO.setSumQty(BigDecimal.ZERO);
            // 设置发货量
            if (warehouseReleaseRecordMap.containsKey(productCode)) {
                viewVO.setSumQty(warehouseReleaseRecordMap.get(productCode));
            }
            // 初始化成品 & 半品库存
            viewVO.setFinishInventory("0");
            viewVO.setSemiFinishedGoodsInventory("0");

            // 发货计划物品信息
            List<DeliveryPlanVO2> detailList = newDeliveryPlanMap.getOrDefault(productCode, new ArrayList<>());
            Map<String, List<DeliveryPlanVO2>> deliveryPlanTimeGroup = detailList.stream().collect(Collectors.groupingBy(p -> DateUtils.dateToString(p.getDemandTime(), DateUtils.COMMON_DATE_STR3)));

            // 映射需求数量
            Map<String, Integer> demandQtyMap = detailList.stream().collect(Collectors.groupingBy(p -> DateUtils.dateToString(p.getDemandTime(), DateUtils.COMMON_DATE_STR3), Collectors.summingInt(DeliveryPlanVO2::getDemandQuantity)));
            int bohStock = viewVO.getSumQty().intValue();

            NewProductStockPointVO productItem = productCodeMap.get(productCode);
            String cx = getInventory(FORMING_OPERATION, productItem, stepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, productCode);
            String hp = getInventory(MERGING_OPERATION, productItem, stepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, productCode);
            String bz = getInventory(PACKAGING_OPERATION, productItem, stepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, productCode);
            OptionalDouble sum = Arrays.stream(new String[]{bz, hp, cx})
                    .mapToDouble(Double::parseDouble).reduce(Double::sum);
            String semiInventory = new BigDecimal(String.valueOf(sum.getAsDouble())).stripTrailingZeros().toPlainString();
            viewVO.setSemiFinishedGoodsInventory(semiInventory);
            BigDecimal finishedInventory = getFinishInventory(inventoryMap.get(productCode), cargoLocationMap).stream().map(t -> new BigDecimal(t.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 设置成品库存
            viewVO.setFinishInventory(finishedInventory.stripTrailingZeros().toPlainString());
            // 先使用仓库收发货 + 成品库存 + 半品库存映射需求数量
            bohStock = bohStock + finishedInventory.intValue() + new BigDecimal(semiInventory).intValue();

            // 使用期初冲减需求
            for (Date date : intervalDates) {
                String currentDate = DateUtils.dateToString(date, DateUtils.COMMON_DATE_STR3);
                if (!demandQtyMap.containsKey(currentDate)) {
                    demandQtyMap.put(currentDate, 0);
                    continue;
                }
                Integer demandQty = demandQtyMap.get(currentDate);
                if (demandQty <= bohStock) {
                    bohStock -= demandQty;
                    demandQtyMap.put(currentDate, 0);
                } else {
                    demandQtyMap.put(currentDate, demandQty - bohStock);
                    break;
                }
            }

            List<MasterPlanTaskVO> masterPlanTasks = productTaskGroup.getOrDefault(productCode, new ArrayList<>()).stream().filter(x -> StringUtils.isNotBlank(x.getStandardStepName()) && PACKAGING_OPERATION.equals(x.getStandardStepName())).sorted(Comparator.comparing(MasterPlanTaskVO::getStartTime)).collect(Collectors.toList());

            int totalPlanQuantity = masterPlanTasks.stream().filter(x -> Objects.nonNull(x.getOperationVO()) && Objects.nonNull(x.getOperationVO().getQuantity())).map(x -> x.getOperationVO().getQuantity()).reduce(BigDecimal.ZERO, BigDecimal::add).intValue();
            for (MasterPlanTaskVO masterPlanTask : masterPlanTasks) {
                OperationVO operationVO = masterPlanTask.getOperationVO();
                NewProductStockPointVO newProductStockPointVO = productMap.get(operationVO.getProductId());
                String safetyKey = Optional.ofNullable(newProductStockPointVO).map(NewProductStockPointVO::getStockPointCode).map(stockPointCode -> String.join("&", stockPointCode, newProductStockPointVO.getProductCode())).orElse("default_value");
                // 安全库存天数
                BigDecimal minStockDay = Optional.ofNullable(safetyStockLevelMap.get(safetyKey)).filter(list -> !list.isEmpty()).map(list -> list.get(0)).map(SafetyStockLevelVO::getMinStockDay).orElse(BigDecimal.ONE);

                // 当前工序的制造订单的完整制造信息，获取最后一道工序的完成时间
                MasterPlanTaskVO lastTask = workOrderTaskMap.get(operationVO.getOrderId()).stream().sorted(Comparator.comparing(OperationTaskVO::getEndTime)).collect(Collectors.toList()).get(0);
                // 最后一道工序的完成时间+最小安全库存天数 = 要去覆盖哪一天的发货计划
                // 可提供覆盖的日期
                Date lastEndTime = DateUtils.truncateTimeOfDate(DateUtils.moveCalendar(lastTask.getEndTime(), Calendar.DAY_OF_YEAR, minStockDay.intValue()));
                // 排产数量，可提供覆盖的量
                int planQuantity = operationVO.getQuantity().intValue();
                for (Date intervalDate : intervalDates) {
                    String currentDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
                    if (!deliveryPlanTimeGroup.containsKey(currentDate)) {
                        continue;
                    }
                    List<DeliveryPlanVO2> deliveryPlanSubList = deliveryPlanTimeGroup.get(currentDate);
                    // 需求时间
                    Date demandTime = deliveryPlanSubList.get(0).getDemandTime();
                    String demandTimeStr = DateUtils.dateToString(demandTime, DateUtils.COMMON_DATE_STR3);
                    // 未覆盖数量
                    Integer originalQty = demandQtyMap.get(demandTimeStr);
                    // 判断是否能提供覆盖日期
                    if (demandTime.getTime() >= lastEndTime.getTime()) {
                        // 如果发货计划数量 < 排产数量，直接覆盖
                        if (originalQty <= planQuantity) {
                            // 修改可供的排产数量
                            planQuantity = planQuantity - originalQty;
                            originalQty = 0;
                        } else if (planQuantity > 0) {
                            // 修改发货计划的数量，剩余数量靠其余排产任务进行满足
                            originalQty = originalQty - planQuantity;
                            planQuantity = 0;
                        }
                    }
                    // 重新赋值发货计划覆盖数量
                    demandQtyMap.put(demandTimeStr, originalQty);
                }
            }

            // 后处理映射数量
            List<DeliveryPlanGeneralViewDetailVO> viewDetails = new ArrayList<>();
            for (Date intervalDate : intervalDates) {
                String currentDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
                if (!deliveryPlanTimeGroup.containsKey(currentDate)) {
                    DeliveryPlanGeneralViewDetailVO detailVO = new DeliveryPlanGeneralViewDetailVO();
                    detailVO.setPlanFlag(Boolean.TRUE);
                    detailVO.setDemandQuantity(0);
                    detailVO.setDemandTime(intervalDate);
                    detailVO.setDemandTimeStr(currentDate);
                    detailVO.setSupplyQuantity(0);
                    viewDetails.add(detailVO);
                    continue;
                }
                List<DeliveryPlanVO2> valueList = deliveryPlanTimeGroup.get(currentDate);
                Integer demandQuantity = valueList.stream().map(DeliveryPlanVO2::getDemandQuantity).reduce(Integer::sum).orElse(0);

                String dataStr = DateUtils.dateToString(valueList.get(0).getDemandTime(), DateUtils.COMMON_DATE_STR3);
                Integer unfulfilledQty = demandQtyMap.get(dataStr);
                DeliveryPlanGeneralViewDetailVO detailVO = new DeliveryPlanGeneralViewDetailVO();
                detailVO.setDemandQuantity(demandQuantity);
                detailVO.setDemandTime(valueList.get(0).getDemandTime());
                detailVO.setDemandTimeStr(dataStr);
                detailVO.setSupplyQuantity(unfulfilledQty);
                // 未被覆盖
                if (Objects.equals(unfulfilledQty, demandQuantity)) {
                    detailVO.setPlanFlag(Boolean.FALSE);
                }
                // 完全覆盖
                if (demandQuantity > unfulfilledQty && unfulfilledQty == 0) {
                    detailVO.setPlanFlag(Boolean.TRUE);
                }
                // 部分覆盖
                if (demandQuantity > unfulfilledQty && unfulfilledQty > 0) {
                    detailVO.setPlanFlag(Boolean.FALSE);
                }
                if (demandQuantity == 0) {
                    detailVO.setPlanFlag(Boolean.TRUE);
                }
                viewDetails.add(detailVO);
            }

            if (masterPlanReq.getDeliverStartTime() != null && masterPlanReq.getDeliverEndTime() != null) {
                Date endDate = DateUtils.moveCalendar(DateUtils.truncateTimeOfDate(masterPlanReq.getDeliverEndTime()), Calendar.DAY_OF_YEAR, 1);
                List<Date> filterDayList = intervalDates.stream().filter(p -> p.getTime() >= masterPlanReq.getDeliverStartTime().getTime() && p.getTime() < endDate.getTime()).collect(Collectors.toList());
                List<DeliveryPlanGeneralViewDetailVO> filterDetailData = viewDetails.stream().filter(p -> p.getDemandTime().getTime() >= masterPlanReq.getDeliverStartTime().getTime() && p.getDemandTime().getTime() <= masterPlanReq.getDeliverEndTime().getTime()).sorted(Comparator.comparing(DeliveryPlanGeneralViewDetailVO::getDemandTime)).collect(Collectors.toList());
                viewVO.setDetailList(filterDetailData);
                viewVO.setDateList(filterDayList);
            } else {
                viewDetails.sort(Comparator.comparing(DeliveryPlanGeneralViewDetailVO::getDemandTime));
                viewVO.setDetailList(viewDetails);
                viewVO.setDateList(intervalDates);
            }
            viewVO.setVehicleModelCode(product2VehicleMap.get(productCode));
            viewVO.setPlanQuantity(totalPlanQuantity);
            List<DeliveryPlanGeneralViewDetailVO> errorDetailList = viewVO.getDetailList().stream().filter(e -> e.getPlanFlag() != null && !e.getPlanFlag()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(errorDetailList)) {
                viewVO.setErrorColour(ERROR_COLOUR);
            }
            result.add(viewVO);
        }
        return result;
    }

    @Override
    public List<DeliveryPlanOverviewVO> selectDeliveryPlanOverviewByPage(Pagination pagination, String sortParam, String queryCriteriaParam, MasterPlanReq masterPlanReq) {
        String scenario = SystemHolder.getScenario();
        PlanningHorizonVO planningHorizon = newMdsFeign.selectPlanningHorizon(scenario);
        Date historyRetrospectStartTime = planningHorizon.getHistoryRetrospectStartTime();
        masterPlanReq.setHistoryRetrospectStartTime(historyRetrospectStartTime);

        Map<String, Object> params = new HashMap<>();
        Date planStartTime = DateUtils.stringToDate(DateUtils.dateToString(masterPlanReq.getDeliverStartTime()) + " " + DateUtils.dateToString(planningHorizon.getPlanStartTime(), "HH:mm:ss"), DateUtils.COMMON_DATE_STR1);
        Date planEndTime = DateUtils.stringToDate(DateUtils.dateToString(masterPlanReq.getDeliverEndTime()) + " " + DateUtils.dateToString(planningHorizon.getPlanEndTime(), "HH:mm:ss"), DateUtils.COMMON_DATE_STR1);
        params.put(PARAM_PRODUCT_CODES, masterPlanReq.getProductCodes());
        params.put("startDateTime", planStartTime);
        params.put("endDateTime", planEndTime);
        params.put("userId", SystemHolder.getUserId());
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        List<DeliveryPlanOverviewVO> deliveryPlanOverviews = deliveryPlanOverviewDao.selectByCondition(params);
        List<DeliveryPlanOverviewDetailVO> detailList = deliveryPlanOverviewDao.selectDetailList(params);
        assembleDeliveryPlanOverviews(deliveryPlanOverviews, detailList, masterPlanReq, planningHorizon, scenario);
        return deliveryPlanOverviews;
    }

    /**
     * 组装发货计划总览数据
     *
     * @param deliveryPlanOverviews 发货计划总览数据
     * @param detailList            发货计划总览数据
     * @param masterPlanReq         主生产计划查询对象
     * @param planningHorizon       计划期间
     * @param scenario              场景名
     */
    public void assembleDeliveryPlanOverviews(List<DeliveryPlanOverviewVO> deliveryPlanOverviews, List<DeliveryPlanOverviewDetailVO> detailList, MasterPlanReq masterPlanReq, PlanningHorizonVO planningHorizon, String scenario) {
        if (CollectionUtils.isEmpty(deliveryPlanOverviews)) {
            return;
        }
        List<String> deliveryProductCodes = deliveryPlanOverviews.stream().map(DeliveryPlanOverviewVO::getProductCode).collect(Collectors.toList());

        Map<String, Object> params = new HashMap<>();
        params.put("productType", "SA");
        params.put("productCodeList", deliveryProductCodes);
        List<BomRoutingStepInputVO> bomRoutingStepInputVOS = newMdsFeign.selectBomRoutingStepInputByParams(scenario, params);
        Map<String, List<BomRoutingStepInputVO>> semiBomMap = StreamUtils.mapListByColumn(bomRoutingStepInputVOS, BomRoutingStepInputVO::getSourceProductCode);
        List<String> inputProductIds = bomRoutingStepInputVOS.stream().map(BomRoutingStepInputBasicVO::getInputProductId).distinct().collect(Collectors.toList());

        Map<String, List<DeliveryPlanOverviewDetailVO>> detailGroup = detailList.stream().collect(Collectors.groupingBy(DeliveryPlanOverviewDetailVO::getProductCode));

        // 查询工序任务
        List<MasterPlanTaskVO> operationTasks = operationTaskExtDao.selectByMasterReq(masterPlanReq);
        List<Date> intervalDates = DateUtils.getIntervalDates(planningHorizon.getPlanStartTime(), planningHorizon.getPlanEndTime());
        // 查询库存点数据，用于过滤非本厂库存
        List<NewStockPointVO> newStockPoints = newMdsFeign.selectStockPointByParams(scenario, ImmutableMap.of(PARAM_STOCK_POINT_TYPE, StockPointTypeEnum.BC.getCode()));
        // 获取本厂销售组织类型的仓库(成品库存点)
        List<String> saleOrganizations = newStockPoints.stream().filter(e -> StringUtils.isNotEmpty(e.getOrganizeType()) && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType())).map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        // 获取本厂生产组织类型的仓库(半成品库存点)
        List<String> productOrganizations = newStockPoints.stream().filter(e -> StringUtils.isNotEmpty(e.getOrganizeType()) && StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode().equals(e.getOrganizeType())).map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

        List<String> bcStockPointList = newStockPoints.stream().map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

        // 分组数据
        Map<String, List<MasterPlanTaskVO>> productTaskGroup = operationTasks.stream().filter(x -> StringUtils.isNotBlank(x.getProductStockPointCode())).collect(Collectors.groupingBy(MasterPlanTaskVO::getProductStockPointCode));
        List<String> operationIds = operationTasks.stream().map(OperationTaskVO::getOperationId).distinct().collect(Collectors.toList());
        List<OperationPO> operations = CollectionUtils.isEmpty(operationIds) ? new ArrayList<>() : operationExtDao.selectByIds(operationIds);
        Map<String, OperationPO> operationMap = operations.stream().collect(Collectors.toMap(OperationPO::getId, Function.identity(), (v1, v2) -> v1));
        // 制造订单
        List<WorkOrderPO> workOrders = workOrderDao.selectByParams(new HashMap<>());

        List<String> productIds = operations.stream().map(OperationPO::getProductId).distinct().collect(Collectors.toList());
        productIds.addAll(workOrders.stream().map(WorkOrderPO::getProductId).collect(Collectors.toList()));
        productIds.addAll(inputProductIds);
        productIds = productIds.stream().distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> newProductStockPoints = newMdsFeign.selectProductStockPointByIds(scenario, productIds);
        List<NewProductStockPointVO> deliveryProductStockPoints = newMdsFeign.selectByProductCode(scenario, deliveryProductCodes);
        newProductStockPoints.addAll(deliveryProductStockPoints);

        // 查询本厂编码对应数据，获取车型编码
        List<String> productCodes = newProductStockPoints.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
        Map<String, NewProductStockPointVO> productMap = newProductStockPoints.stream().collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity(), (v1, v2) -> v1));
        Map<String, NewProductStockPointVO> productionProductCodeMap = newProductStockPoints.stream().filter(x -> productOrganizations.contains(x.getStockPointCode())).collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (v1, v2) -> v1));
        Map<String, String> product2VehicleMap = newProductStockPoints.stream().filter(t -> StringUtils.isNotEmpty(t.getVehicleModelCode())).collect(Collectors.toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getVehicleModelCode, (v1, v2) -> v1));

        // 制造订单对应的子工序的完整制造时间10-50工序，20镀膜，30成型，10，40，50推出来
        // 组装半品-成品映射
        operationSetter(operationTasks, operationMap);
        Map<String, List<MasterPlanTaskVO>> workOrderTaskMap = operationTasks.stream().filter(x -> Objects.nonNull(x.getOperationVO()) && StringUtils.isNotBlank(x.getOperationVO().getOrderId())).collect(Collectors.groupingBy(p -> p.getOperationVO().getOrderId()));
        // 材料安全库存
        Map<String, List<SafetyStockLevelVO>> safetyStockLevelMap = dfpFeign.selectSafetyStockLevelByProductCodeList(scenario, deliveryProductCodes).stream().collect(Collectors.groupingBy(p -> String.join("&", p.getStockPointId(), p.getProductCode())));
        // 查询库存数据
        List<InventoryBatchDetailVO> inventoryBatchDetails = dfpFeign.selectInventoryDataByProductCodes(scenario, productCodes, StockPointTypeEnum.BC.getCode()).stream().filter(p -> bcStockPointList.contains(p.getStockPointCode())).collect(Collectors.toList());

        List<String> spaceList = inventoryBatchDetails.stream().map(InventoryBatchDetailVO::getFreightSpace).distinct().collect(Collectors.toList());
        // 查询可用本厂有效货位库存
        Map<String, SubInventoryCargoLocationVO> cargoLocationMap = CollectionUtils.isEmpty(spaceList) ? new HashMap<>() : subInventoryCargoLocationDao.selectByBatchCodeAndStockType(spaceList, StockPointTypeEnum.BC.getCode()).stream().collect(Collectors.toMap(SubInventoryCargoLocationVO::getFreightSpaceCode, Function.identity(), (v1, v2) -> v1));
        // 成品库存
        Map<String, List<InventoryBatchDetailVO>> finishInventoryMap = inventoryBatchDetails.stream().filter(t -> StringUtils.isEmpty(t.getOperationCode()) && saleOrganizations.contains(t.getStockPointCode()) && SUB_INVENTORY_CPSJ.equals(t.getSubinventory())).collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));
        // 工序在制量
        Map<String, List<InventoryBatchDetailVO>> operationInventoryMap = inventoryBatchDetails.stream().filter(t -> productOrganizations.contains(t.getStockPointCode())).collect(Collectors.groupingBy(p -> String.join("-", p.getStockPointCode(), p.getProductCode(), p.getOperationCode())));

        // 半品库存
        Map<String, List<InventoryBatchDetailVO>> semiFinishInventoryMap = inventoryBatchDetails.stream().filter(t -> StringUtils.isEmpty(t.getOperationCode()) && productOrganizations.contains(t.getStockPointCode())).collect(Collectors.groupingBy(p -> String.join("-", p.getStockPointCode(), p.getProductCode())));

        // 发货记录数据
        Map<String, BigDecimal> warehouseReleaseRecordMap = operationTaskExtDao.selectWarehouseReleaseRecord(deliveryProductCodes).stream().collect(Collectors.toMap(WarehouseReleaseRecordVO::getItemCode, WarehouseReleaseRecordVO::getSumQty, (v1, v2) -> v1));
        // 产品工艺基础数据
        Map<String, String> stepMap = newMdsFeign.selectStandardStepAll(scenario).stream().collect(Collectors.toMap(p -> p.getStockPointCode() + p.getStandardStepName(), StandardStepBasicVO::getStandardStepCode, (v1, v2) -> v2));

        String specialStockPoint = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT"));
        String specialStockPoint2 = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT_S2").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT_S2"));

        for (DeliveryPlanOverviewVO viewVO : deliveryPlanOverviews) {
            String productCode = viewVO.getProductCode();
            viewVO.setSumQty(BigDecimal.ZERO);
            // 设置发货量
            if (warehouseReleaseRecordMap.containsKey(productCode)) {
                viewVO.setSumQty(warehouseReleaseRecordMap.get(productCode));
            }
            // 初始化成品 & 半品库存
            viewVO.setFinishInventory("0");
            viewVO.setSemiFinishedGoodsInventory("0");

            // 发货计划需求数量
            Map<String, Integer> qtyMap = detailGroup.getOrDefault(productCode, new ArrayList<>()).stream().collect(Collectors.groupingBy(DeliveryPlanOverviewDetailVO::getDemandDate, Collectors.summingInt(x -> x.getDemandQuantity().intValue())));
            Map<String, Integer> originalQtyMap = new HashMap<>(qtyMap);
            int bohInventory = viewVO.getSumQty().intValue();

            NewProductStockPointVO productItem = productionProductCodeMap.get(productCode);

            String semiInventory = "";
            if (semiBomMap.containsKey(productCode)) {
                List<BomRoutingStepInputVO> semiBomList = semiBomMap.get(productCode);
                List<Double> semiValue = new ArrayList<>();
                for (BomRoutingStepInputVO bomRoutingStepInputVO : semiBomList) {
                    String semiProductCode = bomRoutingStepInputVO.getProductCode();
                    // 半品物料
                    NewProductStockPointVO semiProductCodeItem = productionProductCodeMap.get(semiProductCode);
                    if (Objects.isNull(semiProductCodeItem)) {
                        continue;
                    }
                    String semiProductCodeItemProductCode = semiProductCodeItem.getProductCode();
                    String stockPointCode = semiProductCodeItem.getStockPointCode();
                    String semiKey = stockPointCode + "-" + semiProductCodeItemProductCode;
                    // 维护产品编码对应的半品库存
                    List<InventoryBatchDetailVO> semiFinishList = getFinishInventory(semiFinishInventoryMap
                            .get(semiKey), cargoLocationMap);
                    BigDecimal semiFinishInventory = semiFinishList.stream().map(t ->
                            new BigDecimal(t.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
                    double semiBomInventory = getSemiBomInventory(semiProductCodeItem, stepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, productCode);
                    semiBomInventory = semiBomInventory + semiFinishInventory.doubleValue();
                    semiValue.add(semiBomInventory);
                }
                // 发货计划总览的成型后库存，应该是大小片各自的半品库存，然后取小
                if (CollectionUtils.isNotEmpty(semiValue)) {
                    semiValue.sort(Comparator.comparing(Double::doubleValue));
                    semiInventory = new BigDecimal(String.valueOf(semiValue.get(0))).stripTrailingZeros().toPlainString();
                }
            } else {
                double semiBomInventory = getSemiBomInventory(productItem, stepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, productCode);
                semiInventory = new BigDecimal(String.valueOf(semiBomInventory)).stripTrailingZeros().toPlainString();
            }
            viewVO.setSemiFinishedGoodsInventory(semiInventory);
            BigDecimal finishedInventory = getFinishInventory(finishInventoryMap.get(productCode), cargoLocationMap).stream().map(t -> new BigDecimal(t.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 设置成品库存
            viewVO.setFinishInventory(finishedInventory.stripTrailingZeros().toPlainString());
            // 先使用仓库收发货 + 成品库存 + 半品库存映射需求数量
            bohInventory = bohInventory + finishedInventory.intValue() + new BigDecimal(semiInventory).intValue();

            // 使用期初冲减需求
            Map<String, String> colourMap = new HashMap<>();
            for (Date intervalDate : intervalDates) {
                String currentDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
                qtyMap.putIfAbsent(currentDate, 0);
                if (qtyMap.get(currentDate) == 0) {
                    continue;
                }
                Integer demandQty = qtyMap.get(currentDate);
                if (demandQty <= bohInventory) {
                    bohInventory -= demandQty;
                    qtyMap.put(currentDate, 0);
                    colourMap.put(currentDate, GREEN_COLOUR);
                } else {
                    qtyMap.put(currentDate, demandQty - bohInventory);
                    break;
                }
            }

            List<MasterPlanTaskVO> masterPlanTasks = productTaskGroup.getOrDefault(productCode, new ArrayList<>()).stream().filter(x -> StringUtils.isNotBlank(x.getStandardStepName()) && PACKAGING_OPERATION.equals(x.getStandardStepName())).sorted(Comparator.comparing(MasterPlanTaskVO::getStartTime)).collect(Collectors.toList());

            int totalPlanQuantity = masterPlanTasks.stream().filter(x -> Objects.nonNull(x.getOperationVO()) && Objects.nonNull(x.getOperationVO().getQuantity())).map(x -> x.getOperationVO().getQuantity()).reduce(BigDecimal.ZERO, BigDecimal::add).intValue();
            for (MasterPlanTaskVO masterPlanTask : masterPlanTasks) {
                OperationVO operationVO = masterPlanTask.getOperationVO();
                NewProductStockPointVO newProductStockPointVO = productMap.get(operationVO.getProductId());
                String safetyKey = Optional.ofNullable(newProductStockPointVO).map(NewProductStockPointVO::getStockPointCode).map(stockPointCode -> String.join("&", stockPointCode, newProductStockPointVO.getProductCode())).orElse("default_value");
                // 安全库存天数
                BigDecimal minStockDay = Optional.ofNullable(safetyStockLevelMap.get(safetyKey)).filter(list -> !list.isEmpty()).map(list -> list.get(0)).map(SafetyStockLevelVO::getMinStockDay).orElse(BigDecimal.ONE);

                // 当前工序的制造订单的完整制造信息，获取最后一道工序的完成时间
                MasterPlanTaskVO lastTask = workOrderTaskMap.get(operationVO.getOrderId()).stream().sorted(Comparator.comparing(OperationTaskVO::getEndTime)).collect(Collectors.toList()).get(0);
                // 最后一道工序的完成时间+最小安全库存天数 = 要去覆盖哪一天的发货计划
                // 可提供覆盖的日期
                Date lastEndTime = DateUtils.truncateTimeOfDate(DateUtils.moveCalendar(lastTask.getEndTime(), Calendar.DAY_OF_YEAR, minStockDay.intValue()));
                // 排产数量，可提供覆盖的量
                int planQty = operationVO.getQuantity().intValue();
                for (Date intervalDate : intervalDates) {
                    String currentDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
                    if (!qtyMap.containsKey(currentDate)) {
                        continue;
                    }
                    // 未覆盖数量
                    Integer unfulfilledQty = qtyMap.get(currentDate);
                    // 判断是否能提供覆盖日期
                    if (intervalDate.getTime() >= lastEndTime.getTime()) {
                        // 如果发货计划数量 < 排产数量，直接覆盖
                        if (unfulfilledQty <= planQty) {
                            // 修改可供的排产数量
                            planQty = planQty - unfulfilledQty;
                            colourMap.putIfAbsent(currentDate, BLUE_COLOUR);
                            unfulfilledQty = 0;
                        } else if (planQty > 0) {
                            // 修改发货计划的数量，剩余数量靠其余排产任务进行满足
                            unfulfilledQty = unfulfilledQty - planQty;
                            planQty = 0;
                        }
                    }
                    // 重新赋值发货计划覆盖数量
                    qtyMap.put(currentDate, unfulfilledQty);
                }
            }

            // 后处理映射数量
            List<DeliveryPlanGeneralViewDetailVO> viewDetails = new ArrayList<>();
            for (Date intervalDate : intervalDates) {
                String currentDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
                if (!qtyMap.containsKey(currentDate)) {
                    DeliveryPlanGeneralViewDetailVO detailVO = new DeliveryPlanGeneralViewDetailVO();
                    detailVO.setPlanFlag(Boolean.TRUE);
                    detailVO.setDemandQuantity(0);
                    detailVO.setDemandTime(intervalDate);
                    detailVO.setDemandTimeStr(currentDate);
                    detailVO.setSupplyQuantity(0);
                    detailVO.setBlockColour(EMPTY_COLOUR);
                    viewDetails.add(detailVO);
                    continue;
                }
                Integer demandQuantity = originalQtyMap.getOrDefault(currentDate, 0);
                Integer unfulfilledQty = qtyMap.get(currentDate);
                DeliveryPlanGeneralViewDetailVO detailVO = new DeliveryPlanGeneralViewDetailVO();
                detailVO.setDemandQuantity(demandQuantity);
                detailVO.setDemandTime(DateUtils.stringToDate(currentDate));
                detailVO.setDemandTimeStr(currentDate);
                detailVO.setSupplyQuantity(demandQuantity - unfulfilledQty);
                // 未被覆盖
                if (Objects.equals(unfulfilledQty, demandQuantity)) {
                    detailVO.setPlanFlag(Boolean.FALSE);
                    detailVO.setBlockColour(RED_COLOUR);
                }
                // 完全覆盖
                if (demandQuantity > unfulfilledQty && unfulfilledQty == 0) {
                    detailVO.setPlanFlag(Boolean.TRUE);
                    detailVO.setBlockColour(colourMap.get(currentDate));
                }
                // 部分覆盖
                if (demandQuantity > unfulfilledQty && unfulfilledQty > 0) {
                    detailVO.setPlanFlag(Boolean.FALSE);
                    detailVO.setBlockColour(RED_COLOUR);
                }
                if (demandQuantity == 0) {
                    detailVO.setPlanFlag(Boolean.TRUE);
                    detailVO.setBlockColour(EMPTY_COLOUR);
                }
                viewDetails.add(detailVO);
            }

            viewDetails.sort(Comparator.comparing(DeliveryPlanGeneralViewDetailVO::getDemandTime));
            viewVO.setDetailList(viewDetails);
            viewVO.setDateList(intervalDates);
            viewVO.setVehicleModelCode(product2VehicleMap.get(productCode));
            viewVO.setPlanQuantity(totalPlanQuantity);
            List<DeliveryPlanGeneralViewDetailVO> errorDetailList = viewVO.getDetailList().stream().filter(e -> e.getPlanFlag() != null && !e.getPlanFlag()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(errorDetailList)) {
                viewVO.setErrorColour(ERROR_COLOUR);
            }
        }
        System.out.println(deliveryPlanOverviews.size());
    }

    public static double getSemiBomInventory(NewProductStockPointVO productItem, Map<String, String> stepMap, Map<String, List<InventoryBatchDetailVO>> operationInventoryMap, Map<String, SubInventoryCargoLocationVO> cargoLocationMap, String specialStockPoint, String specialStockPoint2, String parentProductCode) {
        String cx = getInventory(FORMING_OPERATION, productItem, stepMap, operationInventoryMap,
                cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
        String hp = getInventory(MERGING_OPERATION, productItem, stepMap, operationInventoryMap,
                cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
        String bz = getInventory(PACKAGING_OPERATION, productItem, stepMap, operationInventoryMap,
                cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
        OptionalDouble sum = Arrays.stream(new String[]{bz, hp, cx})
                .mapToDouble(Double::parseDouble).reduce(Double::sum);
        return sum.getAsDouble();
    }


    @Override
    public List<LabelValue<String>> resourceDropDown(String productId, String standardStepId) {
        if (StringUtils.isEmpty(productId) || StringUtils.isEmpty(standardStepId)) {
            throw new BusinessException("物品Id或工序Id不能为空");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("productId", productId);
        params.put("standardStepId", standardStepId);
        List<ProductCandidateResourceVO> productCandidateResourceVOS = newMdsFeign.selectProductCandidateResourceByParams(SystemHolder.getScenario(), params);
        if (CollectionUtils.isEmpty(productCandidateResourceVOS)) {
            throw new BusinessException("物品候选资源不存在：" + productId + " , " + standardStepId);
        }
        List<String> physicalResourceId = productCandidateResourceVOS.stream().map(ProductCandidateResourceVO::getPhysicalResourceId).collect(Collectors.toList());
        List<PhysicalResourceVO> physicalResourceVOS = newMdsFeign.selectByPhysicalIds(SystemHolder.getScenario(), physicalResourceId);
        if (CollectionUtils.isNotEmpty(physicalResourceVOS)) {
            return physicalResourceVOS.stream().map(t -> new LabelValue<>(t.getPhysicalResourceName(), t.getPhysicalResourceCode())).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<LabelValue<String>> queryListResourceDropDown() {
        List<PhysicalResourceVO> physicalResourceVOS = operationTaskExtDao.selectPhysicalResource(null, null, null);
        return physicalResourceVOS.stream().map(item -> new LabelValue<>(item.getPhysicalResourceCode() + "-" + item.getPhysicalResourceName(), item.getPhysicalResourceCode())).collect(Collectors.toList());
    }

    @Override
    public List<MasterPlanWorkOrderBodyVO> getMasterPlan(MasterPlanReq masterPlanReq) {
        // 获取生产计划任务
        List<MasterPlanTaskVO> operationTaskVOS = operationTaskExtDao.selectByMasterReq(masterPlanReq);

        // 获取所有资源ID，过滤并获取物理资源
        List<String> resourceIds = operationTaskVOS.stream().map(MasterPlanTaskVO::getPhysicalResourceId).distinct().collect(Collectors.toList());

        List<PhysicalResourceVO> physicalResourceVOS = newMdsFeign.selectByPhysicalIds(SystemHolder.getScenario(), resourceIds);
        // 过滤掉工具资源并构建资源ID到资源对象的映射
        Map<String, PhysicalResourceVO> physicalResourceVOMap = physicalResourceVOS.stream().filter(resource -> !ResourceCategoryEnum.TOOL.getCode().equals(resource.getResourceCategory())).collect(Collectors.toMap(PhysicalResourceVO::getId, Function.identity(), (v1, v2) -> v1));

        // 工具资源
        List<String> toolResourceIds = physicalResourceVOS.stream().filter(t -> ResourceCategoryEnum.TOOL.getCode().equals(t.getResourceCategory())).map(PhysicalResourceVO::getId).collect(Collectors.toList());
        operationTaskVOS = operationTaskVOS.stream().filter(t -> !toolResourceIds.contains(t.getPhysicalResourceId())).collect(Collectors.toList());

        // 构建结果列表
        return operationTaskVOS.stream().map(task -> {
            MasterPlanWorkOrderBodyVO bodyVO = new MasterPlanWorkOrderBodyVO();
            bodyVO.setProductCode(task.getProductStockPointCode());
            // 获取对应的物理资源信息并设置
            PhysicalResourceVO resource = physicalResourceVOMap.get(task.getPhysicalResourceId());
            if (resource != null) {
                bodyVO.setResourceCode(resource.getPhysicalResourceCode());
                bodyVO.setResourceName(resource.getPhysicalResourceName());
            }
            return bodyVO;
        }).collect(Collectors.toList());
    }


    @Override
    public List<LabelValue<String>> selectOperationDropdown(Map<String, Object> params) {
        List<StandardStepPO> standardStepPOS = masterPlanDao.selectOperationDropdown(params);
        return standardStepPOS.stream().map(x -> new LabelValue<>(String.join("-", x.getStockPointCode(), x.getStandardStepCode(), x.getStandardStepName()), x.getStandardStepCode())).collect(Collectors.toList());
    }

    @Override
    public void doUpdateRemark(UpdateRemarkDTO updateRemarkDTO) {
        if (StringUtils.isNotEmpty(updateRemarkDTO.getOperationId())) {
            operationTaskExtDao.updateOperationRemark(updateRemarkDTO.getOperationId(), updateRemarkDTO.getRemark());
        } else {
            operationTaskExtDao.updateOperationTaskRemark(updateRemarkDTO.getOperationTaskId(), updateRemarkDTO.getRemark());
        }
    }

    @Override
    public void updateDueDate(UpdateDueDateDTO updateDueDateDTO) {
        String scenario = SystemHolder.getScenario();
        WorkOrderPO workOrderPO = new WorkOrderPO();
        workOrderPO.setDueDate(DateUtils.stringToDate(updateDueDateDTO.getDueDate(), DateUtils.COMMON_DATE_STR1));
        workOrderPO.setId(updateDueDateDTO.getWorkOrderId());
        workOrderPO.setFixed(YesOrNoEnum.YES.getCode());
        BasePOUtils.updateFiller(workOrderPO);
        workOrderDao.updateSelective(workOrderPO);
        CompletableFuture.runAsync(() -> {
            // 刷新workOrder缓存
            cacheSetService.refreshWorkOrderCache(scenario);
        });
    }


    private static Integer getBohInventory(String finishInventory, String semiFinishInventory, String totalTime,
                                           String saFinishInventory, String saSemiFinishInventory, String saTotalTime,
                                           boolean saFlag) {
        Integer bohInventory = (finishInventory == null ? 0 : new BigDecimal(finishInventory).intValue())
                + (StringUtils.isBlank(semiFinishInventory) ? 0 : new BigDecimal(semiFinishInventory).intValue())
                + (StringUtils.isBlank(totalTime) ? 0 : new BigDecimal(totalTime).intValue());
        Integer saBohInventory = (saFinishInventory == null ? 0 : new BigDecimal(saFinishInventory).intValue())
                + (StringUtils.isBlank(saSemiFinishInventory) ? 0 : new BigDecimal(saSemiFinishInventory).intValue())
                + (StringUtils.isBlank(saTotalTime) ? 0 : new BigDecimal(saTotalTime).intValue());
        return saFlag ? Math.min(bohInventory, saBohInventory) : bohInventory;
    }

}