package com.yhl.scp.mps.plan.convertor;

import com.yhl.scp.mps.plan.domain.entity.SdsOrdOperationHistoryLogDO;
import com.yhl.scp.mps.plan.dto.SdsOrdOperationHistoryLogDTO;
import com.yhl.scp.mps.plan.infrastructure.po.SdsOrdOperationHistoryLogPO;
import com.yhl.scp.mps.plan.vo.SdsOrdOperationHistoryLogVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>SdsOrdOperationHistoryLogConvertor</code>
 * <p>
 * 工序历史记录表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-14 17:38:30
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SdsOrdOperationHistoryLogConvertor {

    SdsOrdOperationHistoryLogConvertor INSTANCE = Mappers.getMapper(SdsOrdOperationHistoryLogConvertor.class);

    SdsOrdOperationHistoryLogDO dto2Do(SdsOrdOperationHistoryLogDTO obj);

    SdsOrdOperationHistoryLogDTO do2Dto(SdsOrdOperationHistoryLogDO obj);

    List<SdsOrdOperationHistoryLogDO> dto2Dos(List<SdsOrdOperationHistoryLogDTO> list);

    List<SdsOrdOperationHistoryLogDTO> do2Dtos(List<SdsOrdOperationHistoryLogDO> list);

    SdsOrdOperationHistoryLogVO do2Vo(SdsOrdOperationHistoryLogDO obj);

    SdsOrdOperationHistoryLogVO po2Vo(SdsOrdOperationHistoryLogPO obj);

    List<SdsOrdOperationHistoryLogVO> po2Vos(List<SdsOrdOperationHistoryLogPO> list);

    SdsOrdOperationHistoryLogPO do2Po(SdsOrdOperationHistoryLogDO obj);

    SdsOrdOperationHistoryLogDO po2Do(SdsOrdOperationHistoryLogPO obj);

    SdsOrdOperationHistoryLogPO dto2Po(SdsOrdOperationHistoryLogDTO obj);

    List<SdsOrdOperationHistoryLogPO> dto2Pos(List<SdsOrdOperationHistoryLogDTO> obj);

}
