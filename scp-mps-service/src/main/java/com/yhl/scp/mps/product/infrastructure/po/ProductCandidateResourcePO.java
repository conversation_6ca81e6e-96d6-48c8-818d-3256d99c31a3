package com.yhl.scp.mps.product.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>ProductCandidateResourcePO</code>
 * <p>
 * 产品资源生产关系表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 10:16:29
 */
public class ProductCandidateResourcePO extends BasePO implements Serializable {

    private static final long serialVersionUID = 653612317824816992L;

    /**
     * 库存点代码
     */
    private String stockPointCode;
    /**
     * 库存点名称
     */
    private String stockPointName;
    /**
     * 本厂编码
     */
    private String productCode;
    /**
     * 零件名称
     */
    private String partName;
    /**
     * 工序代码
     */
    private String operationCode;
    /**
     * 工序名称
     */
    private String operationName;
    /**
     * 资源代码
     */
    private String resourceCode;
    /**
     * 资源名称
     */
    private String resourceName;
    /**
     * 资源类型
     */
    private String resourceType;
    /**
     * 生产线组
     */
    private String lineGroup;
    /**
     * 节拍（秒/片）
     */
    private Double beat;

    public String getStockPointCode() {
        return stockPointCode;
    }

    public void setStockPointCode(String stockPointCode) {
        this.stockPointCode = stockPointCode;
    }

    public String getStockPointName() {
        return stockPointName;
    }

    public void setStockPointName(String stockPointName) {
        this.stockPointName = stockPointName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getPartName() {
        return partName;
    }

    public void setPartName(String partName) {
        this.partName = partName;
    }

    public String getOperationCode() {
        return operationCode;
    }

    public void setOperationCode(String operationCode) {
        this.operationCode = operationCode;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public String getResourceCode() {
        return resourceCode;
    }

    public void setResourceCode(String resourceCode) {
        this.resourceCode = resourceCode;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getLineGroup() {
        return lineGroup;
    }

    public void setLineGroup(String lineGroup) {
        this.lineGroup = lineGroup;
    }

    public Double getBeat() {
        return beat;
    }

    public void setBeat(Double beat) {
        this.beat = beat;
    }

}
