<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.operationPublished.infrastructure.dao.OperationExtendPublishedDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationExtendPublishedPO">
        <!--@Table sds_ord_operation_extend_published-->
        <id column="new_id" jdbcType="VARCHAR" property="newId"/>
        <result column="operation_id" jdbcType="VARCHAR" property="operationId"/>
        <result column="pre_operation_ids" jdbcType="VARCHAR" property="preOperationIds"/>
        <result column="resource_ids" jdbcType="VARCHAR" property="resourceIds"/>
        <result column="resource_fixed" jdbcType="VARCHAR" property="resourceFixed"/>
        <result column="time_fixed" jdbcType="VARCHAR" property="timeFixed"/>
        <result column="next_operation_ids" jdbcType="VARCHAR" property="nextOperationIds"/>
        <result column="appoint_production_time" jdbcType="TIMESTAMP" property="appointProductionTime"/>
        <result column="appoint_quantity" jdbcType="INTEGER" property="appointQuantity"/>
        <result column="appoint_main_resource_id" jdbcType="VARCHAR" property="appointMainResourceId"/>
        <result column="appoint_start_time" jdbcType="TIMESTAMP" property="appointStartTime"/>
        <result column="appoint_end_time" jdbcType="TIMESTAMP" property="appointEndTime"/>
        <result column="partition_rate" jdbcType="VARCHAR" property="partitionRate"/>
        <result column="partition_num" jdbcType="INTEGER" property="partitionNum"/>
        <result column="partition_batch" jdbcType="VARCHAR" property="partitionBatch"/>
        <result column="partition_type" jdbcType="VARCHAR" property="partitionType"/>
        <result column="max_partition_batch" jdbcType="VARCHAR" property="maxPartitionBatch"/>
        <result column="min_partition_batch" jdbcType="VARCHAR" property="minPartitionBatch"/>
        <result column="partition_batch_unit" jdbcType="VARCHAR" property="partitionBatchUnit"/>
        <result column="partition_mantissa_deal" jdbcType="VARCHAR" property="partitionMantissaDeal"/>
        <result column="planned_main_resource_id" jdbcType="VARCHAR" property="plannedMainResourceId"/>
        <result column="last_main_resource_id" jdbcType="VARCHAR" property="lastMainResourceId"/>
        <result column="planned_tool_resource_id" jdbcType="VARCHAR" property="plannedToolResourceId"/>
        <result column="last_tool_resource_id" jdbcType="VARCHAR" property="lastToolResourceId"/>
        <result column="planned_skill_id" jdbcType="VARCHAR" property="plannedSkillId"/>
        <result column="last_skill_id" jdbcType="VARCHAR" property="lastSkillId"/>
        <result column="setup_start_time" jdbcType="TIMESTAMP" property="setupStartTime"/>
        <result column="last_setup_start_time" jdbcType="TIMESTAMP" property="lastSetupStartTime"/>
        <result column="setup_end_time" jdbcType="TIMESTAMP" property="setupEndTime"/>
        <result column="last_setup_end_time" jdbcType="TIMESTAMP" property="lastSetupEndTime"/>
        <result column="setup_duration" jdbcType="INTEGER" property="setupDuration"/>
        <result column="production_start_time" jdbcType="TIMESTAMP" property="productionStartTime"/>
        <result column="last_production_start_time" jdbcType="TIMESTAMP" property="lastProductionStartTime"/>
        <result column="production_end_time" jdbcType="TIMESTAMP" property="productionEndTime"/>
        <result column="last_production_end_time" jdbcType="TIMESTAMP" property="lastProductionEndTime"/>
        <result column="lock_start_time" jdbcType="TIMESTAMP" property="lockStartTime"/>
        <result column="last_lock_start_time" jdbcType="TIMESTAMP" property="lastLockStartTime"/>
        <result column="lock_end_time" jdbcType="TIMESTAMP" property="lockEndTime"/>
        <result column="last_lock_end_time" jdbcType="TIMESTAMP" property="lastLockEndTime"/>
        <result column="lock_duration" jdbcType="INTEGER" property="lockDuration"/>
        <result column="cleanup_start_time" jdbcType="TIMESTAMP" property="cleanupStartTime"/>
        <result column="last_cleanup_start_time" jdbcType="TIMESTAMP" property="lastCleanupStartTime"/>
        <result column="cleanup_end_time" jdbcType="TIMESTAMP" property="cleanupEndTime"/>
        <result column="last_cleanup_end_time" jdbcType="TIMESTAMP" property="lastCleanupEndTime"/>
        <result column="cleanup_duration" jdbcType="INTEGER" property="cleanupDuration"/>
        <result column="feed_finish_time" jdbcType="TIMESTAMP" property="feedFinishTime"/>
        <result column="operation_type" jdbcType="VARCHAR" property="operationType"/>
        <result column="delay_reason" jdbcType="VARCHAR" property="delayReason"/>
        <result column="lock_status" jdbcType="VARCHAR" property="lockStatus"/>
        <result column="unscheduled_reason" jdbcType="VARCHAR" property="unscheduledReason"/>
        <result column="last_insertion" jdbcType="VARCHAR" property="lastInsertion"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
     	<result column="published_log_id" jdbcType="VARCHAR" property="publishedLogId"/>
        <result column="id" jdbcType="VARCHAR" property="id"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.operationPublished.vo.OperationExtendPublishedVO">
        <!-- TODO -->
    </resultMap>
    
    <resultMap id="POResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationExtendPublishedPO">
        <!-- TODO -->
    </resultMap>
    
    <sql id="Base_Column_List">
        new_id,operation_id,pre_operation_ids,resource_ids,resource_fixed,time_fixed,next_operation_ids,appoint_production_time,appoint_quantity,appoint_main_resource_id,appoint_start_time,appoint_end_time,partition_rate,partition_num,partition_batch,partition_type,max_partition_batch,min_partition_batch,partition_batch_unit,partition_mantissa_deal,planned_main_resource_id,last_main_resource_id,planned_tool_resource_id,last_tool_resource_id,planned_skill_id,last_skill_id,setup_start_time,last_setup_start_time,setup_end_time,last_setup_end_time,setup_duration,production_start_time,last_production_start_time,production_end_time,last_production_end_time,lock_start_time,last_lock_start_time,lock_end_time,last_lock_end_time,lock_duration,cleanup_start_time,last_cleanup_start_time,cleanup_end_time,last_cleanup_end_time,cleanup_duration,feed_finish_time,operation_type,delay_reason,lock_status,unscheduled_reason,last_insertion,remark,enabled,creator,create_time,modifier,modify_time,id,published_log_id
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.newId != null and params.newId != ''">
                and new_id = #{params.newId,jdbcType=VARCHAR}
            </if>
            <if test="params.operationId != null and params.operationId != ''">
                and operation_id = #{params.operationId,jdbcType=VARCHAR}
            </if>
            <if test="params.preOperationIds != null and params.preOperationIds != ''">
                and pre_operation_ids = #{params.preOperationIds,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceIds != null and params.resourceIds != ''">
                and resource_ids = #{params.resourceIds,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceFixed != null and params.resourceFixed != ''">
                and resource_fixed = #{params.resourceFixed,jdbcType=VARCHAR}
            </if>
            <if test="params.timeFixed != null and params.timeFixed != ''">
                and time_fixed = #{params.timeFixed,jdbcType=VARCHAR}
            </if>
            <if test="params.nextOperationIds != null and params.nextOperationIds != ''">
                and next_operation_ids = #{params.nextOperationIds,jdbcType=VARCHAR}
            </if>
            <if test="params.appointProductionTime != null">
                and appoint_production_time = #{params.appointProductionTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.appointQuantity != null">
                and appoint_quantity = #{params.appointQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.appointMainResourceId != null and params.appointMainResourceId != ''">
                and appoint_main_resource_id = #{params.appointMainResourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.appointStartTime != null">
                and appoint_start_time = #{params.appointStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.appointEndTime != null">
                and appoint_end_time = #{params.appointEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.partitionRate != null">
                and partition_rate = #{params.partitionRate,jdbcType=VARCHAR}
            </if>
            <if test="params.partitionNum != null">
                and partition_num = #{params.partitionNum,jdbcType=INTEGER}
            </if>
            <if test="params.partitionBatch != null">
                and partition_batch = #{params.partitionBatch,jdbcType=VARCHAR}
            </if>
            <if test="params.partitionType != null and params.partitionType != ''">
                and partition_type = #{params.partitionType,jdbcType=VARCHAR}
            </if>
            <if test="params.maxPartitionBatch != null">
                and max_partition_batch = #{params.maxPartitionBatch,jdbcType=VARCHAR}
            </if>
            <if test="params.minPartitionBatch != null">
                and min_partition_batch = #{params.minPartitionBatch,jdbcType=VARCHAR}
            </if>
            <if test="params.partitionBatchUnit != null and params.partitionBatchUnit != ''">
                and partition_batch_unit = #{params.partitionBatchUnit,jdbcType=VARCHAR}
            </if>
            <if test="params.partitionMantissaDeal != null and params.partitionMantissaDeal != ''">
                and partition_mantissa_deal = #{params.partitionMantissaDeal,jdbcType=VARCHAR}
            </if>
            <if test="params.plannedMainResourceId != null and params.plannedMainResourceId != ''">
                and planned_main_resource_id = #{params.plannedMainResourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.lastMainResourceId != null and params.lastMainResourceId != ''">
                and last_main_resource_id = #{params.lastMainResourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.plannedToolResourceId != null and params.plannedToolResourceId != ''">
                and planned_tool_resource_id = #{params.plannedToolResourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.lastToolResourceId != null and params.lastToolResourceId != ''">
                and last_tool_resource_id = #{params.lastToolResourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.plannedSkillId != null and params.plannedSkillId != ''">
                and planned_skill_id = #{params.plannedSkillId,jdbcType=VARCHAR}
            </if>
            <if test="params.lastSkillId != null and params.lastSkillId != ''">
                and last_skill_id = #{params.lastSkillId,jdbcType=VARCHAR}
            </if>
            <if test="params.setupStartTime != null">
                and setup_start_time = #{params.setupStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lastSetupStartTime != null">
                and last_setup_start_time = #{params.lastSetupStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.setupEndTime != null">
                and setup_end_time = #{params.setupEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lastSetupEndTime != null">
                and last_setup_end_time = #{params.lastSetupEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.setupDuration != null">
                and setup_duration = #{params.setupDuration,jdbcType=INTEGER}
            </if>
            <if test="params.productionStartTime != null">
                and production_start_time = #{params.productionStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lastProductionStartTime != null">
                and last_production_start_time = #{params.lastProductionStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.productionEndTime != null">
                and production_end_time = #{params.productionEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lastProductionEndTime != null">
                and last_production_end_time = #{params.lastProductionEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lockStartTime != null">
                and lock_start_time = #{params.lockStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lastLockStartTime != null">
                and last_lock_start_time = #{params.lastLockStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lockEndTime != null">
                and lock_end_time = #{params.lockEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lastLockEndTime != null">
                and last_lock_end_time = #{params.lastLockEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lockDuration != null">
                and lock_duration = #{params.lockDuration,jdbcType=INTEGER}
            </if>
            <if test="params.cleanupStartTime != null">
                and cleanup_start_time = #{params.cleanupStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lastCleanupStartTime != null">
                and last_cleanup_start_time = #{params.lastCleanupStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.cleanupEndTime != null">
                and cleanup_end_time = #{params.cleanupEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lastCleanupEndTime != null">
                and last_cleanup_end_time = #{params.lastCleanupEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.cleanupDuration != null">
                and cleanup_duration = #{params.cleanupDuration,jdbcType=INTEGER}
            </if>
            <if test="params.feedFinishTime != null">
                and feed_finish_time = #{params.feedFinishTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.operationType != null and params.operationType != ''">
                and operation_type = #{params.operationType,jdbcType=VARCHAR}
            </if>
            <if test="params.delayReason != null and params.delayReason != ''">
                and delay_reason = #{params.delayReason,jdbcType=VARCHAR}
            </if>
            <if test="params.lockStatus != null and params.lockStatus != ''">
                and lock_status = #{params.lockStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.unscheduledReason != null and params.unscheduledReason != ''">
                and unscheduled_reason = #{params.unscheduledReason,jdbcType=VARCHAR}
            </if>
            <if test="params.lastInsertion != null and params.lastInsertion != ''">
                and last_insertion = #{params.lastInsertion,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.publishedLogId != null and params.publishedLogId != ''">
                and published_log_id = #{params.publishedLogId,jdbcType=VARCHAR}
            </if>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sds_ord_operation_extend_published
        where new_id = #{newId,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sds_ord_operation_extend_published
        where new_id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sds_peg_demand_published
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sds_ord_operation_extend_published
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_sds_ord_operation_extend_published
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationExtendPublishedPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into sds_ord_operation_extend_published(
        new_id,
        operation_id,
        pre_operation_ids,
        resource_ids,
        resource_fixed,
        time_fixed,
        next_operation_ids,
        appoint_production_time,
        appoint_quantity,
        appoint_main_resource_id,
        appoint_start_time,
        appoint_end_time,
        partition_rate,
        partition_num,
        partition_batch,
        partition_type,
        max_partition_batch,
        min_partition_batch,
        partition_batch_unit,
        partition_mantissa_deal,
        planned_main_resource_id,
        last_main_resource_id,
        planned_tool_resource_id,
        last_tool_resource_id,
        planned_skill_id,
        last_skill_id,
        setup_start_time,
        last_setup_start_time,
        setup_end_time,
        last_setup_end_time,
        setup_duration,
        production_start_time,
        last_production_start_time,
        production_end_time,
        last_production_end_time,
        lock_start_time,
        last_lock_start_time,
        lock_end_time,
        last_lock_end_time,
        lock_duration,
        cleanup_start_time,
        last_cleanup_start_time,
        cleanup_end_time,
        last_cleanup_end_time,
        cleanup_duration,
        feed_finish_time,
        operation_type,
        delay_reason,
        lock_status,
        unscheduled_reason,
        last_insertion,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        id,
        published_log_id)
        values (
        #{newId,jdbcType=VARCHAR},
        #{operationId,jdbcType=VARCHAR},
        #{preOperationIds,jdbcType=VARCHAR},
        #{resourceIds,jdbcType=VARCHAR},
        #{resourceFixed,jdbcType=VARCHAR},
        #{timeFixed,jdbcType=VARCHAR},
        #{nextOperationIds,jdbcType=VARCHAR},
        #{appointProductionTime,jdbcType=TIMESTAMP},
        #{appointQuantity,jdbcType=INTEGER},
        #{appointMainResourceId,jdbcType=VARCHAR},
        #{appointStartTime,jdbcType=TIMESTAMP},
        #{appointEndTime,jdbcType=TIMESTAMP},
        #{partitionRate,jdbcType=VARCHAR},
        #{partitionNum,jdbcType=INTEGER},
        #{partitionBatch,jdbcType=VARCHAR},
        #{partitionType,jdbcType=VARCHAR},
        #{maxPartitionBatch,jdbcType=VARCHAR},
        #{minPartitionBatch,jdbcType=VARCHAR},
        #{partitionBatchUnit,jdbcType=VARCHAR},
        #{partitionMantissaDeal,jdbcType=VARCHAR},
        #{plannedMainResourceId,jdbcType=VARCHAR},
        #{lastMainResourceId,jdbcType=VARCHAR},
        #{plannedToolResourceId,jdbcType=VARCHAR},
        #{lastToolResourceId,jdbcType=VARCHAR},
        #{plannedSkillId,jdbcType=VARCHAR},
        #{lastSkillId,jdbcType=VARCHAR},
        #{setupStartTime,jdbcType=TIMESTAMP},
        #{lastSetupStartTime,jdbcType=TIMESTAMP},
        #{setupEndTime,jdbcType=TIMESTAMP},
        #{lastSetupEndTime,jdbcType=TIMESTAMP},
        #{setupDuration,jdbcType=INTEGER},
        #{productionStartTime,jdbcType=TIMESTAMP},
        #{lastProductionStartTime,jdbcType=TIMESTAMP},
        #{productionEndTime,jdbcType=TIMESTAMP},
        #{lastProductionEndTime,jdbcType=TIMESTAMP},
        #{lockStartTime,jdbcType=TIMESTAMP},
        #{lastLockStartTime,jdbcType=TIMESTAMP},
        #{lockEndTime,jdbcType=TIMESTAMP},
        #{lastLockEndTime,jdbcType=TIMESTAMP},
        #{lockDuration,jdbcType=INTEGER},
        #{cleanupStartTime,jdbcType=TIMESTAMP},
        #{lastCleanupStartTime,jdbcType=TIMESTAMP},
        #{cleanupEndTime,jdbcType=TIMESTAMP},
        #{lastCleanupEndTime,jdbcType=TIMESTAMP},
        #{cleanupDuration,jdbcType=INTEGER},
        #{feedFinishTime,jdbcType=TIMESTAMP},
        #{operationType,jdbcType=VARCHAR},
        #{delayReason,jdbcType=VARCHAR},
        #{lockStatus,jdbcType=VARCHAR},
        #{unscheduledReason,jdbcType=VARCHAR},
        #{lastInsertion,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
     	#{id,jdbcType=VARCHAR},
        #{publishedLogId,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationExtendPublishedPO">
        insert into sds_ord_operation_extend_published(
        new_id,
        operation_id,
        pre_operation_ids,
        resource_ids,
        resource_fixed,
        time_fixed,
        next_operation_ids,
        appoint_production_time,
        appoint_quantity,
        appoint_main_resource_id,
        appoint_start_time,
        appoint_end_time,
        partition_rate,
        partition_num,
        partition_batch,
        partition_type,
        max_partition_batch,
        min_partition_batch,
        partition_batch_unit,
        partition_mantissa_deal,
        planned_main_resource_id,
        last_main_resource_id,
        planned_tool_resource_id,
        last_tool_resource_id,
        planned_skill_id,
        last_skill_id,
        setup_start_time,
        last_setup_start_time,
        setup_end_time,
        last_setup_end_time,
        setup_duration,
        production_start_time,
        last_production_start_time,
        production_end_time,
        last_production_end_time,
        lock_start_time,
        last_lock_start_time,
        lock_end_time,
        last_lock_end_time,
        lock_duration,
        cleanup_start_time,
        last_cleanup_start_time,
        cleanup_end_time,
        last_cleanup_end_time,
        cleanup_duration,
        feed_finish_time,
        operation_type,
        delay_reason,
        lock_status,
        unscheduled_reason,
        last_insertion,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        id,
        published_log_id)
        values (
        #{newId,jdbcType=VARCHAR},
        #{operationId,jdbcType=VARCHAR},
        #{preOperationIds,jdbcType=VARCHAR},
        #{resourceIds,jdbcType=VARCHAR},
        #{resourceFixed,jdbcType=VARCHAR},
        #{timeFixed,jdbcType=VARCHAR},
        #{nextOperationIds,jdbcType=VARCHAR},
        #{appointProductionTime,jdbcType=TIMESTAMP},
        #{appointQuantity,jdbcType=INTEGER},
        #{appointMainResourceId,jdbcType=VARCHAR},
        #{appointStartTime,jdbcType=TIMESTAMP},
        #{appointEndTime,jdbcType=TIMESTAMP},
        #{partitionRate,jdbcType=VARCHAR},
        #{partitionNum,jdbcType=INTEGER},
        #{partitionBatch,jdbcType=VARCHAR},
        #{partitionType,jdbcType=VARCHAR},
        #{maxPartitionBatch,jdbcType=VARCHAR},
        #{minPartitionBatch,jdbcType=VARCHAR},
        #{partitionBatchUnit,jdbcType=VARCHAR},
        #{partitionMantissaDeal,jdbcType=VARCHAR},
        #{plannedMainResourceId,jdbcType=VARCHAR},
        #{lastMainResourceId,jdbcType=VARCHAR},
        #{plannedToolResourceId,jdbcType=VARCHAR},
        #{lastToolResourceId,jdbcType=VARCHAR},
        #{plannedSkillId,jdbcType=VARCHAR},
        #{lastSkillId,jdbcType=VARCHAR},
        #{setupStartTime,jdbcType=TIMESTAMP},
        #{lastSetupStartTime,jdbcType=TIMESTAMP},
        #{setupEndTime,jdbcType=TIMESTAMP},
        #{lastSetupEndTime,jdbcType=TIMESTAMP},
        #{setupDuration,jdbcType=INTEGER},
        #{productionStartTime,jdbcType=TIMESTAMP},
        #{lastProductionStartTime,jdbcType=TIMESTAMP},
        #{productionEndTime,jdbcType=TIMESTAMP},
        #{lastProductionEndTime,jdbcType=TIMESTAMP},
        #{lockStartTime,jdbcType=TIMESTAMP},
        #{lastLockStartTime,jdbcType=TIMESTAMP},
        #{lockEndTime,jdbcType=TIMESTAMP},
        #{lastLockEndTime,jdbcType=TIMESTAMP},
        #{lockDuration,jdbcType=INTEGER},
        #{cleanupStartTime,jdbcType=TIMESTAMP},
        #{lastCleanupStartTime,jdbcType=TIMESTAMP},
        #{cleanupEndTime,jdbcType=TIMESTAMP},
        #{lastCleanupEndTime,jdbcType=TIMESTAMP},
        #{cleanupDuration,jdbcType=INTEGER},
        #{feedFinishTime,jdbcType=TIMESTAMP},
        #{operationType,jdbcType=VARCHAR},
        #{delayReason,jdbcType=VARCHAR},
        #{lockStatus,jdbcType=VARCHAR},
        #{unscheduledReason,jdbcType=VARCHAR},
        #{lastInsertion,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
     	#{id,jdbcType=VARCHAR},
        #{publishedLogId,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sds_ord_operation_extend_published(
        new_id,
        operation_id,
        pre_operation_ids,
        resource_ids,
        resource_fixed,
        time_fixed,
        next_operation_ids,
        appoint_production_time,
        appoint_quantity,
        appoint_main_resource_id,
        appoint_start_time,
        appoint_end_time,
        partition_rate,
        partition_num,
        partition_batch,
        partition_type,
        max_partition_batch,
        min_partition_batch,
        partition_batch_unit,
        partition_mantissa_deal,
        planned_main_resource_id,
        last_main_resource_id,
        planned_tool_resource_id,
        last_tool_resource_id,
        planned_skill_id,
        last_skill_id,
        setup_start_time,
        last_setup_start_time,
        setup_end_time,
        last_setup_end_time,
        setup_duration,
        production_start_time,
        last_production_start_time,
        production_end_time,
        last_production_end_time,
        lock_start_time,
        last_lock_start_time,
        lock_end_time,
        last_lock_end_time,
        lock_duration,
        cleanup_start_time,
        last_cleanup_start_time,
        cleanup_end_time,
        last_cleanup_end_time,
        cleanup_duration,
        feed_finish_time,
        operation_type,
        delay_reason,
        lock_status,
        unscheduled_reason,
        last_insertion,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        id,
        published_log_id)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.operationId,jdbcType=VARCHAR},
        #{entity.preOperationIds,jdbcType=VARCHAR},
        #{entity.resourceIds,jdbcType=VARCHAR},
        #{entity.resourceFixed,jdbcType=VARCHAR},
        #{entity.timeFixed,jdbcType=VARCHAR},
        #{entity.nextOperationIds,jdbcType=VARCHAR},
        #{entity.appointProductionTime,jdbcType=TIMESTAMP},
        #{entity.appointQuantity,jdbcType=INTEGER},
        #{entity.appointMainResourceId,jdbcType=VARCHAR},
        #{entity.appointStartTime,jdbcType=TIMESTAMP},
        #{entity.appointEndTime,jdbcType=TIMESTAMP},
        #{entity.partitionRate,jdbcType=VARCHAR},
        #{entity.partitionNum,jdbcType=INTEGER},
        #{entity.partitionBatch,jdbcType=VARCHAR},
        #{entity.partitionType,jdbcType=VARCHAR},
        #{entity.maxPartitionBatch,jdbcType=VARCHAR},
        #{entity.minPartitionBatch,jdbcType=VARCHAR},
        #{entity.partitionBatchUnit,jdbcType=VARCHAR},
        #{entity.partitionMantissaDeal,jdbcType=VARCHAR},
        #{entity.plannedMainResourceId,jdbcType=VARCHAR},
        #{entity.lastMainResourceId,jdbcType=VARCHAR},
        #{entity.plannedToolResourceId,jdbcType=VARCHAR},
        #{entity.lastToolResourceId,jdbcType=VARCHAR},
        #{entity.plannedSkillId,jdbcType=VARCHAR},
        #{entity.lastSkillId,jdbcType=VARCHAR},
        #{entity.setupStartTime,jdbcType=TIMESTAMP},
        #{entity.lastSetupStartTime,jdbcType=TIMESTAMP},
        #{entity.setupEndTime,jdbcType=TIMESTAMP},
        #{entity.lastSetupEndTime,jdbcType=TIMESTAMP},
        #{entity.setupDuration,jdbcType=INTEGER},
        #{entity.productionStartTime,jdbcType=TIMESTAMP},
        #{entity.lastProductionStartTime,jdbcType=TIMESTAMP},
        #{entity.productionEndTime,jdbcType=TIMESTAMP},
        #{entity.lastProductionEndTime,jdbcType=TIMESTAMP},
        #{entity.lockStartTime,jdbcType=TIMESTAMP},
        #{entity.lastLockStartTime,jdbcType=TIMESTAMP},
        #{entity.lockEndTime,jdbcType=TIMESTAMP},
        #{entity.lastLockEndTime,jdbcType=TIMESTAMP},
        #{entity.lockDuration,jdbcType=INTEGER},
        #{entity.cleanupStartTime,jdbcType=TIMESTAMP},
        #{entity.lastCleanupStartTime,jdbcType=TIMESTAMP},
        #{entity.cleanupEndTime,jdbcType=TIMESTAMP},
        #{entity.lastCleanupEndTime,jdbcType=TIMESTAMP},
        #{entity.cleanupDuration,jdbcType=INTEGER},
        #{entity.feedFinishTime,jdbcType=TIMESTAMP},
        #{entity.operationType,jdbcType=VARCHAR},
        #{entity.delayReason,jdbcType=VARCHAR},
        #{entity.lockStatus,jdbcType=VARCHAR},
        #{entity.unscheduledReason,jdbcType=VARCHAR},
        #{entity.lastInsertion,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.id,jdbcType=VARCHAR},
        #{entity.publishedLogId,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into sds_ord_operation_extend_published(
        new_id,
        operation_id,
        pre_operation_ids,
        resource_ids,
        resource_fixed,
        time_fixed,
        next_operation_ids,
        appoint_production_time,
        appoint_quantity,
        appoint_main_resource_id,
        appoint_start_time,
        appoint_end_time,
        partition_rate,
        partition_num,
        partition_batch,
        partition_type,
        max_partition_batch,
        min_partition_batch,
        partition_batch_unit,
        partition_mantissa_deal,
        planned_main_resource_id,
        last_main_resource_id,
        planned_tool_resource_id,
        last_tool_resource_id,
        planned_skill_id,
        last_skill_id,
        setup_start_time,
        last_setup_start_time,
        setup_end_time,
        last_setup_end_time,
        setup_duration,
        production_start_time,
        last_production_start_time,
        production_end_time,
        last_production_end_time,
        lock_start_time,
        last_lock_start_time,
        lock_end_time,
        last_lock_end_time,
        lock_duration,
        cleanup_start_time,
        last_cleanup_start_time,
        cleanup_end_time,
        last_cleanup_end_time,
        cleanup_duration,
        feed_finish_time,
        operation_type,
        delay_reason,
        lock_status,
        unscheduled_reason,
        last_insertion,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        id,
        published_log_id)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.newId,jdbcType=VARCHAR},
        #{entity.operationId,jdbcType=VARCHAR},
        #{entity.preOperationIds,jdbcType=VARCHAR},
        #{entity.resourceIds,jdbcType=VARCHAR},
        #{entity.resourceFixed,jdbcType=VARCHAR},
        #{entity.timeFixed,jdbcType=VARCHAR},
        #{entity.nextOperationIds,jdbcType=VARCHAR},
        #{entity.appointProductionTime,jdbcType=TIMESTAMP},
        #{entity.appointQuantity,jdbcType=INTEGER},
        #{entity.appointMainResourceId,jdbcType=VARCHAR},
        #{entity.appointStartTime,jdbcType=TIMESTAMP},
        #{entity.appointEndTime,jdbcType=TIMESTAMP},
        #{entity.partitionRate,jdbcType=VARCHAR},
        #{entity.partitionNum,jdbcType=INTEGER},
        #{entity.partitionBatch,jdbcType=VARCHAR},
        #{entity.partitionType,jdbcType=VARCHAR},
        #{entity.maxPartitionBatch,jdbcType=VARCHAR},
        #{entity.minPartitionBatch,jdbcType=VARCHAR},
        #{entity.partitionBatchUnit,jdbcType=VARCHAR},
        #{entity.partitionMantissaDeal,jdbcType=VARCHAR},
        #{entity.plannedMainResourceId,jdbcType=VARCHAR},
        #{entity.lastMainResourceId,jdbcType=VARCHAR},
        #{entity.plannedToolResourceId,jdbcType=VARCHAR},
        #{entity.lastToolResourceId,jdbcType=VARCHAR},
        #{entity.plannedSkillId,jdbcType=VARCHAR},
        #{entity.lastSkillId,jdbcType=VARCHAR},
        #{entity.setupStartTime,jdbcType=TIMESTAMP},
        #{entity.lastSetupStartTime,jdbcType=TIMESTAMP},
        #{entity.setupEndTime,jdbcType=TIMESTAMP},
        #{entity.lastSetupEndTime,jdbcType=TIMESTAMP},
        #{entity.setupDuration,jdbcType=INTEGER},
        #{entity.productionStartTime,jdbcType=TIMESTAMP},
        #{entity.lastProductionStartTime,jdbcType=TIMESTAMP},
        #{entity.productionEndTime,jdbcType=TIMESTAMP},
        #{entity.lastProductionEndTime,jdbcType=TIMESTAMP},
        #{entity.lockStartTime,jdbcType=TIMESTAMP},
        #{entity.lastLockStartTime,jdbcType=TIMESTAMP},
        #{entity.lockEndTime,jdbcType=TIMESTAMP},
        #{entity.lastLockEndTime,jdbcType=TIMESTAMP},
        #{entity.lockDuration,jdbcType=INTEGER},
        #{entity.cleanupStartTime,jdbcType=TIMESTAMP},
        #{entity.lastCleanupStartTime,jdbcType=TIMESTAMP},
        #{entity.cleanupEndTime,jdbcType=TIMESTAMP},
        #{entity.lastCleanupEndTime,jdbcType=TIMESTAMP},
        #{entity.cleanupDuration,jdbcType=INTEGER},
        #{entity.feedFinishTime,jdbcType=TIMESTAMP},
        #{entity.operationType,jdbcType=VARCHAR},
        #{entity.delayReason,jdbcType=VARCHAR},
        #{entity.lockStatus,jdbcType=VARCHAR},
        #{entity.unscheduledReason,jdbcType=VARCHAR},
        #{entity.lastInsertion,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.id,jdbcType=VARCHAR},
        #{entity.publishedLogId,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationExtendPublishedPO">
        update sds_ord_operation_extend_published set
        operation_id = #{operationId,jdbcType=VARCHAR},
        pre_operation_ids = #{preOperationIds,jdbcType=VARCHAR},
        resource_ids = #{resourceIds,jdbcType=VARCHAR},
        resource_fixed = #{resourceFixed,jdbcType=VARCHAR},
        time_fixed = #{timeFixed,jdbcType=VARCHAR},
        next_operation_ids = #{nextOperationIds,jdbcType=VARCHAR},
        appoint_production_time = #{appointProductionTime,jdbcType=TIMESTAMP},
        appoint_quantity = #{appointQuantity,jdbcType=INTEGER},
        appoint_main_resource_id = #{appointMainResourceId,jdbcType=VARCHAR},
        appoint_start_time = #{appointStartTime,jdbcType=TIMESTAMP},
        appoint_end_time = #{appointEndTime,jdbcType=TIMESTAMP},
        partition_rate = #{partitionRate,jdbcType=VARCHAR},
        partition_num = #{partitionNum,jdbcType=INTEGER},
        partition_batch = #{partitionBatch,jdbcType=VARCHAR},
        partition_type = #{partitionType,jdbcType=VARCHAR},
        max_partition_batch = #{maxPartitionBatch,jdbcType=VARCHAR},
        min_partition_batch = #{minPartitionBatch,jdbcType=VARCHAR},
        partition_batch_unit = #{partitionBatchUnit,jdbcType=VARCHAR},
        partition_mantissa_deal = #{partitionMantissaDeal,jdbcType=VARCHAR},
        planned_main_resource_id = #{plannedMainResourceId,jdbcType=VARCHAR},
        last_main_resource_id = #{lastMainResourceId,jdbcType=VARCHAR},
        planned_tool_resource_id = #{plannedToolResourceId,jdbcType=VARCHAR},
        last_tool_resource_id = #{lastToolResourceId,jdbcType=VARCHAR},
        planned_skill_id = #{plannedSkillId,jdbcType=VARCHAR},
        last_skill_id = #{lastSkillId,jdbcType=VARCHAR},
        setup_start_time = #{setupStartTime,jdbcType=TIMESTAMP},
        last_setup_start_time = #{lastSetupStartTime,jdbcType=TIMESTAMP},
        setup_end_time = #{setupEndTime,jdbcType=TIMESTAMP},
        last_setup_end_time = #{lastSetupEndTime,jdbcType=TIMESTAMP},
        setup_duration = #{setupDuration,jdbcType=INTEGER},
        production_start_time = #{productionStartTime,jdbcType=TIMESTAMP},
        last_production_start_time = #{lastProductionStartTime,jdbcType=TIMESTAMP},
        production_end_time = #{productionEndTime,jdbcType=TIMESTAMP},
        last_production_end_time = #{lastProductionEndTime,jdbcType=TIMESTAMP},
        lock_start_time = #{lockStartTime,jdbcType=TIMESTAMP},
        last_lock_start_time = #{lastLockStartTime,jdbcType=TIMESTAMP},
        lock_end_time = #{lockEndTime,jdbcType=TIMESTAMP},
        last_lock_end_time = #{lastLockEndTime,jdbcType=TIMESTAMP},
        lock_duration = #{lockDuration,jdbcType=INTEGER},
        cleanup_start_time = #{cleanupStartTime,jdbcType=TIMESTAMP},
        last_cleanup_start_time = #{lastCleanupStartTime,jdbcType=TIMESTAMP},
        cleanup_end_time = #{cleanupEndTime,jdbcType=TIMESTAMP},
        last_cleanup_end_time = #{lastCleanupEndTime,jdbcType=TIMESTAMP},
        cleanup_duration = #{cleanupDuration,jdbcType=INTEGER},
        feed_finish_time = #{feedFinishTime,jdbcType=TIMESTAMP},
        operation_type = #{operationType,jdbcType=VARCHAR},
        delay_reason = #{delayReason,jdbcType=VARCHAR},
        lock_status = #{lockStatus,jdbcType=VARCHAR},
        unscheduled_reason = #{unscheduledReason,jdbcType=VARCHAR},
        last_insertion = #{lastInsertion,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        id = #{id,jdbcType=VARCHAR},
        published_log_id = #{publishedLogId,jdbcType=VARCHAR}
        where new_id = #{newId,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationExtendPublishedPO">
        update sds_ord_operation_extend_published
        <set>
            <if test="item.operationId != null and item.operationId != ''">
                operation_id = #{item.operationId,jdbcType=VARCHAR},
            </if>
            <if test="item.preOperationIds != null and item.preOperationIds != ''">
                pre_operation_ids = #{item.preOperationIds,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceIds != null and item.resourceIds != ''">
                resource_ids = #{item.resourceIds,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceFixed != null and item.resourceFixed != ''">
                resource_fixed = #{item.resourceFixed,jdbcType=VARCHAR},
            </if>
            <if test="item.timeFixed != null and item.timeFixed != ''">
                time_fixed = #{item.timeFixed,jdbcType=VARCHAR},
            </if>
            <if test="item.nextOperationIds != null and item.nextOperationIds != ''">
                next_operation_ids = #{item.nextOperationIds,jdbcType=VARCHAR},
            </if>
            <if test="item.appointProductionTime != null">
                appoint_production_time = #{item.appointProductionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.appointQuantity != null">
                appoint_quantity = #{item.appointQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.appointMainResourceId != null and item.appointMainResourceId != ''">
                appoint_main_resource_id = #{item.appointMainResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.appointStartTime != null">
                appoint_start_time = #{item.appointStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.appointEndTime != null">
                appoint_end_time = #{item.appointEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.partitionRate != null">
                partition_rate = #{item.partitionRate,jdbcType=VARCHAR},
            </if>
            <if test="item.partitionNum != null">
                partition_num = #{item.partitionNum,jdbcType=INTEGER},
            </if>
            <if test="item.partitionBatch != null">
                partition_batch = #{item.partitionBatch,jdbcType=VARCHAR},
            </if>
            <if test="item.partitionType != null and item.partitionType != ''">
                partition_type = #{item.partitionType,jdbcType=VARCHAR},
            </if>
            <if test="item.maxPartitionBatch != null">
                max_partition_batch = #{item.maxPartitionBatch,jdbcType=VARCHAR},
            </if>
            <if test="item.minPartitionBatch != null">
                min_partition_batch = #{item.minPartitionBatch,jdbcType=VARCHAR},
            </if>
            <if test="item.partitionBatchUnit != null and item.partitionBatchUnit != ''">
                partition_batch_unit = #{item.partitionBatchUnit,jdbcType=VARCHAR},
            </if>
            <if test="item.partitionMantissaDeal != null and item.partitionMantissaDeal != ''">
                partition_mantissa_deal = #{item.partitionMantissaDeal,jdbcType=VARCHAR},
            </if>
            <if test="item.plannedMainResourceId != null and item.plannedMainResourceId != ''">
                planned_main_resource_id = #{item.plannedMainResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.lastMainResourceId != null and item.lastMainResourceId != ''">
                last_main_resource_id = #{item.lastMainResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.plannedToolResourceId != null and item.plannedToolResourceId != ''">
                planned_tool_resource_id = #{item.plannedToolResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.lastToolResourceId != null and item.lastToolResourceId != ''">
                last_tool_resource_id = #{item.lastToolResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.plannedSkillId != null and item.plannedSkillId != ''">
                planned_skill_id = #{item.plannedSkillId,jdbcType=VARCHAR},
            </if>
            <if test="item.lastSkillId != null and item.lastSkillId != ''">
                last_skill_id = #{item.lastSkillId,jdbcType=VARCHAR},
            </if>
            <if test="item.setupStartTime != null">
                setup_start_time = #{item.setupStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastSetupStartTime != null">
                last_setup_start_time = #{item.lastSetupStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.setupEndTime != null">
                setup_end_time = #{item.setupEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastSetupEndTime != null">
                last_setup_end_time = #{item.lastSetupEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.setupDuration != null">
                setup_duration = #{item.setupDuration,jdbcType=INTEGER},
            </if>
            <if test="item.productionStartTime != null">
                production_start_time = #{item.productionStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastProductionStartTime != null">
                last_production_start_time = #{item.lastProductionStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.productionEndTime != null">
                production_end_time = #{item.productionEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastProductionEndTime != null">
                last_production_end_time = #{item.lastProductionEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lockStartTime != null">
                lock_start_time = #{item.lockStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastLockStartTime != null">
                last_lock_start_time = #{item.lastLockStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lockEndTime != null">
                lock_end_time = #{item.lockEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastLockEndTime != null">
                last_lock_end_time = #{item.lastLockEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lockDuration != null">
                lock_duration = #{item.lockDuration,jdbcType=INTEGER},
            </if>
            <if test="item.cleanupStartTime != null">
                cleanup_start_time = #{item.cleanupStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastCleanupStartTime != null">
                last_cleanup_start_time = #{item.lastCleanupStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.cleanupEndTime != null">
                cleanup_end_time = #{item.cleanupEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastCleanupEndTime != null">
                last_cleanup_end_time = #{item.lastCleanupEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.cleanupDuration != null">
                cleanup_duration = #{item.cleanupDuration,jdbcType=INTEGER},
            </if>
            <if test="item.feedFinishTime != null">
                feed_finish_time = #{item.feedFinishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.operationType != null and item.operationType != ''">
                operation_type = #{item.operationType,jdbcType=VARCHAR},
            </if>
            <if test="item.delayReason != null and item.delayReason != ''">
                delay_reason = #{item.delayReason,jdbcType=VARCHAR},
            </if>
            <if test="item.lockStatus != null and item.lockStatus != ''">
                lock_status = #{item.lockStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.unscheduledReason != null and item.unscheduledReason != ''">
                unscheduled_reason = #{item.unscheduledReason,jdbcType=VARCHAR},
            </if>
            <if test="item.lastInsertion != null and item.lastInsertion != ''">
                last_insertion = #{item.lastInsertion,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.id != null and item.id != ''">
                id = #{item.id,jdbcType=VARCHAR},
            </if>
            <if test="item.publishedLogId != null and item.publishedLogId != ''">
                published_log_id = #{item.publishedLogId,jdbcType=VARCHAR},
            </if>
        </set>
        where new_id = #{item.newId,jdbcType=VARCHAR}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        update sds_ord_operation_extend_published
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="operation_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.operationId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="pre_operation_ids = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.preOperationIds,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_ids = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.resourceIds,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_fixed = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.resourceFixed,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="time_fixed = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.timeFixed,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="next_operation_ids = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.nextOperationIds,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="appoint_production_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.appointProductionTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="appoint_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.appointQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="appoint_main_resource_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.appointMainResourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="appoint_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.appointStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="appoint_end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.appointEndTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="partition_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.partitionRate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="partition_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.partitionNum,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="partition_batch = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.partitionBatch,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="partition_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.partitionType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="max_partition_batch = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.maxPartitionBatch,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="min_partition_batch = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.minPartitionBatch,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="partition_batch_unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.partitionBatchUnit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="partition_mantissa_deal = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.partitionMantissaDeal,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="planned_main_resource_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.plannedMainResourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_main_resource_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lastMainResourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="planned_tool_resource_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.plannedToolResourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_tool_resource_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lastToolResourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="planned_skill_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.plannedSkillId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_skill_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lastSkillId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="setup_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.setupStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="last_setup_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lastSetupStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="setup_end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.setupEndTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="last_setup_end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lastSetupEndTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="setup_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.setupDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="production_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.productionStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="last_production_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lastProductionStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="production_end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.productionEndTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="last_production_end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lastProductionEndTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="lock_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lockStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="last_lock_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lastLockStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="lock_end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lockEndTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="last_lock_end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lastLockEndTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="lock_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lockDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="cleanup_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.cleanupStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="last_cleanup_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lastCleanupStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="cleanup_end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.cleanupEndTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="last_cleanup_end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lastCleanupEndTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="cleanup_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.cleanupDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="feed_finish_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.feedFinishTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="operation_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.operationType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="delay_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.delayReason,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lock_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lockStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="unscheduled_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.unscheduledReason,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_insertion = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lastInsertion,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.id,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="published_log_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.publishedLogId,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where new_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.newId,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update sds_ord_operation_extend_published 
        <set>
            <if test="item.operationId != null and item.operationId != ''">
                operation_id = #{item.operationId,jdbcType=VARCHAR},
            </if>
            <if test="item.preOperationIds != null and item.preOperationIds != ''">
                pre_operation_ids = #{item.preOperationIds,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceIds != null and item.resourceIds != ''">
                resource_ids = #{item.resourceIds,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceFixed != null and item.resourceFixed != ''">
                resource_fixed = #{item.resourceFixed,jdbcType=VARCHAR},
            </if>
            <if test="item.timeFixed != null and item.timeFixed != ''">
                time_fixed = #{item.timeFixed,jdbcType=VARCHAR},
            </if>
            <if test="item.nextOperationIds != null and item.nextOperationIds != ''">
                next_operation_ids = #{item.nextOperationIds,jdbcType=VARCHAR},
            </if>
            <if test="item.appointProductionTime != null">
                appoint_production_time = #{item.appointProductionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.appointQuantity != null">
                appoint_quantity = #{item.appointQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.appointMainResourceId != null and item.appointMainResourceId != ''">
                appoint_main_resource_id = #{item.appointMainResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.appointStartTime != null">
                appoint_start_time = #{item.appointStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.appointEndTime != null">
                appoint_end_time = #{item.appointEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.partitionRate != null">
                partition_rate = #{item.partitionRate,jdbcType=VARCHAR},
            </if>
            <if test="item.partitionNum != null">
                partition_num = #{item.partitionNum,jdbcType=INTEGER},
            </if>
            <if test="item.partitionBatch != null">
                partition_batch = #{item.partitionBatch,jdbcType=VARCHAR},
            </if>
            <if test="item.partitionType != null and item.partitionType != ''">
                partition_type = #{item.partitionType,jdbcType=VARCHAR},
            </if>
            <if test="item.maxPartitionBatch != null">
                max_partition_batch = #{item.maxPartitionBatch,jdbcType=VARCHAR},
            </if>
            <if test="item.minPartitionBatch != null">
                min_partition_batch = #{item.minPartitionBatch,jdbcType=VARCHAR},
            </if>
            <if test="item.partitionBatchUnit != null and item.partitionBatchUnit != ''">
                partition_batch_unit = #{item.partitionBatchUnit,jdbcType=VARCHAR},
            </if>
            <if test="item.partitionMantissaDeal != null and item.partitionMantissaDeal != ''">
                partition_mantissa_deal = #{item.partitionMantissaDeal,jdbcType=VARCHAR},
            </if>
            <if test="item.plannedMainResourceId != null and item.plannedMainResourceId != ''">
                planned_main_resource_id = #{item.plannedMainResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.lastMainResourceId != null and item.lastMainResourceId != ''">
                last_main_resource_id = #{item.lastMainResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.plannedToolResourceId != null and item.plannedToolResourceId != ''">
                planned_tool_resource_id = #{item.plannedToolResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.lastToolResourceId != null and item.lastToolResourceId != ''">
                last_tool_resource_id = #{item.lastToolResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.plannedSkillId != null and item.plannedSkillId != ''">
                planned_skill_id = #{item.plannedSkillId,jdbcType=VARCHAR},
            </if>
            <if test="item.lastSkillId != null and item.lastSkillId != ''">
                last_skill_id = #{item.lastSkillId,jdbcType=VARCHAR},
            </if>
            <if test="item.setupStartTime != null">
                setup_start_time = #{item.setupStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastSetupStartTime != null">
                last_setup_start_time = #{item.lastSetupStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.setupEndTime != null">
                setup_end_time = #{item.setupEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastSetupEndTime != null">
                last_setup_end_time = #{item.lastSetupEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.setupDuration != null">
                setup_duration = #{item.setupDuration,jdbcType=INTEGER},
            </if>
            <if test="item.productionStartTime != null">
                production_start_time = #{item.productionStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastProductionStartTime != null">
                last_production_start_time = #{item.lastProductionStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.productionEndTime != null">
                production_end_time = #{item.productionEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastProductionEndTime != null">
                last_production_end_time = #{item.lastProductionEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lockStartTime != null">
                lock_start_time = #{item.lockStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastLockStartTime != null">
                last_lock_start_time = #{item.lastLockStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lockEndTime != null">
                lock_end_time = #{item.lockEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastLockEndTime != null">
                last_lock_end_time = #{item.lastLockEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lockDuration != null">
                lock_duration = #{item.lockDuration,jdbcType=INTEGER},
            </if>
            <if test="item.cleanupStartTime != null">
                cleanup_start_time = #{item.cleanupStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastCleanupStartTime != null">
                last_cleanup_start_time = #{item.lastCleanupStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.cleanupEndTime != null">
                cleanup_end_time = #{item.cleanupEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastCleanupEndTime != null">
                last_cleanup_end_time = #{item.lastCleanupEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.cleanupDuration != null">
                cleanup_duration = #{item.cleanupDuration,jdbcType=INTEGER},
            </if>
            <if test="item.feedFinishTime != null">
                feed_finish_time = #{item.feedFinishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.operationType != null and item.operationType != ''">
                operation_type = #{item.operationType,jdbcType=VARCHAR},
            </if>
            <if test="item.delayReason != null and item.delayReason != ''">
                delay_reason = #{item.delayReason,jdbcType=VARCHAR},
            </if>
            <if test="item.lockStatus != null and item.lockStatus != ''">
                lock_status = #{item.lockStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.unscheduledReason != null and item.unscheduledReason != ''">
                unscheduled_reason = #{item.unscheduledReason,jdbcType=VARCHAR},
            </if>
            <if test="item.lastInsertion != null and item.lastInsertion != ''">
                last_insertion = #{item.lastInsertion,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.id != null and item.id != ''">
                id = #{item.id,jdbcType=VARCHAR},
            </if>
            <if test="item.publishedLogId != null and item.publishedLogId != ''">
                published_log_id = #{item.publishedLogId,jdbcType=VARCHAR},
            </if>
        </set>  
        where new_id = #{item.newId,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from sds_ord_operation_extend_published where new_id = #{newId,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sds_ord_operation_extend_published where new_id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    
    
    <update id="truncateTable">
        TRUNCATE TABLE sds_ord_operation_extend_published
    </update>
    
    <insert id="doSnapshotData">
	 INSERT INTO sds_ord_operation_extend_published (
			new_id,
			id,
			operation_id,
			pre_operation_ids,
			resource_ids,
			resource_fixed,
			time_fixed,
			next_operation_ids,
			appoint_production_time,
			appoint_quantity,
			appoint_main_resource_id,
			appoint_start_time,
			appoint_end_time,
			partition_rate,
			partition_num,
			partition_batch,
			partition_type,
			max_partition_batch,
			min_partition_batch,
			partition_batch_unit,
			partition_mantissa_deal,
			planned_main_resource_id,
			last_main_resource_id,
			planned_tool_resource_id,
			last_tool_resource_id,
			planned_skill_id,
			last_skill_id,
			setup_start_time,
			last_setup_start_time,
			setup_end_time,
			last_setup_end_time,
			setup_duration,
			production_start_time,
			last_production_start_time,
			production_end_time,
			last_production_end_time,
			lock_start_time,
			last_lock_start_time,
			lock_end_time,
			last_lock_end_time,
			lock_duration,
			cleanup_start_time,
			last_cleanup_start_time,
			cleanup_end_time,
			last_cleanup_end_time,
			cleanup_duration,
			feed_finish_time,
			operation_type,
			delay_reason,
			lock_status,
			unscheduled_reason,
			last_insertion,
			remark,
			enabled,
			creator,
			create_time,
			modifier,
			modify_time,
			published_log_id
		) 
		SELECT
			CONCAT(id, ${publishedTime}) as new_id,
			id as id,
			operation_id,
			pre_operation_ids,
			resource_ids,
			resource_fixed,
			time_fixed,
			next_operation_ids,
			appoint_production_time,
			appoint_quantity,
			appoint_main_resource_id,
			appoint_start_time,
			appoint_end_time,
			partition_rate,
			partition_num,
			partition_batch,
			partition_type,
			max_partition_batch,
			min_partition_batch,
			partition_batch_unit,
			partition_mantissa_deal,
			planned_main_resource_id,
			last_main_resource_id,
			planned_tool_resource_id,
			last_tool_resource_id,
			planned_skill_id,
			last_skill_id,
			setup_start_time,
			last_setup_start_time,
			setup_end_time,
			last_setup_end_time,
			setup_duration,
			production_start_time,
			last_production_start_time,
			production_end_time,
			last_production_end_time,
			lock_start_time,
			last_lock_start_time,
			lock_end_time,
			last_lock_end_time,
			lock_duration,
			cleanup_start_time,
			last_cleanup_start_time,
			cleanup_end_time,
			last_cleanup_end_time,
			cleanup_duration,
			feed_finish_time,
			operation_type,
			delay_reason,
			lock_status,
			unscheduled_reason,
			last_insertion,
			remark,
			enabled,
			creator,
			create_time,
			modifier,
			modify_time,
			${publishedLogId} AS published_log_id
		FROM
			sds_ord_operation_extend
	</insert>
	
	<delete id="deleteByPublishedLogId" parameterType="java.lang.String">
        delete from 
        	sds_ord_operation_extend_published 
        where 
        	published_log_id = #{publishedLogId,jdbcType=VARCHAR}
    </delete>
    
    <select id="selectAllCount" resultType="java.lang.Integer">
        select
        	count(*)
        from sds_ord_operation_extend
    </select>
    
    <select id="selectOperationExtendByOperationIds" resultMap="POResultMap">
        SELECT
			id as id,
			operation_id,
			pre_operation_ids,
			resource_ids,
			resource_fixed,
			time_fixed,
			next_operation_ids,
			appoint_production_time,
			appoint_quantity,
			appoint_main_resource_id,
			appoint_start_time,
			appoint_end_time,
			partition_rate,
			partition_num,
			partition_batch,
			partition_type,
			max_partition_batch,
			min_partition_batch,
			partition_batch_unit,
			partition_mantissa_deal,
			planned_main_resource_id,
			last_main_resource_id,
			planned_tool_resource_id,
			last_tool_resource_id,
			planned_skill_id,
			last_skill_id,
			setup_start_time,
			last_setup_start_time,
			setup_end_time,
			last_setup_end_time,
			setup_duration,
			production_start_time,
			last_production_start_time,
			production_end_time,
			last_production_end_time,
			lock_start_time,
			last_lock_start_time,
			lock_end_time,
			last_lock_end_time,
			lock_duration,
			cleanup_start_time,
			last_cleanup_start_time,
			cleanup_end_time,
			last_cleanup_end_time,
			cleanup_duration,
			feed_finish_time,
			operation_type,
			delay_reason,
			lock_status,
			unscheduled_reason,
			last_insertion,
			remark,
			enabled,
			creator,
			create_time,
			modifier,
			modify_time
		FROM
			sds_ord_operation_extend
		where 
			operation_id in	
		<foreach collection="operationIds" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    
    <delete id="deleteByPublishedLogIds">
        delete 
        from 
        	sds_ord_operation_extend_published 
    	where 
    		published_log_id in 
    		<foreach collection="publishedLogIds" item="item" index="index" open="(" separator="," close=")">
            	#{item,jdbcType=VARCHAR}
        	</foreach>
    </delete>
</mapper>
