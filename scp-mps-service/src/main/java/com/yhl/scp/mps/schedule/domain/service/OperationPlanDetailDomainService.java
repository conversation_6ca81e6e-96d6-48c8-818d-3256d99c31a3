package com.yhl.scp.mps.schedule.domain.service;

import com.yhl.scp.ams.extension.schedule.domain.entity.OperationPlanDetailDO;
import com.yhl.scp.ams.schedule.infrastructure.dao.OperationPlanDetailDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>OperationTaskDomainService</code>
 * <p>
 * 工序任务领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-05-06 15:25:04
 */
@Service
public class OperationPlanDetailDomainService {

    @Resource
    private OperationPlanDetailDao operationPlanDetailDao;

    /**
     * 数据校验
     *
     * @param operationPlanDetailDO 领域对象
     */
    public void validation(OperationPlanDetailDO operationPlanDetailDO) {
        checkNotNull(operationPlanDetailDO);
        checkUniqueCode(operationPlanDetailDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param operationPlanDetailDO 领域对象
     */
    private void checkNotNull(OperationPlanDetailDO operationPlanDetailDO) {
        // TODO
    }

    /**
     * 唯一性校验
     *
     * @param operationPlanDetailDO 领域对象
     */
    private void checkUniqueCode(OperationPlanDetailDO operationPlanDetailDO) {
        // TODO
    }

}
