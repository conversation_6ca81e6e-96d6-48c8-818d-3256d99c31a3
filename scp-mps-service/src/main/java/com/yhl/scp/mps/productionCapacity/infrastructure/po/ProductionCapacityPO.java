package com.yhl.scp.mps.productionCapacity.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;

/**
 * <code>ProductionCapacityPO</code>
 * <p>
 * 工序后库容量PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-29 20:48:15
 */
public class ProductionCapacityPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 341636983268516953L;

        /**
     * 公司
     */
        private String company;
        /**
     * 工厂
     */
        private String plantCode;
        /**
     * 装车位置
     */
        private String loadingPosition;
        /**
     * 工序代码
     */
        private String operationCode;
        /**
     * 工序名称
     */
        private String operationName;
        /**
     * 工序后库容量（理论）
     */
        private Integer procedureCapacityTheory;
        /**
     * 工序后库容量（实际约束）
     */
        private Integer procedureCapacityActual;
        /**
     * 版本号
     */
        private Integer versionValue;

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getPlantCode() {
        return plantCode;
    }

    public void setPlantCode(String plantCode) {
        this.plantCode = plantCode;
    }

    public String getLoadingPosition() {
        return loadingPosition;
    }

    public void setLoadingPosition(String loadingPosition) {
        this.loadingPosition = loadingPosition;
    }

    public String getOperationCode() {
        return operationCode;
    }

    public void setOperationCode(String operationCode) {
        this.operationCode = operationCode;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public Integer getProcedureCapacityTheory() {
        return procedureCapacityTheory;
    }

    public void setProcedureCapacityTheory(Integer procedureCapacityTheory) {
        this.procedureCapacityTheory = procedureCapacityTheory;
    }

    public Integer getProcedureCapacityActual() {
        return procedureCapacityActual;
    }

    public void setProcedureCapacityActual(Integer procedureCapacityActual) {
        this.procedureCapacityActual = procedureCapacityActual;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
