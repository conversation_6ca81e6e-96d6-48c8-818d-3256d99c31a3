package com.yhl.scp.mps.resource.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>ResourceGroupPO</code>
 * <p>
 * 资源组PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-26 13:41:35
 */
public class ResourceGroupPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -88625911192391247L;

    /**
     * 主键
     */
    private String resourceGroupId;
    /**
     * 资源组名称
     */
    private String resourceGroupName;
    private String valid;
    private String creatorName;
    private String modifierName;
    /**
     * 是否瓶颈资源
     */
    private String bottleneck;
    /**
     * 显示顺序
     */
    private Integer indexNum;
    private String disabled;
    /**
     * 工厂id
     */
    private String plantId;
    /**
     * 资源组类型
     */
    private String groupType;
    /**
     * 资源组代码
     */
    private String resourceGroupCode;
    /**
     * 生产组织代码
     */
    private String orgCode;
    /**
     * 生产组织名称
     */
    private String orgName;
    /**
     * 公司代码
     */
    private String companyCode;

    public String getResourceGroupId() {
        return resourceGroupId;
    }

    public void setResourceGroupId(String resourceGroupId) {
        this.resourceGroupId = resourceGroupId;
    }

    public String getResourceGroupName() {
        return resourceGroupName;
    }

    public void setResourceGroupName(String resourceGroupName) {
        this.resourceGroupName = resourceGroupName;
    }

    public String getValid() {
        return valid;
    }

    public void setValid(String valid) {
        this.valid = valid;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getModifierName() {
        return modifierName;
    }

    public void setModifierName(String modifierName) {
        this.modifierName = modifierName;
    }

    public String getBottleneck() {
        return bottleneck;
    }

    public void setBottleneck(String bottleneck) {
        this.bottleneck = bottleneck;
    }

    public Integer getIndexNum() {
        return indexNum;
    }

    public void setIndexNum(Integer indexNum) {
        this.indexNum = indexNum;
    }

    public String getDisabled() {
        return disabled;
    }

    public void setDisabled(String disabled) {
        this.disabled = disabled;
    }

    public String getPlantId() {
        return plantId;
    }

    public void setPlantId(String plantId) {
        this.plantId = plantId;
    }

    public String getGroupType() {
        return groupType;
    }

    public void setGroupType(String groupType) {
        this.groupType = groupType;
    }

    public String getResourceGroupCode() {
        return resourceGroupCode;
    }

    public void setResourceGroupCode(String resourceGroupCode) {
        this.resourceGroupCode = resourceGroupCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

}
