package com.yhl.scp.mps.demand.convertor;

import com.yhl.scp.mps.demand.domain.entity.DemandEarlyWarningDO;
import com.yhl.scp.mps.demand.dto.DemandEarlyWarningDTO;
import com.yhl.scp.mps.demand.infrastructure.po.DemandEarlyWarningPO;
import com.yhl.scp.mps.demand.vo.DemandEarlyWarningVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>DemandEarlyWarningConvertor</code>
 * <p>
 * 需求异常预计信息转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-06 17:40:17
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DemandEarlyWarningConvertor {

    DemandEarlyWarningConvertor INSTANCE = Mappers.getMapper(DemandEarlyWarningConvertor.class);

    DemandEarlyWarningDO dto2Do(DemandEarlyWarningDTO obj);

    List<DemandEarlyWarningDO> dto2Dos(List<DemandEarlyWarningDTO> list);

    DemandEarlyWarningDTO do2Dto(DemandEarlyWarningDO obj);

    List<DemandEarlyWarningDTO> do2Dtos(List<DemandEarlyWarningDO> list);

    DemandEarlyWarningDTO vo2Dto(DemandEarlyWarningVO obj);

    List<DemandEarlyWarningDTO> vo2Dtos(List<DemandEarlyWarningVO> list);

    DemandEarlyWarningVO po2Vo(DemandEarlyWarningPO obj);

    List<DemandEarlyWarningVO> po2Vos(List<DemandEarlyWarningPO> list);

    DemandEarlyWarningPO dto2Po(DemandEarlyWarningDTO obj);

    List<DemandEarlyWarningPO> dto2Pos(List<DemandEarlyWarningDTO> obj);

    DemandEarlyWarningVO do2Vo(DemandEarlyWarningDO obj);

    DemandEarlyWarningPO do2Po(DemandEarlyWarningDO obj);

    DemandEarlyWarningDO po2Do(DemandEarlyWarningPO obj);

}
