package com.yhl.scp.mps.operationPublished.convertor;

import com.yhl.scp.mps.operationPublished.domain.entity.SupplyPublishedDO;
import com.yhl.scp.mps.operationPublished.dto.SupplyPublishedDTO;
import com.yhl.scp.mps.operationPublished.infrastructure.po.SupplyPublishedPO;
import com.yhl.scp.mps.operationPublished.vo.SupplyPublishedVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>SupplyPublishedConvertor</code>
 * <p>
 * 供应发布信息表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 10:04:16
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SupplyPublishedConvertor {

    SupplyPublishedConvertor INSTANCE = Mappers.getMapper(SupplyPublishedConvertor.class);

    SupplyPublishedDO dto2Do(SupplyPublishedDTO obj);

    SupplyPublishedDTO do2Dto(SupplyPublishedDO obj);

    List<SupplyPublishedDO> dto2Dos(List<SupplyPublishedDTO> list);

    List<SupplyPublishedDTO> do2Dtos(List<SupplyPublishedDO> list);

    SupplyPublishedVO do2Vo(SupplyPublishedDO obj);

    SupplyPublishedVO po2Vo(SupplyPublishedPO obj);

    List<SupplyPublishedVO> po2Vos(List<SupplyPublishedPO> list);

    SupplyPublishedPO do2Po(SupplyPublishedDO obj);

    SupplyPublishedDO po2Do(SupplyPublishedPO obj);

    SupplyPublishedPO dto2Po(SupplyPublishedDTO obj);

    List<SupplyPublishedPO> dto2Pos(List<SupplyPublishedDTO> obj);

}
