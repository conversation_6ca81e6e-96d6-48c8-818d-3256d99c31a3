package com.yhl.scp.mps.operationPublished.convertor;

import com.yhl.scp.mps.operationPublished.domain.entity.FulfillmentPublishedDO;
import com.yhl.scp.mps.operationPublished.dto.FulfillmentPublishedDTO;
import com.yhl.scp.mps.operationPublished.infrastructure.po.FulfillmentPublishedPO;
import com.yhl.scp.mps.operationPublished.vo.FulfillmentPublishedVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>FulfillmentPublishedConvertor</code>
 * <p>
 * 分配关系发布信息表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 10:05:30
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FulfillmentPublishedConvertor {

    FulfillmentPublishedConvertor INSTANCE = Mappers.getMapper(FulfillmentPublishedConvertor.class);

    FulfillmentPublishedDO dto2Do(FulfillmentPublishedDTO obj);

    FulfillmentPublishedDTO do2Dto(FulfillmentPublishedDO obj);

    List<FulfillmentPublishedDO> dto2Dos(List<FulfillmentPublishedDTO> list);

    List<FulfillmentPublishedDTO> do2Dtos(List<FulfillmentPublishedDO> list);

    FulfillmentPublishedVO do2Vo(FulfillmentPublishedDO obj);

    FulfillmentPublishedVO po2Vo(FulfillmentPublishedPO obj);

    List<FulfillmentPublishedVO> po2Vos(List<FulfillmentPublishedPO> list);

    FulfillmentPublishedPO do2Po(FulfillmentPublishedDO obj);

    FulfillmentPublishedDO po2Do(FulfillmentPublishedPO obj);

    FulfillmentPublishedPO dto2Po(FulfillmentPublishedDTO obj);

    List<FulfillmentPublishedPO> dto2Pos(List<FulfillmentPublishedDTO> obj);

}
