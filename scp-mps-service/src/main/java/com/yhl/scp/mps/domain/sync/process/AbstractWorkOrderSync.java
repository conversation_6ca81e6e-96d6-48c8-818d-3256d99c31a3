package com.yhl.scp.mps.domain.sync.process;

import com.yhl.scp.mps.dispatch.output.RzzMpsAlgorithmOutput;
import com.yhl.scp.mps.domain.sync.IWorkOrderSync;
import com.yhl.scp.mps.domain.sync.model.SyncContext;
import com.yhl.scp.mps.domain.sync.support.SyncSupport;
import com.yhl.scp.sds.basic.order.enums.PlannedStatusEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * <code>AbstractWorkOrderSync</code>
 * <p>
 * AbstractWorkOrderSync：定义制造订单同步流程
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/10 11:11
 */
@Slf4j
public abstract class AbstractWorkOrderSync extends SyncSupport implements IWorkOrderSync {

    //限制改变的制造订单状态
    public static final List<String> keepStatus = Arrays.asList(PlannedStatusEnum.FINISHED.getCode(), PlannedStatusEnum.STARTED.getCode());

    // 新品试制订单类型 不生成demand,supply
    public static final List<String> orderType = Arrays.asList("SZ", "XSB", "SYTS");


    /**
     * 制造订单同步主流程
     *
     * @param output MPS结果输出
     */
    protected void doSync(RzzMpsAlgorithmOutput output) {

        //1.初始化数据传输上下文
        SyncContext syncContext = initContext(output);
        //2.工艺路径选择
        selectOrderRouting(syncContext);
        //3.补充物品信息
        getNewProductStockPoint(syncContext);
        //4.工序同步
        syncOperation(syncContext);
        //5.工序候选资源同步
        syncOperationResource(syncContext);
        //6.工序输入物品同步
        syncOperationInput(syncContext);
        //7.工序输出物品同步
        syncOperationOutput(syncContext);
    }


    /**
     * 工艺路径选择
     * @param syncContext
     */
    protected abstract void selectOrderRouting(SyncContext syncContext);

    /**
     * 工序同步
     * @param syncContext
     */
    protected abstract void syncOperation(SyncContext syncContext);

    /**
     * 工序资源同步
     * @param syncContext
     */
    protected abstract void syncOperationResource(SyncContext syncContext);

    /**
     * 工序输入物品同步
     * @param syncContext
     */
    protected abstract void syncOperationInput(SyncContext syncContext);

    /**
     * 工序输出物品同步
     * @param syncContext
     */
    protected abstract void syncOperationOutput(SyncContext syncContext);
}
