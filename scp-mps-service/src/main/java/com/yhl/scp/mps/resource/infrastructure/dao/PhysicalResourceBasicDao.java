package com.yhl.scp.mps.resource.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mds.extension.resource.infrastructure.po.PhysicalResourcePO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <code>PhysicalResourceDao</code>
 * <p>
 * 物理资源DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-25 17:32:50
 */
public interface PhysicalResourceBasicDao extends BaseDao<PhysicalResourcePO, PhysicalResourceVO> {

    /**
     * 查询所有的资源
     *
     * @return list
     */
    List<PhysicalResourcePO> selectAll();

    /**
     * 查询物理资源id By standardResourceIds
     *
     * @return 返回资源id
     */
    List<PhysicalResourcePO> selectByStandardResourceIds(@Param("standardResourceIds") List<String> standardResourceIds);

    List<PhysicalResourcePO> selectByOrganizationId(@Param("organizationId") String organizationId);

    int deleteByStandardResourceIdIn(@Param("standardResourceIdList") Collection<String> standardResourceIdList);
}