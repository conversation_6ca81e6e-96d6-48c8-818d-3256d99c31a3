package com.yhl.scp.mps.plan.domain.factory;

import com.yhl.scp.mps.plan.domain.entity.MasterPlanIssuedDataDO;
import com.yhl.scp.mps.plan.dto.MasterPlanIssuedDataDTO;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanIssuedDataDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>MasterPlanIssuedDataFactory</code>
 * <p>
 * 主计划发布数据领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 09:40:36
 */
@Component
public class MasterPlanIssuedDataFactory {

    @Resource
    private MasterPlanIssuedDataDao masterPlanIssuedDataDao;

    MasterPlanIssuedDataDO create(MasterPlanIssuedDataDTO dto) {
        // TODO
        return null;
    }

}
