package com.yhl.scp.mps.domain.workbench.model;


import com.yhl.scp.ips.rbac.dto.UserMessageDTO;
import com.yhl.scp.ips.rbac.vo.UserMessageVO;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;


/**
 * <code>WorkBenchContext</code>
 * <p>
 * context
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10 13:36:36
 */
@Data
@Builder
public class WorkBenchContext {

    private String userId;

    private String scenario;

    private List<String> permissionPhysicalResourceCodes;

    private List<UserMessageDTO> userMessageDTOList;

    Map<String, List<UserMessageVO>> messageTypeMap;

}
