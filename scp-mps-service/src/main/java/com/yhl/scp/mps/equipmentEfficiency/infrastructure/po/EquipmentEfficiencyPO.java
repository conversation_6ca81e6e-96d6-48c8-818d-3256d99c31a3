package com.yhl.scp.mps.equipmentEfficiency.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>EquipmentEfficiencyPO</code>
 * <p>
 * 特殊工艺设备效率PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-19 16:03:32
 */
public class EquipmentEfficiencyPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -80317246105105468L;

        /**
     * 公司代码
     */
        private String companyCode;
        /**
     * 公司名称
     */
        private String companyName;
        /**
     * 特殊工艺
     */
        private String specialTechnology;
        /**
     * 工艺类型大类
     */
        private String technologyType;
        /**
     * 设备数量
     */
        private Integer equipmentNumber;
        /**
     * 设备OEE%
     */
        private BigDecimal equipmentOee;
        /**
     * 设备月不可以天数
     */
        private BigDecimal equipmentNotUseDay;
        /**
     * 版本
     */
        private Integer versionValue;

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getSpecialTechnology() {
        return specialTechnology;
    }

    public void setSpecialTechnology(String specialTechnology) {
        this.specialTechnology = specialTechnology;
    }

    public String getTechnologyType() {
        return technologyType;
    }

    public void setTechnologyType(String technologyType) {
        this.technologyType = technologyType;
    }

    public Integer getEquipmentNumber() {
        return equipmentNumber;
    }

    public void setEquipmentNumber(Integer equipmentNumber) {
        this.equipmentNumber = equipmentNumber;
    }

    public BigDecimal getEquipmentOee() {
        return equipmentOee;
    }

    public void setEquipmentOee(BigDecimal equipmentOee) {
        this.equipmentOee = equipmentOee;
    }

    public BigDecimal getEquipmentNotUseDay() {
        return equipmentNotUseDay;
    }

    public void setEquipmentNotUseDay(BigDecimal equipmentNotUseDay) {
        this.equipmentNotUseDay = equipmentNotUseDay;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
