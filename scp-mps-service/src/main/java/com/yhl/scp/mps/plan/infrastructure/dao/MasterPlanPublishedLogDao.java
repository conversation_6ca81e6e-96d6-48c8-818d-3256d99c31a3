package com.yhl.scp.mps.plan.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mps.plan.infrastructure.po.MasterPlanPublishedLogPO;
import com.yhl.scp.mps.plan.vo.MasterPlanPublishedLogVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>MasterPlanPublishedLogDao</code>
 * <p>
 * 主生产计划发布日志表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-03 15:38:10
 */
public interface MasterPlanPublishedLogDao extends BaseDao<MasterPlanPublishedLogPO, MasterPlanPublishedLogVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link MasterPlanPublishedLogVO}
     */
    List<MasterPlanPublishedLogVO> selectVOByParams(@Param("params") Map<String, Object> params);

    String selectNewestLogIdByOperatorId(@Param("publishedOperatorId") String publishedOperatorId);

}
