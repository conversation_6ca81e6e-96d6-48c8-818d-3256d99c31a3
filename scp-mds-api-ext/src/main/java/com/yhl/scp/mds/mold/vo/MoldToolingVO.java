package com.yhl.scp.mds.mold.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MoldToolingVO</code>
 * <p>
 * 模具工装族与工装编号关系VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-11 10:28:45
 */
@ApiModel(value = "模具工装族与工装编号关系VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MoldToolingVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 596262780380185441L;

        /**
     * 标准资源ID
     */
        @ApiModelProperty(value = "标准资源ID")
    @FieldInterpretation(value = "标准资源ID")
    private String standardResourceId;
        /**
     * 物理资源代码
     */
        @ApiModelProperty(value = "物理资源代码")
    @FieldInterpretation(value = "物理资源代码")
    private String physicalResourceCode;
        /**
     * 物理资源名称
     */
        @ApiModelProperty(value = "物理资源名称")
    @FieldInterpretation(value = "物理资源名称")
    private String physicalResourceName;
        /**
     * 资源类别
     */
        @ApiModelProperty(value = "资源类别")
    @FieldInterpretation(value = "资源类别")
    private String resourceCategory;
        /**
     * 资源类型
     */
        @ApiModelProperty(value = "资源类型")
    @FieldInterpretation(value = "资源类型")
    private String resourceType;
        /**
     * 资源门类
     */
        @ApiModelProperty(value = "资源门类")
    @FieldInterpretation(value = "资源门类")
    private String resourceClassification;
        /**
     * 子任务类型
     */
        @ApiModelProperty(value = "子任务类型")
    @FieldInterpretation(value = "子任务类型")
    private String subtaskType;
        /**
     * 堆叠限制类型
     */
        @ApiModelProperty(value = "堆叠限制类型")
    @FieldInterpretation(value = "堆叠限制类型")
    private String assignQuantityType;
        /**
     * 显示顺序
     */
        @ApiModelProperty(value = "显示顺序")
    @FieldInterpretation(value = "显示顺序")
    private Integer displayIndex;
        /**
     * 是否瓶颈资源
     */
        @ApiModelProperty(value = "是否瓶颈资源")
    @FieldInterpretation(value = "是否瓶颈资源")
    private String bottleneck;
        /**
     * 是否无限能力
     */
        @ApiModelProperty(value = "是否无限能力")
    @FieldInterpretation(value = "是否无限能力")
    private String infiniteCapacity;
        /**
     * 工序代码
     */
        @ApiModelProperty(value = "工序代码")
    @FieldInterpretation(value = "工序代码")
    private String sequenceCode;
        /**
     * 是否可变工时
     */
        @ApiModelProperty(value = "是否可变工时")
    @FieldInterpretation(value = "是否可变工时")
    private String variableWorkHours;
        /**
     * 资源量系数
     */
        @ApiModelProperty(value = "资源量系数")
    @FieldInterpretation(value = "资源量系数")
    private String resourceQuantityCoefficient;
        /**
     * 计量单位ID
     */
        @ApiModelProperty(value = "计量单位ID")
    @FieldInterpretation(value = "计量单位ID")
    private String countingUnitId;
        /**
     * 制造效率
     */
        @ApiModelProperty(value = "制造效率")
    @FieldInterpretation(value = "制造效率")
    private BigDecimal productionEfficiency;
        /**
     * 设置效率
     */
        @ApiModelProperty(value = "设置效率")
    @FieldInterpretation(value = "设置效率")
    private BigDecimal setupEfficiency;
        /**
     * 清洗效率
     */
        @ApiModelProperty(value = "清洗效率")
    @FieldInterpretation(value = "清洗效率")
    private BigDecimal cleanupEfficiency;
        /**
     * 设置时间
     */
        @ApiModelProperty(value = "设置时间")
    @FieldInterpretation(value = "设置时间")
    private Integer setupDuration;
        /**
     * 清洗时间
     */
        @ApiModelProperty(value = "清洗时间")
    @FieldInterpretation(value = "清洗时间")
    private Integer cleanupDuration;
        /**
     * 前缓冲时间
     */
        @ApiModelProperty(value = "前缓冲时间")
    @FieldInterpretation(value = "前缓冲时间")
    private Integer bufferTimeBefore;
        /**
     * 后缓冲时间
     */
        @ApiModelProperty(value = "后缓冲时间")
    @FieldInterpretation(value = "后缓冲时间")
    private Integer bufferTimeAfter;
        /**
     * 制造最大中断时间
     */
        @ApiModelProperty(value = "制造最大中断时间")
    @FieldInterpretation(value = "制造最大中断时间")
    private Integer maxProductionSuspendDuration;
        /**
     * 设置最大中断时间
     */
        @ApiModelProperty(value = "设置最大中断时间")
    @FieldInterpretation(value = "设置最大中断时间")
    private Integer maxSetupSuspendDuration;
        /**
     * 清理最大中断时间
     */
        @ApiModelProperty(value = "清理最大中断时间")
    @FieldInterpretation(value = "清理最大中断时间")
    private Integer maxCleanupSuspendDuration;
        /**
     * 生产线
     */
        @ApiModelProperty(value = "生产线")
    @FieldInterpretation(value = "生产线")
    private String productionLine;
        /**
     * 是否严格遵守生产线约束
     */
        @ApiModelProperty(value = "是否严格遵守生产线约束")
    @FieldInterpretation(value = "是否严格遵守生产线约束")
    private String strictProductionLineConstraints;
        @ApiModelProperty(value = "${column.comment}")
    @FieldInterpretation(value = "${column.comment}")
    private String noBufferActionType;
        /**
     * 资源锁定时间
     */
        @ApiModelProperty(value = "资源锁定时间")
    @FieldInterpretation(value = "资源锁定时间")
    private Integer noBufferActionDuration;
        /**
     * 单位制造批量
     */
        @ApiModelProperty(value = "单位制造批量")
    @FieldInterpretation(value = "单位制造批量")
    private Integer lotSize;
        /**
     * 最大制造批量
     */
        @ApiModelProperty(value = "最大制造批量")
    @FieldInterpretation(value = "最大制造批量")
    private Integer maxLotSize;
        /**
     * 制造时刻尾数调整单位
     */
        @ApiModelProperty(value = "制造时刻尾数调整单位")
    @FieldInterpretation(value = "制造时刻尾数调整单位")
    private String productionDateLastNumChangeUnit;
        /**
     * 制造时间尾数调整单位
     */
        @ApiModelProperty(value = "制造时间尾数调整单位")
    @FieldInterpretation(value = "制造时间尾数调整单位")
    private String productionTimeLastNumChangeUnit;
        /**
     * 制造时间取值方式
     */
        @ApiModelProperty(value = "制造时间取值方式")
    @FieldInterpretation(value = "制造时间取值方式")
    private String productionDurationLogic;
        /**
     * 设置和清洗时间取值方式
     */
        @ApiModelProperty(value = "设置和清洗时间取值方式")
    @FieldInterpretation(value = "设置和清洗时间取值方式")
    private String setupAndCleanupDurationLogic;
        /**
     * 动态设置和清洗时间取值方式
     */
        @ApiModelProperty(value = "动态设置和清洗时间取值方式")
    @FieldInterpretation(value = "动态设置和清洗时间取值方式")
    private String dynamicSetupAndCleanupDurationLogic;
        /**
     * 切换间取值切换
     */
        @ApiModelProperty(value = "切换间取值切换")
    @FieldInterpretation(value = "切换间取值切换")
    private String changeoverDurationLogic;
        /**
     * 动态切换时间取值方式
     */
        @ApiModelProperty(value = "动态切换时间取值方式")
    @FieldInterpretation(value = "动态切换时间取值方式")
    private String dynamicChangeoverDurationLogic;
        /**
     * 生效时间
     */
        @ApiModelProperty(value = "生效时间")
    @FieldInterpretation(value = "生效时间")
    private Date effectiveTime;
        /**
     * 失效时间
     */
        @ApiModelProperty(value = "失效时间")
    @FieldInterpretation(value = "失效时间")
    private Date expiryTime;
        /**
     * 生产计划员
     */
        @ApiModelProperty(value = "生产计划员")
    @FieldInterpretation(value = "生产计划员")
    private String productionPlanner;

    @Override
    public void clean() {

    }

}
