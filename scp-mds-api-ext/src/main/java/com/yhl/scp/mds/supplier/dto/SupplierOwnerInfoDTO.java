package com.yhl.scp.mds.supplier.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>SupplierOwnerInfoDTO</code>
 * <p>
 * DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-02 16:21:17
 */
@ApiModel(value = "DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SupplierOwnerInfoDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -60429536567180049L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 物品ID
     */
    @ApiModelProperty(value = "物品ID")
    private String productId;
    /**
     * 物品代码
     */
    @ApiModelProperty(value = "物品代码")
    private String productCode;
    /**
     * 是否寄售(N:否/Y:是)
     */
    @ApiModelProperty(value = "是否寄售(N:否/Y:是)")
    private String ownerType;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdateDate;
    /**
     * 合格供应商 ID
     */
    @ApiModelProperty(value = "合格供应商 ID")
    private String supplierId;
    /**
     * 供应商代码
     */
    @ApiModelProperty(value = "供应商代码")
    private String supplierCode;
    /**
     * 计划区域
     */
    @ApiModelProperty(value = "计划区域")
    private String plantId;
    /**
     * 库存点说明
     */
    @ApiModelProperty(value = "库存点说明")
    private String locatorDesc;
    /**
     * 库存点编码
     */
    @ApiModelProperty(value = "库存点编码")
    private String stockPointCode;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer versionValue;

}
