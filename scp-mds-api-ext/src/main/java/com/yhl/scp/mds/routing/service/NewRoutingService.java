package com.yhl.scp.mds.routing.service;

import java.util.List;
import java.util.Map;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mds.bom.vo.ProductRiskLevelVO;
import com.yhl.scp.mds.routing.dto.NewRoutingDTO;
import com.yhl.scp.mds.routing.vo.NewRoutingVO;
import com.yhl.scp.mds.routing.vo.NewRoutingValidateVO;

import javax.servlet.http.HttpServletResponse;

/**
 * <code>NewRoutingService</code>
 * <p>
 * 新-生产路径应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20 09:26:24
 */
public interface NewRoutingService extends BaseService<NewRoutingDTO, NewRoutingVO> {

    /**
     * 查询所有
     *
     * @return list {@link NewRoutingVO}
     */
    List<NewRoutingVO> selectAll();

    /**
     * 物品工艺路径转产品工艺路径
	 *
     * @param routingSequenceIds 路径步骤顺序ID集合
     */
    void doTransitionRouting(List<String> routingSequenceIds, String scenario);

    void doCreateBatchWithPrimaryKey(List<NewRoutingDTO> list);

    void doUpdateBatchSelective(List<NewRoutingDTO> list);

    /**
	 * 删除对应的产品工艺路径数据
	 *
	 * @param deleteProductRoutingList 		待删除产品工艺路径集合
	 * @param deleteBomVersionList 			待删除BOM版本集合
	 * @param deleteBomLineList 			待删除BOM行集合
	 * @param deleteProductRoutingStepList 	待删除工艺路径步骤集合
     */
	void doDeleteRoutingDate(List<String> deleteProductRoutingList, List<String> deleteBomVersionList,
			List<String> deleteBomLineList, List<String> deleteProductRoutingStepList);

	/**
	 * 获取产品对应的BOM风险等级
	 *
	 * @param productCodeList 成品编码集合
	 * @return java.util.Map<java.lang.String,java.util.List<com.yhl.scp.mds.bom.vo.ProductRiskLevelVO>>
	 */
	Map<String, List<ProductRiskLevelVO>> selectProductRiskLevelMap(List<String> productCodeList);

	Map<String, List<NewRoutingValidateVO>> validateRoutingList();

	void downloadErrorData(HttpServletResponse response);

	void checkEnableFlagByPoCategory(String scenario);

	void updateEnableForExpiryTime();

	List<String> selectRoutingIdsByProductCode(String productCodeList);

	List<String> selectEnabledProductCodes(List<String> productCodes);

}