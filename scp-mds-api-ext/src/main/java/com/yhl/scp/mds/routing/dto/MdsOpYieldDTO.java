package com.yhl.scp.mds.routing.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MdsOpYieldDTO</code>
 * <p>
 * mes工序成品率接口同步中间表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-14 11:44:56
 */
@ApiModel(value = "mes工序成品率接口同步中间表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MdsOpYieldDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -84082848999704195L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 物品id
     */
    @ApiModelProperty(value = "物品id")
    private String productId;
    /**
     * 物品编码
     */
    @ApiModelProperty(value = "物品编码")
    private String productCode;
    /**
     * 物品规格
     */
    @ApiModelProperty(value = "物品规格")
    private String productSpec;
    /**
     * 公司代码
     */
    @ApiModelProperty(value = "公司代码")
    private String planArea;
    /**
     * 库存点ID
     */
    @ApiModelProperty(value = "库存点ID")
    private String stockPointId;
    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    private String stockPointCode;
    /**
     * 年月
     */
    @ApiModelProperty(value = "年月")
    private String yearMm;
    /**
     * 成品率
     */
    @ApiModelProperty(value = "成品率")
    private String opYield;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdateDate;
    /**
     * 工序
     */
    @ApiModelProperty(value = "工序")
    private String opProcess;
    @ApiModelProperty(value = "${column.comment}")
    private String kid;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer versionValue;

}
