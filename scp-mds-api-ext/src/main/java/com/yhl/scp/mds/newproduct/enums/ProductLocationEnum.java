package com.yhl.scp.mds.newproduct.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <p>
 * 装车位置枚举
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-05-16 18:05:17
 */
public enum ProductLocationEnum implements CommonEnum {

    /**
     * QD
     */
    QD("前挡", "前挡"),

    /**
     * HD
     */
    HD("后挡", "后挡");

    private String code;

    private String desc;

    ProductLocationEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    void setCode(String code) {
        this.code = code;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

}