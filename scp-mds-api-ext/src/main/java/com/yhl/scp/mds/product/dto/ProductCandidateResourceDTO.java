package com.yhl.scp.mds.product.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <code>ProductCandidateResourceDTO</code>
 * <p>
 * 产品资源生产关系表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-15 19:53:32
 */
@ApiModel(value = "产品资源生产关系表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProductCandidateResourceDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -4412308482540237219L;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    private String stockPointCode;
    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    private String stockPointName;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    private String productCode;
    /**
     * 零件名称
     */
    @ApiModelProperty(value = "零件名称")
    private String partName;
    /**
     * 工序代码
     */
    @ApiModelProperty(value = "工序代码")
    private String operationCode;
    /**
     * 工序名称
     */
    @ApiModelProperty(value = "工序名称")
    private String operationName;
    /**
     * 资源代码
     */
    @ApiModelProperty(value = "资源代码")
    private String resourceCode;
    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    private String resourceName;
    /**
     * 资源类型
     */
    @ApiModelProperty(value = "资源类型")
    private String resourceType;
    /**
     * 生产线组
     */
    @ApiModelProperty(value = "生产线组")
    private String lineGroup;
    /**
     * 节拍（秒/片）
     */
    @ApiModelProperty(value = "节拍（秒/片）")
    private Double beat;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;

    private List<String> productCodeList;
}
